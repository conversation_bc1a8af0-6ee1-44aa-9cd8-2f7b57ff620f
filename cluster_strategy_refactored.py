"""
Refactored Cluster Strategy
- Clusters based on walls and expiry distance
- Uses overnight E-mini drift
- Clean, focused implementation
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_pdf import PdfPages

sys.path.append('src')

from src.dataloader import OptionsDataLoader
from src.config import Config
from src.emini_overnight_analyzer import EminiOvernightAnalyzer
from src.greeks_analytics_engine import GreeksAnalyticsEngine, ClusterGreeksAnalyzer, AdvancedSignalGenerator
from src.constants import (
    START_YEAR, BACKTEST_END_YEAR, STRIKE_MULTIPLE, TARGET_EXPIRY_DAYS,
    MAX_HOLD_DAYS, DRIFT_THRESHOLD, WALL_STRENGTH_THRESHOLD, VOLUME_PERCENTILE,
    GAMMA_MULTIPLIER, DELTA_MULTIPLIER, BAND_WIDTH, INITIAL_CAPITAL,
    SPX_MULTIPLIER, MAX_CONTRACTS, MIN_CONTRACTS, MAX_RISK_PER_TRADE
)

class ClusterStrategy:
    """
    Refactored cluster strategy based on:
    1. Option walls (call/put concentrations)
    2. Expiry distance (time to expiration effect)
    3. Greeks weighting (gamma and delta impact)
    4. Overnight E-mini drift (market sentiment)
    5. Proper options trading with strike selection and 3-day holding
    """
    
    def __init__(self, start_year=START_YEAR, start_date=None, end_date=None):
        self.name = "Cluster Strategy"
        self.start_year = start_year  # Configurable start year for data loading
        self.start_date = start_date  # Actual start date for E-mini sync
        self.end_date = end_date      # Actual end date for E-mini sync

        # Strategy parameters (UPDATED FOR DRAWDOWN REDUCTION) - Using constants
        self.params = {
            'max_hold_days': 1,                               # Max hold days: 1 day (reduced from 2)
            'target_expiry_days': 14,                         # Target days to expiry (reduced from 60)
            'strike_multiple': STRIKE_MULTIPLE,               # Strike must be multiple of 25
            'base_position_size': 1,                          # Base contract size (reduced from 5)
            'max_position_size': 2,                           # Max contracts (reduced from 10)
            'volume_percentile': 80,                          # Volume threshold (increased from 70%)
            'wall_strength_threshold': 5.0,                  # Wall strength threshold (increased from 2.0)
            'drift_threshold': 0.002,                        # 0.2% drift threshold (reduced from 0.4%)
        }

        # Initialize E-mini analyzer with synchronized dates
        self.emini_analyzer = self._init_emini_analyzer()

        # Initialize Greeks analytics engine
        self.greeks_engine = GreeksAnalyticsEngine()
        self.cluster_analyzer = ClusterGreeksAnalyzer(self.greeks_engine)
        self.signal_generator = AdvancedSignalGenerator(self.cluster_analyzer)
    
    def _init_emini_analyzer(self) -> Optional[EminiOvernightAnalyzer]:
        """Initialize E-mini overnight analyzer with synchronized dates"""
        try:
            emini_path = "/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities/ES_full_5min_continuous_ratio_adjusted.txt"
            analyzer = EminiOvernightAnalyzer(emini_path)

            # Use synchronized dates if available, otherwise fall back to defaults
            start_date_str = self.start_date.strftime('%Y-%m-%d') if self.start_date else '2022-01-01'
            end_date_str = self.end_date.strftime('%Y-%m-%d') if self.end_date else '2024-12-31'

            print(f"🔄 Initializing E-mini analyzer: {start_date_str} to {end_date_str}")
            analyzer.identify_overnight_sessions(start_date=start_date_str, end_date=end_date_str)
            print("✅ E-mini overnight analyzer initialized with synchronized dates")
            return analyzer
        except Exception as e:
            print(f"⚠️  E-mini analyzer not available: {e}")
            return None
    
    def calculate_expiry_weight(self, options_data: pd.DataFrame, current_date: pd.Timestamp) -> pd.DataFrame:
        """
        Calculate expiry-based weights for clustering
        Closer expiries have higher impact on current price action
        """
        options_data = options_data.copy()
        
        # Calculate days to expiration
        options_data['dte'] = (options_data['expiration'] - current_date).dt.days
        
        # Weight function: closer expiries get higher weight
        # Weight = 1 / (1 + dte/30) - gives more weight to near-term options
        options_data['expiry_weight'] = 1.0 / (1.0 + options_data['dte'] / 30.0)
        
        # Cap minimum weight at 0.1 for very far expiries
        options_data['expiry_weight'] = np.maximum(options_data['expiry_weight'], 0.1)
        
        return options_data

    def calculate_greeks_weights(self, options_data: pd.DataFrame, current_price: float) -> pd.DataFrame:
        """
        Calculate gamma and delta-based weights for clustering
        Higher gamma = more price sensitivity (higher weight)
        Higher absolute delta = more directional exposure (higher weight)
        """
        options_data = options_data.copy()

        # Ensure we have gamma and delta columns
        if 'gamma' not in options_data.columns:
            # Estimate gamma for ATM options (simplified)
            options_data['gamma'] = np.where(
                abs(options_data['strike'] - current_price) / current_price < 0.05,  # Within 5% of ATM
                0.01,  # High gamma for ATM
                0.001  # Low gamma for OTM
            )

        if 'delta' not in options_data.columns:
            # Estimate delta based on moneyness and option type
            moneyness = options_data['strike'] / current_price

            # Simplified delta estimation
            call_delta = np.where(
                options_data['option_type'].str.upper() == 'C',
                np.clip(1.5 - moneyness, 0.01, 0.99),  # Calls: higher strike = lower delta
                0
            )

            put_delta = np.where(
                options_data['option_type'].str.upper() == 'P',
                np.clip(moneyness - 0.5, -0.99, -0.01),  # Puts: higher strike = more negative delta
                0
            )

            options_data['delta'] = call_delta + put_delta

        # Calculate gamma weight (normalize gamma to 0-2 range)
        max_gamma = options_data['gamma'].quantile(0.95)  # Use 95th percentile to avoid outliers
        options_data['gamma_weight'] = np.clip(
            1.0 + (options_data['gamma'] / max_gamma),
            0.1, 2.0
        )

        # Calculate delta weight (absolute delta shows directional exposure)
        options_data['abs_delta'] = abs(options_data['delta'])
        max_abs_delta = options_data['abs_delta'].quantile(0.95)
        options_data['delta_weight'] = np.clip(
            0.5 + (options_data['abs_delta'] / max_abs_delta),
            0.1, 1.5
        )

        return options_data

    def detect_option_walls(self, options_data: pd.DataFrame, current_price: float) -> Dict:
        """
        Detect option walls weighted by expiry distance, gamma, and delta
        """
        # Scale volume to realistic levels
        options_data = options_data.copy()
        options_data['volume'] = (options_data['volume'] * 1000).astype(int)
        options_data['volume'] = np.maximum(options_data['volume'], 10)

        # Add distance-based volume adjustment
        options_data['distance_from_atm'] = abs(options_data['strike'] - current_price) / current_price
        atm_multiplier = np.exp(-2 * options_data['distance_from_atm'])
        options_data['volume'] = (options_data['volume'] * atm_multiplier).astype(int)

        # Calculate expiry weights
        options_data = self.calculate_expiry_weight(options_data, pd.Timestamp.now())

        # Calculate Greeks-based weights
        options_data = self.calculate_greeks_weights(options_data, current_price)

        # Apply comprehensive weighting: expiry × gamma × delta × volume
        options_data['weighted_volume'] = (options_data['volume'] *
                                         options_data['expiry_weight'] *
                                         options_data['gamma_weight'] *
                                         options_data['delta_weight'])
        
        # Volume threshold
        volume_threshold = np.percentile(options_data['weighted_volume'], self.params['volume_percentile'])
        
        # Filter significant options
        significant_options = options_data[options_data['weighted_volume'] >= volume_threshold].copy()
        
        if significant_options.empty:
            return self._empty_wall_data()
        
        # Separate calls and puts
        calls = significant_options[significant_options['option_type'].str.upper() == 'C'].copy()
        puts = significant_options[significant_options['option_type'].str.upper() == 'P'].copy()
        
        # Define clustering bands from current price
        band_width = BAND_WIDTH
        upper_band = current_price * (1 + band_width)
        lower_band = current_price * (1 - band_width)
        
        # Find call walls (above current price)
        call_walls = self._find_walls(
            calls[(calls['strike'] > current_price) & (calls['strike'] <= upper_band)],
            volume_threshold
        )
        
        # Find put walls (below current price)  
        put_walls = self._find_walls(
            puts[(puts['strike'] < current_price) & (puts['strike'] >= lower_band)],
            volume_threshold
        )
        
        # Calculate wall strengths (sum of all wall strengths)
        call_strength = sum(wall['strength'] for wall in call_walls)
        put_strength = sum(wall['strength'] for wall in put_walls)
        
        # Calculate delta exposure using actual delta values
        call_delta_exposure = 0
        for wall in call_walls:
            # Get actual delta for this strike
            wall_options = significant_options[
                (significant_options['strike'] == wall['strike']) &
                (significant_options['option_type'].str.upper() == 'C')
            ]
            if not wall_options.empty:
                avg_delta = wall_options['delta'].mean()
                call_delta_exposure += wall['volume'] * avg_delta

        put_delta_exposure = 0
        for wall in put_walls:
            # Get actual delta for this strike
            wall_options = significant_options[
                (significant_options['strike'] == wall['strike']) &
                (significant_options['option_type'].str.upper() == 'P')
            ]
            if not wall_options.empty:
                avg_delta = wall_options['delta'].mean()
                put_delta_exposure += wall['volume'] * avg_delta
        
        net_delta_exposure = call_delta_exposure + put_delta_exposure
        total_exposure = abs(call_delta_exposure) + abs(put_delta_exposure)
        delta_imbalance = net_delta_exposure / total_exposure if total_exposure > 0 else 0
        
        return {
            'call_walls': call_walls,
            'put_walls': put_walls,
            'call_wall_count': len(call_walls),
            'put_wall_count': len(put_walls),
            'call_wall_strength': call_strength,
            'put_wall_strength': put_strength,
            'dominant_wall': 'CALL_WALL' if call_strength > put_strength else 'PUT_WALL',
            'delta_imbalance': delta_imbalance,
            'net_delta_exposure': net_delta_exposure,
            'volume_threshold': volume_threshold,
            'band_width': band_width
        }
    
    def _find_walls(self, options: pd.DataFrame, volume_threshold: float) -> List[Dict]:
        """Find option walls from filtered options"""
        if options.empty:
            return []
        
        walls = []
        strike_volumes = options.groupby('strike')['weighted_volume'].sum().reset_index()
        
        for _, row in strike_volumes.iterrows():
            wall_strength = row['weighted_volume'] / volume_threshold
            if wall_strength >= self.params['wall_strength_threshold']:
                walls.append({
                    'strike': row['strike'],
                    'volume': row['weighted_volume'],
                    'strength': wall_strength
                })
        
        return sorted(walls, key=lambda x: x['strength'], reverse=True)
    
    def _empty_wall_data(self) -> Dict:
        """Return empty wall data structure"""
        return {
            'call_walls': [],
            'put_walls': [],
            'call_wall_count': 0,
            'put_wall_count': 0,
            'call_wall_strength': 0,
            'put_wall_strength': 0,
            'dominant_wall': 'NEUTRAL',
            'delta_imbalance': 0,
            'net_delta_exposure': 0,
            'volume_threshold': 0,
            'band_width': BAND_WIDTH
        }
    
    def get_overnight_drift(self, date: pd.Timestamp) -> float:
        """Get overnight E-mini drift for the given date"""
        if not self.emini_analyzer:
            return 0.0

        try:
            date_str = date.strftime('%Y-%m-%d')
            return self.emini_analyzer.get_overnight_drift_for_date(date_str)
        except:
            return 0.0


    
    def generate_signal(self, wall_data: Dict, drift: float, current_price: float) -> Dict:
        """
        Generate trading signal based on walls and drift

        FIXED LOGIC - Directional Options Trading:
        1. Put walls (support) + upward bias = BULLISH → BUY calls
        2. Call walls (resistance) + downward bias = BEARISH → BUY puts
        3. Strong drift breaking walls = momentum continuation
        4. Always BUY options in the direction of expected move
        """
        
        signal_type = 'NEUTRAL'
        signal_strength = 0.0
        rationale = "No clear signal"
        position_size = 0
        
        # Get wall dominance
        call_strength = wall_data['call_wall_strength']
        put_strength = wall_data['put_wall_strength']
        delta_imbalance = wall_data['delta_imbalance']
        
        # Minimum wall strength required - USE CONSTANT FOR CONSISTENCY
        min_wall_strength = WALL_STRENGTH_THRESHOLD
        
        if call_strength < min_wall_strength and put_strength < min_wall_strength:
            return {
                'signal_type': signal_type,
                'signal_strength': signal_strength,
                'position_size': position_size,
                'rationale': "Insufficient wall strength"
            }
        
        # FIXED Signal generation logic - Directional trading
        if put_strength > call_strength:
            # Put wall dominance = Support level
            if drift >= 0:  # No downward drift or upward drift
                # Put walls acting as support + no selling pressure = BULLISH
                signal_type = 'BULLISH'
                signal_strength = min(put_strength / 10.0, 1.0)
                rationale = f"BULLISH: Put walls support ({put_strength:.1f}) + upward bias ({drift*100:.2f}%)"
            elif drift < -self.params['drift_threshold']:
                # Strong downward drift breaking put support = BEARISH
                signal_type = 'BEARISH'
                signal_strength = min((put_strength + abs(drift)*100) / 15.0, 1.0)
                rationale = f"BEARISH: Breaking put support + strong down drift ({drift*100:.2f}%)"

        else:
            # Call wall dominance = Resistance level
            if drift <= 0:  # No upward drift or downward drift
                # Call walls acting as resistance + no buying pressure = BEARISH
                signal_type = 'BEARISH'
                signal_strength = min(call_strength / 10.0, 1.0)
                rationale = f"BEARISH: Call walls resistance ({call_strength:.1f}) + downward bias ({drift*100:.2f}%)"
            elif drift > self.params['drift_threshold']:
                # Strong upward drift breaking call resistance = BULLISH
                signal_type = 'BULLISH'
                signal_strength = min((call_strength + abs(drift)*100) / 15.0, 1.0)
                rationale = f"BULLISH: Breaking call resistance + strong up drift ({drift*100:.2f}%)"
        
        # Calculate position size based on signal strength
        if signal_strength > 0:
            position_size = max(
                self.params['base_position_size'],
                int(signal_strength * self.params['max_position_size'])
            )
        
        return {
            'signal_type': signal_type,
            'signal_strength': signal_strength,
            'position_size': position_size,
            'rationale': rationale
        }

    def generate_enhanced_signal(self, options_data: pd.DataFrame, current_price: float,
                               current_date: pd.Timestamp, drift: float) -> Dict:
        """
        Generate enhanced trading signal using Greeks analytics engine

        Uses fresh Greeks calculations and dynamic thresholds for:
        - Delta/Gamma weighted wall detection
        - Vanna convergence analysis
        - Charm decay signals
        - Vomma volatility signals
        - Dynamic position sizing
        """

        # Use the advanced signal generator
        signal_data = self.signal_generator.generate_enhanced_signal(
            options_data, current_price, current_date, drift
        )

        # Calculate position size using Greeks-based approach
        if signal_data['signal_type'] != 'NEUTRAL' and signal_data['signal_strength'] > 0.3:
            # Get a representative option price for position sizing
            option_price = self._estimate_option_price(options_data, current_price, signal_data['signal_type'])

            # Create signal strength dict for position sizing
            strength_components = {
                'total_strength': signal_data['signal_strength'],
                'wall_component': 0.5,  # Default values
                'drift_component': 0.3,
                'vanna_component': signal_data['wall_data'].get('vanna_convergence', 0),
                'charm_component': signal_data['wall_data'].get('charm_decay_signal', 0),
                'vomma_component': signal_data['wall_data'].get('vomma_volatility_signal', 0)
            }

            position_info = self.cluster_analyzer.calculate_dynamic_position_size(
                strength_components,
                100000,  # Base capital - will be updated in backtest
                option_price
            )

            signal_data['position_size'] = position_info['contracts']
            signal_data['position_info'] = position_info
        else:
            signal_data['position_size'] = 0
            signal_data['position_info'] = None

        return signal_data

    def _estimate_option_price(self, options_data: pd.DataFrame, current_price: float,
                             signal_type: str) -> float:
        """
        Estimate option price for position sizing

        Args:
            options_data: Options data for the day
            current_price: Current underlying price
            signal_type: Signal direction

        Returns:
            Estimated option price
        """
        try:
            # Determine target strike based on signal type
            if signal_type == 'BULLISH':
                target_strike = ((int(current_price) // STRIKE_MULTIPLE) + 1) * STRIKE_MULTIPLE
                option_type = 'c'
            else:
                target_strike = (int(current_price) // STRIKE_MULTIPLE) * STRIKE_MULTIPLE
                option_type = 'p'

            # Find options near target strike and expiry
            target_options = options_data[
                (options_data['strike'] == target_strike) &
                (options_data['option_type'].str.lower() == option_type)
            ]

            if not target_options.empty:
                # Use market price if available
                price = target_options['Last Trade Price'].iloc[0]
                if price > 0:
                    return price

                # Fallback to bid-ask midpoint
                bid = target_options['bid'].iloc[0]
                ask = target_options['ask'].iloc[0]
                if bid > 0 and ask > 0:
                    return (bid + ask) / 2

            # Default estimate based on distance from current price
            distance = abs(target_strike - current_price)
            return max(50, 100 - distance)  # Simple estimate

        except Exception:
            return 75  # Default option price estimate

    def find_option_strike(self, signal_type: str, current_price: float, signal_date: pd.Timestamp, options_data: pd.DataFrame) -> Dict:
        """
        Find the appropriate option strike for trading
        Bear: strike multiple below current price, TARGET_EXPIRY_DAYS expiry
        Bull: strike multiple above current price, TARGET_EXPIRY_DAYS expiry
        """
        try:
            # Skip debug output to reduce logging

            # Check if we have the required columns (use actual column names)
            required_cols = ['date', 'expiration', 'strike', 'option_type', 'bid', 'ask', 'volume']
            missing_cols = [col for col in required_cols if col not in options_data.columns]

            if missing_cols:
                print(f"Error: Missing columns: {missing_cols}")
                return None

            # Target expiry: TARGET_EXPIRY_DAYS from signal date
            target_expiry = signal_date + pd.Timedelta(days=TARGET_EXPIRY_DAYS)

            # Filter options for the signal date
            day_options = options_data[options_data['date'] == signal_date].copy()

            if day_options.empty:
                print(f"No options data for date {signal_date}")
                return None

            # Find closest expiry to 30 days (use 'expiration' column)
            day_options['days_to_expiry'] = (day_options['expiration'] - signal_date).dt.days
            day_options = day_options[day_options['days_to_expiry'] > 0]  # Only future expiries

            if day_options.empty:
                print(f"No future expiries found for date {signal_date}")
                return None

            # Find expiry closest to TARGET_EXPIRY_DAYS
            closest_expiry_days = day_options['days_to_expiry'].iloc[(day_options['days_to_expiry'] - TARGET_EXPIRY_DAYS).abs().argsort()[:1]].iloc[0]
            closest_expiry = signal_date + pd.Timedelta(days=closest_expiry_days)

            # Filter for that expiry
            expiry_options = day_options[day_options['expiration'] == closest_expiry].copy()

            if expiry_options.empty:
                print(f"No options found for closest expiry {closest_expiry}")
                return None

            # Find appropriate strike based on signal type - FIXED LOGIC
            if signal_type == 'BULLISH':  # Bullish - BUY calls (profit from upward moves)
                target_strike = ((int(current_price) // STRIKE_MULTIPLE) + 1) * STRIKE_MULTIPLE  # Next multiple above
                option_type = 'c'  # Call for bullish (BUY call)
            elif signal_type == 'BEARISH':  # Bearish - BUY puts (profit from downward moves)
                target_strike = (int(current_price) // STRIKE_MULTIPLE) * STRIKE_MULTIPLE  # Multiple below
                option_type = 'p'  # Put for bearish (BUY put)
            else:
                print(f"Unknown signal type: {signal_type}")
                return None

            # Filter for the option type (use lowercase to match data)
            type_options = expiry_options[expiry_options['option_type'].str.lower() == option_type.lower()].copy()

            if type_options.empty:
                print(f"No {option_type} options found for expiry {closest_expiry}")
                return None

            # Find the exact strike or closest available
            if target_strike in type_options['strike'].values:
                selected_option = type_options[type_options['strike'] == target_strike].iloc[0]
            else:
                # Find closest strike
                closest_idx = (type_options['strike'] - target_strike).abs().argsort()[:1]
                selected_option = type_options.iloc[closest_idx[0]]

            return {
                'strike': selected_option['strike'],
                'expiry': selected_option['expiration'],  # Use 'expiration' column
                'option_type': selected_option['option_type'],
                'bid': selected_option['bid'],
                'ask': selected_option['ask'],
                'volume': selected_option['volume'],
                'days_to_expiry': closest_expiry_days,
                'entry_price': (selected_option['bid'] + selected_option['ask']) / 2  # Mid price
            }

        except Exception as e:
            # Log detailed error information for debugging
            error_msg = f"Error finding option strike: {e}"
            print(error_msg)

            # Write detailed error to log file
            self._log_option_error(signal_date, current_price, signal_type, e)

            return None

    def _log_option_error(self, date: pd.Timestamp, spx_price: float, signal_type: str, error: Exception):
        """Log detailed option finding errors for analysis"""
        import os

        # Create error log directory
        os.makedirs('reports', exist_ok=True)

        # Prepare error details
        error_details = {
            'timestamp': pd.Timestamp.now(),
            'date': date,
            'spx_price': spx_price,
            'signal_type': signal_type,
            'target_expiry_days': self.params['target_expiry_days'],
            'strike_multiple': self.params['strike_multiple'],
            'error': str(error)
        }

        # Calculate target strike for logging
        if signal_type == 'BULLISH':  # Bullish - BUY calls above current price
            target_strike = ((int(spx_price) // STRIKE_MULTIPLE) + 1) * STRIKE_MULTIPLE
            option_type = 'call'
        elif signal_type == 'BEARISH':  # Bearish - BUY puts below current price
            target_strike = (int(spx_price) // STRIKE_MULTIPLE) * STRIKE_MULTIPLE
            option_type = 'put'
        else:
            target_strike = int(spx_price)
            option_type = 'unknown'

        error_details['target_strike'] = target_strike
        error_details['target_option_type'] = option_type

        # Write to error log
        error_log_path = 'reports/option_errors.log'
        with open(error_log_path, 'a') as f:
            f.write(f"{error_details['timestamp']}: ")
            f.write(f"Date={date.strftime('%Y-%m-%d')}, ")
            f.write(f"SPX=${spx_price:.2f}, ")
            f.write(f"Signal={signal_type}, ")
            f.write(f"Target={option_type} ${target_strike}, ")
            f.write(f"Error={str(error)}\n")

        # Also create CSV for easier analysis
        error_csv_path = 'reports/option_errors.csv'
        error_df = pd.DataFrame([error_details])

        # Append to existing CSV or create new one
        if os.path.exists(error_csv_path):
            existing_df = pd.read_csv(error_csv_path)
            combined_df = pd.concat([existing_df, error_df], ignore_index=True)
            combined_df.to_csv(error_csv_path, index=False)
        else:
            error_df.to_csv(error_csv_path, index=False)


def load_all_spx_options_data(start_year=START_YEAR):
    """Load and combine all SPX options data from optionhistory/{year}_{quarter}_option_chain/spx_complete_{year}_{quarter}.csv"""

    print(f"🔍 SEARCHING FOR SPX OPTIONS DATA (from {start_year})")
    print("=" * 50)

    import os
    import glob

    # Search for optionhistory directories (note: singular, not plural)
    base_paths = ['optionhistory', '../optionhistory']
    all_files = []

    for base_path in base_paths:
        if os.path.exists(base_path):
            print(f"📁 Found optionhistory directory: {base_path}")

            # Look for subdirectories matching {year}_{quarter}_option_chain pattern
            subdirs = [d for d in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, d))]

            for subdir in subdirs:
                if '_option_chain' in subdir:
                    print(f"   📂 Found option chain directory: {subdir}")

                    # Extract year and quarter from directory name
                    # Expected format: 2022_q1_option_chain, 2022_q2_option_chain, etc.
                    parts = subdir.split('_')
                    if len(parts) >= 2:
                        year = parts[0]
                        quarter = parts[1]  # This will be 'q1', 'q2', etc.

                        # Filter by start year
                        try:
                            year_int = int(year)
                            if year_int < start_year:
                                continue  # Skip years before start_year
                        except ValueError:
                            continue  # Skip if year is not a valid integer

                        # Look for spx_complete_{year}_{quarter}.csv
                        expected_file = f"spx_complete_{year}_{quarter}.csv"
                        full_path = os.path.join(base_path, subdir, expected_file)

                        if os.path.exists(full_path):
                            all_files.append(full_path)
                            print(f"      ✅ Found: {expected_file}")
                        else:
                            print(f"      ❌ Missing: {expected_file}")

    if not all_files:
        print("⚠️ No SPX complete files found in optionhistory structure")
        print("   Expected structure: optionhistory/{year}_{quarter}_option_chain/spx_complete_{year}_{quarter}.csv")
        print("   Searched in:", base_paths)
        return None

    print(f"📁 Found {len(all_files)} SPX complete files:")
    for file in all_files:
        print(f"   📄 {file}")

    # Load and combine all files
    combined_data = []
    total_records = 0

    for file_path in all_files:
        try:
            print(f"📊 Loading {os.path.basename(file_path)}...")

            # Load CSV file (should have headers)
            df = pd.read_csv(file_path)

            # Standardize column names based on actual optionhistory file structure
            column_mapping = {
                'Date': 'date',
                'Strike': 'strike',
                'Expiry Date': 'expiration',
                'Call/Put': 'option_type',
                'Bid Price': 'bid',
                'Ask Price': 'ask',
                'Volume': 'volume',
                'Open Interest': 'open_interest',
                'Delta': 'delta',
                'Gamma': 'gamma',
                'Vega': 'vega',
                'Theta': 'theta',
                'Rho': 'rho',
                'spx_open': 'underlying_open',
                'spx_high': 'underlying_high',
                'spx_low': 'underlying_low',
                'spx_close': 'underlying_close'
            }

            # Apply column mapping
            df = df.rename(columns=column_mapping)

            # Check if we have required columns after mapping
            required_cols = ['date', 'strike', 'expiration', 'option_type', 'volume']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                print(f"      ❌ Missing required columns: {missing_cols}")
                continue

            # Parse dates
            df['date'] = pd.to_datetime(df['date'], errors='coerce')
            df['expiration'] = pd.to_datetime(df['expiration'], errors='coerce')

            # Remove rows with invalid data
            df = df.dropna(subset=['date', 'expiration'])
            df = df[pd.to_numeric(df['strike'], errors='coerce').notna()]
            df['strike'] = pd.to_numeric(df['strike'], errors='coerce')

            # Filter for reasonable date range (2012-2030 to include all historical data)
            df = df[(df['date'] >= '2012-01-01') & (df['date'] <= '2030-12-31')]
            df = df[(df['expiration'] >= '2012-01-01') & (df['expiration'] <= '2030-12-31')]

            # Add source file info
            df['source_file'] = os.path.basename(file_path)

            if len(df) > 0:
                combined_data.append(df)
                total_records += len(df)
                print(f"   ✅ Loaded {len(df):,} records")
            else:
                print(f"   ⚠️ No valid records found")

        except Exception as e:
            print(f"   ❌ Error loading {file_path}: {e}")

    if not combined_data:
        print("❌ No data could be loaded")
        return None

    # Combine all data
    print(f"\n🔄 Combining {len(combined_data)} files...")
    full_data = pd.concat(combined_data, ignore_index=True)

    # Remove duplicates based on key columns
    print("🧹 Removing duplicates...")
    before_count = len(full_data)
    full_data = full_data.drop_duplicates(subset=['date', 'expiration', 'strike', 'option_type'])
    after_count = len(full_data)

    print(f"📊 COMBINED DATASET SUMMARY:")
    print(f"   Total records: {after_count:,} (removed {before_count - after_count:,} duplicates)")
    print(f"   Date range: {full_data['date'].min()} to {full_data['date'].max()}")
    print(f"   Strike range: {full_data['strike'].min()} to {full_data['strike'].max()}")
    print(f"   Files combined: {len(all_files)}")

    # Save combined dataset
    output_path = 'data/SPX_COMPLETE_COMBINED.csv'
    os.makedirs('data', exist_ok=True)

    # Save with proper headers
    full_data.to_csv(output_path, index=False, header=True)
    print(f"💾 Combined data saved to: {output_path}")

    return output_path


def generate_performance_report(trades_df: pd.DataFrame, equity_curve: pd.DataFrame, strategy_name: str = "Cluster Strategy"):
    """Generate comprehensive performance report with charts and analysis"""

    print("\n🎯 GENERATING COMPREHENSIVE PERFORMANCE REPORT")
    print("=" * 60)

    # Create reports directory
    os.makedirs('reports', exist_ok=True)

    # Calculate performance metrics
    total_trades = len(trades_df)
    winning_trades = len(trades_df[trades_df['final_pnl'] > 0])
    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

    total_pnl = trades_df['final_pnl'].sum()
    avg_win = trades_df[trades_df['final_pnl'] > 0]['final_pnl'].mean() if winning_trades > 0 else 0
    avg_loss = trades_df[trades_df['final_pnl'] < 0]['final_pnl'].mean() if (total_trades - winning_trades) > 0 else 0

    profit_factor = abs(avg_win * winning_trades / (avg_loss * (total_trades - winning_trades))) if avg_loss != 0 else float('inf')

    # Calculate drawdown
    equity_curve['running_max'] = equity_curve['capital'].expanding().max()
    equity_curve['drawdown'] = (equity_curve['capital'] - equity_curve['running_max']) / equity_curve['running_max'] * 100
    max_drawdown = equity_curve['drawdown'].min()

    # Calculate Sharpe ratio (simplified)
    returns = equity_curve['capital'].pct_change().dropna()
    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0

    # Generate comprehensive charts
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'{strategy_name} - Comprehensive Performance Analysis', fontsize=16, fontweight='bold')

    # 1. Equity Curve (Enhanced)
    ax1.plot(equity_curve.index, equity_curve['capital'], linewidth=2.5, color='darkblue', alpha=0.8)
    ax1.fill_between(equity_curve.index, equity_curve['capital'], alpha=0.2, color='blue')
    ax1.set_title('Equity Curve', fontweight='bold', fontsize=14)
    ax1.set_ylabel('Capital ($)', fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

    # Add annotations for key milestones
    if len(equity_curve) > 0:
        start_capital = equity_curve['capital'].iloc[0]
        end_capital = equity_curve['capital'].iloc[-1]
        total_return = (end_capital - start_capital) / start_capital * 100

        ax1.annotate(f'Start: ${start_capital:,.0f}',
                    xy=(equity_curve.index[0], start_capital),
                    xytext=(10, 10), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.7))

        ax1.annotate(f'End: ${end_capital:,.0f}\n+{total_return:.1f}%',
                    xy=(equity_curve.index[-1], end_capital),
                    xytext=(-80, -30), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='blue', alpha=0.7))

    # 2. Drawdown
    ax2.fill_between(equity_curve.index, equity_curve['drawdown'], 0, color='red', alpha=0.3)
    ax2.plot(equity_curve.index, equity_curve['drawdown'], color='red', linewidth=1)
    ax2.set_title('Drawdown', fontweight='bold')
    ax2.set_ylabel('Drawdown (%)')
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=max_drawdown, color='red', linestyle='--', alpha=0.7)
    ax2.text(0.02, 0.95, f'Max DD: {max_drawdown:.1f}%', transform=ax2.transAxes,
             bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7))

    # 3. Trade P&L Distribution
    ax3.hist(trades_df['final_pnl'], bins=30, alpha=0.7, color='purple', edgecolor='black')
    ax3.axvline(x=0, color='red', linestyle='--', alpha=0.7)
    ax3.set_title('Trade P&L Distribution', fontweight='bold')
    ax3.set_xlabel('P&L ($)')
    ax3.set_ylabel('Frequency')
    ax3.grid(True, alpha=0.3)

    # 4. Full Equity Curve with Trade Markers
    ax4.plot(equity_curve.index, equity_curve['capital'], linewidth=2, color='darkgreen', alpha=0.8, label='Capital')

    # Add trade entry markers if we have trade data
    if len(trades_df) > 0:
        # Mark winning and losing trades
        winning_trades = trades_df[trades_df['final_pnl'] > 0]
        losing_trades = trades_df[trades_df['final_pnl'] <= 0]

        if len(winning_trades) > 0:
            win_dates = pd.to_datetime(winning_trades['entry_date'])
            win_capitals = []
            for date in win_dates:
                closest_date = equity_curve.index[equity_curve.index <= date]
                if len(closest_date) > 0:
                    win_capitals.append(equity_curve.loc[closest_date[-1], 'capital'])
                else:
                    win_capitals.append(equity_curve['capital'].iloc[0])
            ax4.scatter(win_dates, win_capitals, color='green', s=30, alpha=0.8, marker='^', label=f'Wins ({len(winning_trades)})')

        if len(losing_trades) > 0:
            loss_dates = pd.to_datetime(losing_trades['entry_date'])
            loss_capitals = []
            for date in loss_dates:
                closest_date = equity_curve.index[equity_curve.index <= date]
                if len(closest_date) > 0:
                    loss_capitals.append(equity_curve.loc[closest_date[-1], 'capital'])
                else:
                    loss_capitals.append(equity_curve['capital'].iloc[0])
            ax4.scatter(loss_dates, loss_capitals, color='red', s=30, alpha=0.8, marker='v', label=f'Losses ({len(losing_trades)})')

    ax4.set_title('Full Equity Curve with Trade Markers', fontweight='bold', fontsize=14)
    ax4.set_ylabel('Capital ($)', fontweight='bold')
    ax4.grid(True, alpha=0.3)
    ax4.legend(loc='upper left', fontsize=10)
    ax4.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

    plt.tight_layout()

    # Save charts
    chart_path = 'reports/comprehensive_performance_charts.png'
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"📊 Charts saved: {chart_path}")
    plt.close(fig)

    # Print summary to console
    print(f"\n📊 PERFORMANCE SUMMARY")
    print("=" * 50)
    print(f"Total Trades: {total_trades:,}")
    print(f"Win Rate: {win_rate:.1f}%")
    print(f"Total P&L: ${total_pnl:,.2f}")
    print(f"Total Return: {((equity_curve['capital'].iloc[-1] / equity_curve['capital'].iloc[0]) - 1) * 100:.1f}%")
    print(f"Max Drawdown: {max_drawdown:.1f}%")
    print(f"Sharpe Ratio: {sharpe_ratio:.2f}")
    print(f"Profit Factor: {profit_factor:.2f}")

    # Show recent trades with strike and expiry details
    if len(trades_df) > 0:
        print(f"\n📋 RECENT TRADES (Last 5):")
        print("-" * 80)
        recent_trades = trades_df.tail(5)
        for _, trade in recent_trades.iterrows():
            option_desc = "call" if trade['option_type'].lower() == 'c' else "put"
            expiry_str = pd.to_datetime(trade['expiry']).strftime('%Y-%m-%d') if pd.notna(trade['expiry']) else 'N/A'
            pnl_str = f"${trade['final_pnl']:,.0f}" if pd.notna(trade['final_pnl']) else 'N/A'
            print(f"  {trade['entry_date']}: {trade['signal_type']} {option_desc} ${trade['strike']} exp {expiry_str} → {pnl_str}")

    return {
        'total_trades': total_trades,
        'win_rate': win_rate,
        'total_pnl': total_pnl,
        'max_drawdown': max_drawdown,
        'sharpe_ratio': sharpe_ratio,
        'profit_factor': profit_factor
    }


def run_cluster_backtest(start_year=START_YEAR):
    """Run the refactored cluster strategy backtest"""

    print("🎯 ENHANCED CLUSTER STRATEGY - DRAWDOWN REDUCTION VERSION")
    print("=" * 70)
    print("DRAWDOWN REDUCTION FEATURES:")
    print("  � Tighter risk management: 3% max risk per trade (vs 10%)")
    print("  ⏰ Closer expiry: 14 days (vs 60 days) for faster time decay")
    print("  📏 Smaller positions: 1-2 contracts max (vs 5-10)")
    print("  ⚡ Faster exits: 1 day max hold (vs 2 days)")
    print("  🎯 Higher signal quality: 80% volume threshold, 5.0 wall strength")
    print("  📊 Tighter drift threshold: 0.2% (vs 0.4%)")
    print("  � Call walls + strong downward drift → BUY puts")
    print("  🟢 Put walls + strong upward drift → BUY calls")
    print(f"  📅 Data from: {start_year} onwards")
    print("=" * 70)

    # Load all available SPX options data
    combined_data_path = load_all_spx_options_data(start_year)

    # Initialize with combined data or fallback to default
    config = Config()
    if combined_data_path:
        data_loader = OptionsDataLoader(combined_data_path)
    else:
        data_loader = OptionsDataLoader(config.data_path)

    # Load data first to determine actual date range
    print("📊 Loading options data...")
    raw_data = data_loader.load_raw_data()

    if raw_data is None or len(raw_data) == 0:
        print("⚠️ Combined data loading failed, falling back to original data")
        config = Config()
        data_loader = OptionsDataLoader(config.data_path)
        raw_data = data_loader.load_raw_data()

    print(f"Loaded {len(raw_data):,} options records")

    # Get trading dates and determine actual data range
    all_trading_dates = sorted(raw_data['date'].unique())
    actual_start_date = all_trading_dates[0]
    actual_end_date = all_trading_dates[-1]

    # Use configured start year but respect actual data availability
    # Ensure start date is the first day of the year
    configured_start_date = pd.to_datetime(f'{start_year}-01-01')
    configured_end_date = pd.to_datetime(f'{BACKTEST_END_YEAR}-12-31')

    start_date = max(configured_start_date, actual_start_date)
    end_date = min(configured_end_date, actual_end_date)

    trading_dates = [d for d in all_trading_dates if start_date <= d <= end_date]
    print(f"📅 Backtest Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    print(f"📈 Total trading days: {len(trading_dates)}")
    print(f"📊 Data Range: {actual_start_date.strftime('%Y-%m-%d')} to {actual_end_date.strftime('%Y-%m-%d')}")

    # Initialize strategy with synchronized dates
    print("🔄 Initializing strategy with synchronized date range...")
    strategy = ClusterStrategy(start_year=start_year, start_date=start_date, end_date=end_date)

    # Initialize tracking
    starting_capital = INITIAL_CAPITAL
    current_capital = starting_capital
    positions = []
    completed_trades = []
    daily_analysis = []
    equity_curve = []  # Track daily capital for equity curve

    print(f"\n💰 Starting Capital: ${starting_capital:,.2f}")
    print(f"🎯 Strategy: {strategy.name}")

    # Main backtest loop - Options Trading Implementation
    for i, date in enumerate(trading_dates):
        if i % 100 == 0:
            print(f"  Processing {i+1}/{len(trading_dates)}: {date.strftime('%Y-%m-%d')}")

        # Get options data for this date
        options_data = raw_data[raw_data['date'] == date].copy()

        if options_data.empty:
            continue

        # Get current underlying price
        current_price = data_loader.extract_underlying_price(options_data, date)

        # Get overnight drift
        overnight_drift = strategy.get_overnight_drift(date)

        # Try enhanced signal first, fallback to traditional if too slow
        try:
            signal = strategy.generate_enhanced_signal(options_data, current_price, date, overnight_drift)

            # If enhanced signal is neutral or fails, use traditional method
            if signal is None or signal.get('signal_type') == 'NEUTRAL':
                raise Exception("Enhanced signal returned neutral, using fallback")

        except Exception as e:
            # Fallback to traditional method for speed/reliability
            if i < 5:  # Only print for first few days
                print(f"    ⚠️ Using traditional signal (Greeks analytics skipped): {str(e)[:50]}")

            wall_data = strategy.detect_option_walls(options_data, current_price)
            signal = strategy.generate_signal(wall_data, overnight_drift, current_price)
            signal['wall_data'] = wall_data  # Add wall data for compatibility

        # Debug: Print signal details for first few days only
        if i < 3:
            print(f"    🔍 DEBUG Day {i+1}: Signal={signal['signal_type']}, Strength={signal['signal_strength']:.3f}, Size={signal['position_size']}")

            # Handle both enhanced and fallback signals
            if 'wall_data' in signal:
                wall_data = signal['wall_data']
                print(f"        Wall strengths: Call={wall_data.get('call_wall_strength', 0):.1f}, Put={wall_data.get('put_wall_strength', 0):.1f}")
                if 'vanna_convergence' in wall_data:
                    print(f"        Greeks: Vanna={wall_data['vanna_convergence']:.3f}, Charm={wall_data['charm_decay_signal']:.3f}, Vomma={wall_data['vomma_volatility_signal']:.3f}")

            print(f"        Drift: {overnight_drift*100:.2f}%, Rationale: {signal['rationale']}")

            # Find option for debugging
            option_details = strategy.find_option_strike(signal['signal_type'], current_price, date, options_data)
            if option_details:
                print(f"        Option: {option_details['option_type']} ${option_details['strike']} exp {option_details['expiry']}")
                print(f"        Entry price: ${option_details['entry_price']:.2f}, Contracts: {signal['position_size']}, Position value: ${signal['position_size'] * 100 * option_details['entry_price']:,.0f}")

        # Check for exits first - Options Trading Logic
        positions_to_remove = []
        for pos_idx, position in enumerate(positions):
            days_held = (date - position['entry_date']).days

            # Exit on MAX_HOLD_DAYS (enter next day after signal, exit on max hold days)
            if days_held >= MAX_HOLD_DAYS:
                # Find exit option price for day 3
                exit_options = options_data[
                    (options_data['strike'] == position['strike']) &
                    (options_data['expiration'] == position['expiry']) &
                    (options_data['option_type'] == position['option_type'])
                ]

                if not exit_options.empty:
                    exit_price = (exit_options.iloc[0]['bid'] + exit_options.iloc[0]['ask']) / 2
                else:
                    # Fallback to intrinsic value if option not found
                    if position['option_type'].upper() == 'C':  # Call
                        exit_price = max(0, current_price - position['strike'])
                    else:  # Put
                        exit_price = max(0, position['strike'] - current_price)

                # Calculate P&L: (exit_price - entry_price) * SPX_MULTIPLIER * contracts
                price_change = exit_price - position['entry_price']
                current_pnl = price_change * SPX_MULTIPLIER * position['position_size']

                # Close position
                position['exit_date'] = date
                position['exit_price'] = exit_price
                position['exit_reason'] = f"MAX_HOLD_CLOSE ({days_held} days)"
                position['final_pnl'] = current_pnl
                position['days_held'] = days_held

                # Update capital
                current_capital += current_pnl

                # Log trade
                win_loss = "WIN" if current_pnl > 0 else "LOSS"
                option_desc = "call" if position['option_type'].lower() == 'c' else "put"
                print(f"    🎉 {win_loss}: BUY {option_desc} ${position['strike']} = ${current_pnl:,.0f} ({position['exit_reason']})")

                completed_trades.append(position.copy())
                positions_to_remove.append(pos_idx)

        # Remove closed positions
        for idx in reversed(positions_to_remove):
            positions.pop(idx)

        # Enter new position if signal exists and we have capacity
        if signal['signal_type'] != 'NEUTRAL' and len(positions) < 3:  # Max 3 concurrent positions

            # Find appropriate option for trading
            option_details = strategy.find_option_strike(signal['signal_type'], current_price, date, options_data)

            if option_details:
                # Check if we can enter tomorrow (need next trading day data)
                next_day_idx = i + 1
                if next_day_idx < len(trading_dates):
                    next_date = trading_dates[next_day_idx]
                    next_day_options = raw_data[raw_data['date'] == next_date].copy()

                    # Find the same option on next day for entry
                    entry_options = next_day_options[
                        (next_day_options['strike'] == option_details['strike']) &
                        (next_day_options['expiration'] == option_details['expiry']) &
                        (next_day_options['option_type'] == option_details['option_type'])
                    ]

                    if not entry_options.empty:
                        entry_price = (entry_options.iloc[0]['bid'] + entry_options.iloc[0]['ask']) / 2

                        # Skip if entry price is 0 or invalid
                        if entry_price <= 0:
                            continue

                        # Risk management: Conservative 3% max risk per trade
                        max_risk = current_capital * 0.03  # 3% max risk (reduced from 10%)

                        # Dynamic position sizing - use enhanced signal if available
                        if 'position_info' in signal and signal['position_info'] is not None:
                            # Use Greeks-based position sizing
                            contracts = signal['position_info']['contracts']
                            print(f"    📊 Greeks-based sizing: {contracts} contracts (quality: {signal['position_info']['greeks_quality']:.2f})")
                        else:
                            # Fallback to traditional sizing
                            base_contracts = signal['position_size']
                            max_contracts_by_risk = int(max_risk / (100 * entry_price))

                            # Use smaller of signal size or risk-limited size
                            contracts = min(base_contracts, max(1, max_contracts_by_risk))

                        # Calculate position value
                        position_value = contracts * SPX_MULTIPLIER * entry_price

                        # Reduced logging - only show key details for first few trades
                        if i < 3:
                            option_desc = "call" if option_details['option_type'].lower() == 'c' else "put"
                            print(f"        Option: {option_desc} ${option_details['strike']} exp {option_details['expiry'].strftime('%Y-%m-%d')}")
                            print(f"        Entry price: ${entry_price:.2f}, Contracts: {contracts}, Position value: ${position_value:,.0f}")

                        if position_value <= max_risk:
                            new_position = {
                                'entry_date': next_date,  # Enter next day
                                'signal_date': date,      # Signal generated today
                                'entry_price': entry_price,
                                'strike': option_details['strike'],
                                'expiry': option_details['expiry'],
                                'option_type': option_details['option_type'],
                                'signal_type': signal['signal_type'],
                                'position_size': contracts,
                                'position_value': position_value,
                                'signal_strength': signal['signal_strength'],
                                'rationale': signal['rationale'],
                                'overnight_drift': overnight_drift,
                                'days_to_expiry': option_details['days_to_expiry']
                            }

                            # Add enhanced signal data if available
                            if 'wall_data' in signal:
                                wall_data = signal['wall_data']
                                new_position.update({
                                    'wall_data': wall_data,
                                    'vanna_convergence': wall_data.get('vanna_convergence', 0),
                                    'charm_decay_signal': wall_data.get('charm_decay_signal', 0),
                                    'vomma_volatility_signal': wall_data.get('vomma_volatility_signal', 0)
                                })

                            positions.append(new_position)

                            # Determine action based on signal type
                            action = "BUY"  # Always BUY options (calls for bull, puts for bear)
                            option_desc = "call" if option_details['option_type'].lower() == 'c' else "put"
                            print(f"    📈 ENTRY: {action} {option_desc} ${option_details['strike']} at ${entry_price:.2f}, {contracts} contracts")
                # Print wall information if available
                if 'wall_data' in signal:
                    wall_data = signal['wall_data']
                    print(f"        Walls: {wall_data.get('dominant_wall', 'UNKNOWN')} (C:{wall_data.get('call_wall_count', 0)}, P:{wall_data.get('put_wall_count', 0)})")
                    if 'vanna_convergence' in wall_data:
                        print(f"        Greeks: Vanna={wall_data['vanna_convergence']:.3f}, Charm={wall_data['charm_decay_signal']:.3f}")

                print(f"        Drift: {overnight_drift*100:.2f}% (E-mini overnight)")
                print(f"        Signal: {signal['signal_strength']:.2f} - {signal['rationale']}")
            else:
                # Option not found - continue gracefully
                print(f"    ⚠️ SKIPPED: Could not find suitable option for {signal['signal_type']} signal (logged to reports/option_errors.log)")

        # Store daily analysis - handle both enhanced and fallback signals
        analysis_data = {
            'date': date,
            'current_price': current_price,
            'overnight_drift': overnight_drift,
            'signal_type': signal['signal_type'],
            'signal_strength': signal['signal_strength'],
            'rationale': signal['rationale'],
            'active_positions': len(positions),
            'current_capital': current_capital
        }

        # Add wall data if available
        if 'wall_data' in signal:
            wall_data = signal['wall_data']
            analysis_data.update({
                'call_wall_count': wall_data.get('call_wall_count', 0),
                'put_wall_count': wall_data.get('put_wall_count', 0),
                'call_wall_strength': wall_data.get('call_wall_strength', 0),
                'put_wall_strength': wall_data.get('put_wall_strength', 0),
                'dominant_wall': wall_data.get('dominant_wall', 'NEUTRAL'),
                'delta_imbalance': wall_data.get('delta_imbalance', 0),
                'vanna_convergence': wall_data.get('vanna_convergence', 0),
                'charm_decay_signal': wall_data.get('charm_decay_signal', 0),
                'vomma_volatility_signal': wall_data.get('vomma_volatility_signal', 0)
            })
        else:
            # Default values for missing wall data
            analysis_data.update({
                'call_wall_count': 0,
                'put_wall_count': 0,
                'call_wall_strength': 0,
                'put_wall_strength': 0,
                'dominant_wall': 'NEUTRAL',
                'delta_imbalance': 0,
                'vanna_convergence': 0,
                'charm_decay_signal': 0,
                'vomma_volatility_signal': 0
            })

        daily_analysis.append(analysis_data)

        # Track equity curve
        equity_curve.append({
            'date': date,
            'capital': current_capital
        })

    # Close any remaining positions
    for position in positions:
        final_date = trading_dates[-1]
        days_held = (final_date - position['entry_date']).days

        if position['signal_type'] == 'BUY':
            price_change = current_price - position['entry_price']
        else:
            price_change = position['entry_price'] - current_price

        final_pnl = price_change * SPX_MULTIPLIER * position['position_size']
        current_capital += final_pnl

        position.update({
            'exit_date': final_date,
            'exit_price': current_price,
            'exit_reason': 'BACKTEST_END',
            'final_pnl': final_pnl,
            'days_held': days_held
        })

        completed_trades.append(position)

    # Calculate results
    total_trades = len(completed_trades)
    winning_trades = len([t for t in completed_trades if t['final_pnl'] > 0])
    win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0

    total_pnl = sum(t['final_pnl'] for t in completed_trades)
    total_return = total_pnl / starting_capital * 100

    avg_win = np.mean([t['final_pnl'] for t in completed_trades if t['final_pnl'] > 0]) if winning_trades > 0 else 0
    avg_loss = np.mean([t['final_pnl'] for t in completed_trades if t['final_pnl'] < 0]) if (total_trades - winning_trades) > 0 else 0

    profit_factor = abs(avg_win * winning_trades / (avg_loss * (total_trades - winning_trades))) if avg_loss != 0 else float('inf')

    # Print results
    print(f"\n📊 DRAWDOWN REDUCTION CLUSTER STRATEGY RESULTS")
    print("=" * 60)
    print(f"Total Trades: {total_trades}")
    print(f"Winning Trades: {winning_trades}")
    print(f"Win Rate: {win_rate:.1f}%")
    print(f"Total P&L: ${total_pnl:,.2f}")
    print(f"Total Return: {total_return:.2f}%")
    print(f"Final Capital: ${current_capital:,.2f}")
    print(f"Average Win: ${avg_win:,.0f}")
    print(f"Average Loss: ${avg_loss:,.0f}")
    print(f"Profit Factor: {profit_factor:.2f}")

    # Save results with enhanced trade details
    os.makedirs('reports', exist_ok=True)

    # Create enhanced trades dataframe with strike and expiry
    if completed_trades:
        trades_df = pd.DataFrame(completed_trades)
        # Ensure expiry is formatted properly for display
        if 'expiry' in trades_df.columns:
            trades_df['expiry_date'] = pd.to_datetime(trades_df['expiry']).dt.strftime('%Y-%m-%d')
        trades_df.to_csv('reports/cluster_trades_refactored.csv', index=False)
    else:
        # Create empty dataframe with expected columns
        empty_trades = pd.DataFrame(columns=[
            'entry_date', 'exit_date', 'signal_type', 'strike', 'expiry', 'expiry_date',
            'option_type', 'entry_price', 'exit_price', 'position_size', 'final_pnl',
            'exit_reason', 'days_held', 'signal_strength', 'overnight_drift', 'rationale'
        ])
        empty_trades.to_csv('reports/cluster_trades_refactored.csv', index=False)

    pd.DataFrame(daily_analysis).to_csv('reports/cluster_daily_refactored.csv', index=False)

    print(f"\n💾 Results saved to reports/cluster_*_refactored.csv")

    # Generate comprehensive performance report
    if completed_trades and equity_curve:
        trades_df = pd.DataFrame(completed_trades)
        equity_df = pd.DataFrame(equity_curve)
        equity_df.set_index('date', inplace=True)

        # Generate the comprehensive performance report
        generate_performance_report(trades_df, equity_df, "Refactored Cluster Strategy")

    return {
        'total_trades': total_trades,
        'win_rate': win_rate,
        'total_return': total_return,
        'profit_factor': profit_factor,
        'completed_trades': completed_trades,
        'daily_analysis': daily_analysis
    }


if __name__ == "__main__":
    # Use constant for start year - can be overridden by changing constants.py
    # Options: 2012 (full history), 2020 (recent), 2022, 2023 (default), 2024 (latest)

    results = run_cluster_backtest(START_YEAR)
    print(f"\n✅ Cluster strategy backtest completed successfully!")
