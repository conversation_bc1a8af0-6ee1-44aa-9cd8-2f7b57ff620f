"""
Timing Optimization Framework for Enhanced Regime-Based Options Playbook
Tests different entry/exit timing combinations to maximize win rate
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cluster_strategy_refactored import ClusterStrategy, load_all_spx_options_data
from src.config import Config

class TimingOptimizationFramework:
    """
    Framework to test different entry/exit timing combinations
    """
    
    def __init__(self):
        """Initialize timing optimization framework"""
        self.logger = logging.getLogger(__name__)
        
        # Define timing strategies to test
        self.timing_strategies = {
            'open_to_close': {
                'name': 'Open-to-Close (Current)',
                'entry_time': 'open',
                'exit_time': 'close',
                'description': 'Enter at market open, exit at market close'
            },
            'close_to_close': {
                'name': 'Close-to-Close',
                'entry_time': 'close',
                'exit_time': 'close',
                'description': 'Enter at market close, exit at market close'
            },
            'close_to_open': {
                'name': 'Close-to-Open',
                'entry_time': 'close',
                'exit_time': 'open',
                'description': 'Enter at market close, exit at next market open'
            },
            'open_to_open': {
                'name': 'Open-to-Open',
                'entry_time': 'open',
                'exit_time': 'open',
                'description': 'Enter at market open, exit at next market open'
            }
        }
        
        # Results storage
        self.timing_results = {}
        
        print("✅ Timing Optimization Framework initialized")
    
    def get_option_price_by_timing(self, options_data, option_details, date, timing_type):
        """
        Get option price based on timing strategy (open vs close)
        
        Args:
            options_data: Full options dataset
            option_details: Option specification (strike, expiry, type)
            date: Trading date
            timing_type: 'open' or 'close'
            
        Returns:
            Option price for the specified timing
        """
        
        # Filter for the specific option on the specific date
        option_filter = (
            (options_data['date'] == date) &
            (options_data['strike'] == option_details['strike']) &
            (options_data['expiration'] == option_details['expiry']) &
            (options_data['option_type'] == option_details['option_type'])
        )
        
        matching_options = options_data[option_filter]
        
        if matching_options.empty:
            return None
        
        option_row = matching_options.iloc[0]
        
        if timing_type == 'open':
            # For open timing, we'll use the mid price as proxy
            # In real implementation, this would use opening auction prices
            return (option_row['bid'] + option_row['ask']) / 2
        elif timing_type == 'close':
            # For close timing, use the closing mid price
            return (option_row['bid'] + option_row['ask']) / 2
        else:
            raise ValueError(f"Unknown timing type: {timing_type}")
    
    def run_timing_strategy_backtest(self, timing_strategy_key, options_data, emini_analyzer):
        """
        Run backtest for a specific timing strategy
        
        Args:
            timing_strategy_key: Key from self.timing_strategies
            options_data: Full options dataset
            emini_analyzer: E-mini analyzer for drift calculation
            
        Returns:
            Dictionary with backtest results
        """
        
        timing_config = self.timing_strategies[timing_strategy_key]
        print(f"\n🎯 TESTING TIMING STRATEGY: {timing_config['name']}")
        print(f"📋 Description: {timing_config['description']}")
        print("=" * 60)
        
        # Create modified strategy with timing-specific logic
        strategy = TimingAwareClusterStrategy(
            timing_config=timing_config,
            emini_analyzer=emini_analyzer
        )
        
        # Run backtest
        results = strategy.run_backtest(options_data)
        
        # Store results
        self.timing_results[timing_strategy_key] = {
            'config': timing_config,
            'results': results,
            'trades': results.get('trades', []),
            'performance': {
                'total_trades': results.get('total_trades', 0),
                'win_rate': results.get('win_rate', 0.0),
                'total_return': results.get('total_return', 0.0),
                'max_drawdown': results.get('max_drawdown', 0.0),
                'profit_factor': results.get('profit_factor', 0.0),
                'sharpe_ratio': results.get('sharpe_ratio', 0.0),
                'avg_win': results.get('avg_win', 0.0),
                'avg_loss': results.get('avg_loss', 0.0)
            }
        }
        
        print(f"✅ {timing_config['name']} backtest completed")
        print(f"📊 Trades: {results.get('total_trades', 0)} | "
              f"Win Rate: {results.get('win_rate', 0.0):.1f}% | "
              f"Return: {results.get('total_return', 0.0):.1f}% | "
              f"Max DD: {results.get('max_drawdown', 0.0):.1f}%")
        
        return self.timing_results[timing_strategy_key]
    
    def run_comprehensive_timing_optimization(self):
        """
        Run comprehensive timing optimization across all strategies
        """
        
        print("🚀 STARTING COMPREHENSIVE TIMING OPTIMIZATION")
        print("=" * 70)
        
        # Load data
        print("📊 Loading options data...")
        options_data = load_all_spx_options_data()
        
        print("📊 Loading E-mini data...")
        from src.emini_overnight_analyzer import EminiOvernightAnalyzer
        emini_analyzer = EminiOvernightAnalyzer()
        
        # Test each timing strategy
        for strategy_key in self.timing_strategies.keys():
            try:
                self.run_timing_strategy_backtest(strategy_key, options_data, emini_analyzer)
            except Exception as e:
                print(f"❌ Error testing {strategy_key}: {e}")
                self.timing_results[strategy_key] = {
                    'config': self.timing_strategies[strategy_key],
                    'error': str(e),
                    'performance': {}
                }
        
        # Analyze and compare results
        self.analyze_timing_results()
        
        # Generate comprehensive report
        self.generate_timing_optimization_report()
        
        return self.timing_results
    
    def analyze_timing_results(self):
        """
        Analyze and rank timing strategies by performance
        """
        
        print("\n📊 ANALYZING TIMING STRATEGY PERFORMANCE")
        print("=" * 60)
        
        # Create performance comparison
        performance_data = []
        
        for strategy_key, result in self.timing_results.items():
            if 'error' not in result:
                perf = result['performance']
                performance_data.append({
                    'strategy': strategy_key,
                    'name': result['config']['name'],
                    'trades': perf.get('total_trades', 0),
                    'win_rate': perf.get('win_rate', 0.0),
                    'total_return': perf.get('total_return', 0.0),
                    'max_drawdown': perf.get('max_drawdown', 0.0),
                    'profit_factor': perf.get('profit_factor', 0.0),
                    'sharpe_ratio': perf.get('sharpe_ratio', 0.0),
                    'avg_win': perf.get('avg_win', 0.0),
                    'avg_loss': perf.get('avg_loss', 0.0)
                })
        
        if not performance_data:
            print("❌ No successful timing strategy results to analyze")
            return
        
        # Convert to DataFrame for analysis
        perf_df = pd.DataFrame(performance_data)
        
        # Rank by win rate (primary objective)
        perf_df_sorted = perf_df.sort_values('win_rate', ascending=False)
        
        print("\n🏆 TIMING STRATEGY RANKINGS (by Win Rate):")
        print("-" * 80)
        for idx, row in perf_df_sorted.iterrows():
            print(f"{row.name + 1:2d}. {row['name']:<20} | "
                  f"Win Rate: {row['win_rate']:5.1f}% | "
                  f"Return: {row['total_return']:6.1f}% | "
                  f"Max DD: {row['max_drawdown']:5.1f}% | "
                  f"Trades: {row['trades']:3d}")
        
        # Identify best strategy
        best_strategy = perf_df_sorted.iloc[0]
        print(f"\n🥇 BEST TIMING STRATEGY: {best_strategy['name']}")
        print(f"   Win Rate: {best_strategy['win_rate']:.1f}%")
        print(f"   Total Return: {best_strategy['total_return']:.1f}%")
        print(f"   Max Drawdown: {best_strategy['max_drawdown']:.1f}%")
        print(f"   Profit Factor: {best_strategy['profit_factor']:.2f}")
        
        # Store analysis results
        self.best_timing_strategy = best_strategy['strategy']
        self.performance_comparison = perf_df_sorted
        
        return perf_df_sorted
    
    def generate_timing_optimization_report(self):
        """
        Generate comprehensive timing optimization report
        """
        
        print("\n📋 GENERATING TIMING OPTIMIZATION REPORT")
        print("=" * 60)
        
        # Create detailed report
        report_path = 'reports/timing_optimization_report.txt'
        
        with open(report_path, 'w') as f:
            f.write("ENHANCED REGIME-BASED OPTIONS PLAYBOOK - TIMING OPTIMIZATION REPORT\n")
            f.write("=" * 80 + "\n\n")
            
            f.write("OBJECTIVE: Optimize entry/exit timing to maximize win rate\n")
            f.write("BASELINE: 31.7% win rate with 82 trades\n\n")
            
            f.write("TIMING STRATEGIES TESTED:\n")
            f.write("-" * 40 + "\n")
            
            for strategy_key, config in self.timing_strategies.items():
                f.write(f"{config['name']}: {config['description']}\n")
            
            f.write("\nPERFORMANCE RESULTS:\n")
            f.write("-" * 40 + "\n")
            
            if hasattr(self, 'performance_comparison'):
                for idx, row in self.performance_comparison.iterrows():
                    f.write(f"\n{idx + 1}. {row['name']}\n")
                    f.write(f"   Win Rate: {row['win_rate']:.1f}%\n")
                    f.write(f"   Total Return: {row['total_return']:.1f}%\n")
                    f.write(f"   Max Drawdown: {row['max_drawdown']:.1f}%\n")
                    f.write(f"   Profit Factor: {row['profit_factor']:.2f}\n")
                    f.write(f"   Total Trades: {row['trades']}\n")
            
            if hasattr(self, 'best_timing_strategy'):
                f.write(f"\nRECOMMENDED TIMING STRATEGY: {self.timing_strategies[self.best_timing_strategy]['name']}\n")
                f.write(f"RATIONALE: Highest win rate while maintaining acceptable risk metrics\n")
        
        print(f"✅ Timing optimization report saved to: {report_path}")
        
        return report_path


class TimingAwareClusterStrategy(ClusterStrategy):
    """
    Modified ClusterStrategy that implements different timing strategies
    """

    def __init__(self, timing_config, emini_analyzer):
        """Initialize timing-aware strategy"""
        super().__init__()
        self.timing_config = timing_config
        self.emini_analyzer = emini_analyzer

        print(f"🕐 Timing Strategy: {timing_config['name']}")
        print(f"   Entry: {timing_config['entry_time']} | Exit: {timing_config['exit_time']}")

    def get_option_price_by_timing(self, options_data, strike, expiry, option_type, date, timing_type):
        """
        Get option price based on timing strategy

        Args:
            options_data: Options dataset
            strike: Option strike price
            expiry: Option expiry date
            option_type: Option type ('c' or 'p')
            date: Trading date
            timing_type: 'open' or 'close'

        Returns:
            Option price for specified timing
        """

        # Filter for the specific option on the specific date
        option_filter = (
            (options_data['date'] == date) &
            (options_data['strike'] == strike) &
            (options_data['expiration'] == expiry) &
            (options_data['option_type'] == option_type)
        )

        matching_options = options_data[option_filter]

        if matching_options.empty:
            return None

        option_row = matching_options.iloc[0]

        # For this implementation, we'll use bid-ask mid as proxy for both open and close
        # In production, you would use actual opening/closing auction prices
        if timing_type in ['open', 'close']:
            return (option_row['bid'] + option_row['ask']) / 2
        else:
            raise ValueError(f"Unknown timing type: {timing_type}")

    def run_backtest(self, options_data):
        """
        Run backtest with timing-aware entry/exit logic
        """

        print(f"🚀 Starting backtest with {self.timing_config['name']} timing strategy")

        # Initialize tracking variables
        current_capital = 100000.0  # Starting capital
        positions = []
        closed_trades = []
        daily_analysis = []

        # Get trading dates
        trading_dates = sorted(options_data['date'].unique())
        start_date = pd.Timestamp('2023-01-03')
        end_date = pd.Timestamp('2025-07-07')

        # Filter trading dates to backtest period
        trading_dates = [d for d in trading_dates if start_date <= d <= end_date]

        print(f"📅 Backtest Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        print(f"📈 Total trading days: {len(trading_dates)}")

        # Process each trading day
        for i, date in enumerate(trading_dates):
            if i % 100 == 0:
                print(f"  Processing {i+1}/{len(trading_dates)}: {date.strftime('%Y-%m-%d')}")

            # Get current market data
            current_price = self._get_current_price(options_data, date)
            if current_price is None:
                continue

            # Generate signals (same logic as original)
            signal = self._generate_signal(options_data, date, current_price)
            if signal is None:
                continue

            # Check for exits first (timing-aware)
            positions = self._process_exits_timing_aware(positions, options_data, date, current_capital, closed_trades)

            # Enter new positions (timing-aware)
            if signal['signal_type'] != 'NEUTRAL' and len(positions) < 3:
                new_position = self._enter_position_timing_aware(signal, options_data, date, current_price, current_capital)
                if new_position:
                    positions.append(new_position)

        # Close remaining positions at end
        final_date = trading_dates[-1]
        for position in positions:
            self._close_position_at_end(position, options_data, final_date, current_capital, closed_trades)

        # Calculate performance metrics
        results = self._calculate_performance_metrics(closed_trades, current_capital)

        return results

    def _process_exits_timing_aware(self, positions, options_data, date, current_capital, closed_trades):
        """
        Process position exits using timing-aware logic
        """

        positions_to_remove = []

        for position in positions:
            days_held = (date - position['entry_date']).days

            # Exit after 1 day (MAX_HOLD_DAYS = 1)
            if days_held >= 1:
                # Get exit price based on timing strategy
                exit_price = self.get_option_price_by_timing(
                    options_data=options_data,
                    strike=position['strike'],
                    expiry=position['expiry'],
                    option_type=position['option_type'],
                    date=date,
                    timing_type=self.timing_config['exit_time']
                )

                if exit_price is not None:
                    # Calculate P&L
                    price_change = exit_price - position['entry_price']
                    pnl = price_change * 100 * position['position_size']  # SPX multiplier = 100

                    # Update position
                    position['exit_date'] = date
                    position['exit_price'] = exit_price
                    position['exit_reason'] = f"TIMING_EXIT_{self.timing_config['exit_time'].upper()}"
                    position['final_pnl'] = pnl
                    position['days_held'] = days_held

                    # Update capital
                    current_capital += pnl

                    # Add to closed trades
                    closed_trades.append(position.copy())
                    positions_to_remove.append(position)

        # Remove closed positions
        for position in positions_to_remove:
            positions.remove(position)

        return positions

    def _enter_position_timing_aware(self, signal, options_data, date, current_price, current_capital):
        """
        Enter position using timing-aware logic
        """

        # Get enhanced trading recommendation (same as original)
        greeks_data = {
            'vanna_convergence': signal.get('wall_data', {}).get('vanna_convergence', 0),
            'charm_decay_signal': signal.get('wall_data', {}).get('charm_decay_signal', 0),
            'gamma': signal.get('wall_data', {}).get('gamma', 0)
        }

        wall_data = signal.get('wall_data', {})
        overnight_drift = self.emini_analyzer.get_overnight_drift(date) if self.emini_analyzer else 0.0

        recommendation = self.strategy_selector.get_trading_recommendation(
            date=date,
            greeks_data=greeks_data,
            wall_data=wall_data,
            drift=overnight_drift,
            current_price=current_price
        )

        if not recommendation['should_trade']:
            return None

        # Find option details (same logic as original)
        enhanced_strategy = recommendation['strategy']

        # Determine option type from enhanced strategy
        if enhanced_strategy and hasattr(enhanced_strategy, 'option_type'):
            strategy_type = enhanced_strategy.option_type.lower()

            if 'call' in strategy_type:
                effective_signal_type = 'BULLISH'
            elif 'put' in strategy_type:
                effective_signal_type = 'BEARISH'
            else:
                effective_signal_type = signal['signal_type']
        else:
            effective_signal_type = signal['signal_type']

        # Find option strike
        option_details = self.find_option_strike_enhanced(
            effective_signal_type, current_price, date, options_data,
            'call' if effective_signal_type == 'BULLISH' else 'put', enhanced_strategy
        )

        if not option_details:
            return None

        # Get entry price based on timing strategy
        entry_date = date  # For timing strategies, we enter on the same day

        entry_price = self.get_option_price_by_timing(
            options_data=options_data,
            strike=option_details['strike'],
            expiry=option_details['expiry'],
            option_type=option_details['option_type'],
            date=entry_date,
            timing_type=self.timing_config['entry_time']
        )

        if entry_price is None or entry_price <= 0:
            return None

        # Calculate position size
        contracts = self.strategy_selector.calculate_position_size(
            strategy=enhanced_strategy,
            base_capital=current_capital,
            current_price=current_price,
            option_price=entry_price
        )

        # Risk check
        position_value = contracts * 100 * entry_price
        max_risk = current_capital * (enhanced_strategy.max_risk_percent / 100.0)

        if position_value > max_risk:
            return None

        # Create position
        new_position = {
            'entry_date': entry_date,
            'signal_date': date,
            'entry_price': entry_price,
            'strike': option_details['strike'],
            'expiry': option_details['expiry'],
            'option_type': option_details['option_type'],
            'signal_type': effective_signal_type,
            'position_size': contracts,
            'strategy_name': enhanced_strategy.name,
            'timing_strategy': self.timing_config['name']
        }

        return new_position

    def _get_current_price(self, options_data, date):
        """Get current SPX price for the date"""
        day_data = options_data[options_data['date'] == date]
        if day_data.empty:
            return None

        # Use underlying close price
        return day_data.iloc[0]['underlying_close']

    def _generate_signal(self, options_data, date, current_price):
        """Generate trading signal (simplified version of original logic)"""

        # Get overnight drift
        overnight_drift = self.emini_analyzer.get_overnight_drift(date) if self.emini_analyzer else 0.0

        # Simple signal generation based on drift
        if abs(overnight_drift) > 0.002:  # 0.2% threshold
            signal_type = 'BULLISH' if overnight_drift > 0 else 'BEARISH'
            signal_strength = min(abs(overnight_drift) * 100, 1.0)  # Cap at 1.0

            return {
                'signal_type': signal_type,
                'signal_strength': signal_strength,
                'position_size': 1,  # Fixed size for timing tests
                'wall_data': {
                    'vanna_convergence': 0.0,
                    'charm_decay_signal': 0.0,
                    'gamma': 0.0
                }
            }

        return {'signal_type': 'NEUTRAL'}

    def _close_position_at_end(self, position, options_data, final_date, current_capital, closed_trades):
        """Close position at end of backtest"""

        # Get final price
        final_price = self.get_option_price_by_timing(
            options_data=options_data,
            strike=position['strike'],
            expiry=position['expiry'],
            option_type=position['option_type'],
            date=final_date,
            timing_type='close'  # Use close price for final exit
        )

        if final_price is None:
            # Use intrinsic value as fallback
            current_price = self._get_current_price(options_data, final_date)
            if position['option_type'].upper() == 'C':
                final_price = max(0, current_price - position['strike'])
            else:
                final_price = max(0, position['strike'] - current_price)

        # Calculate P&L
        price_change = final_price - position['entry_price']
        pnl = price_change * 100 * position['position_size']

        # Update position
        position['exit_date'] = final_date
        position['exit_price'] = final_price
        position['exit_reason'] = 'BACKTEST_END'
        position['final_pnl'] = pnl
        position['days_held'] = (final_date - position['entry_date']).days

        # Add to closed trades
        closed_trades.append(position.copy())

        return pnl

    def _calculate_performance_metrics(self, closed_trades, final_capital):
        """Calculate performance metrics from closed trades"""

        if not closed_trades:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'total_return': 0.0,
                'max_drawdown': 0.0,
                'profit_factor': 0.0,
                'sharpe_ratio': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'trades': []
            }

        # Convert to DataFrame for analysis
        trades_df = pd.DataFrame(closed_trades)

        # Basic metrics
        total_trades = len(closed_trades)
        winning_trades = trades_df[trades_df['final_pnl'] > 0]
        losing_trades = trades_df[trades_df['final_pnl'] <= 0]

        win_rate = (len(winning_trades) / total_trades) * 100 if total_trades > 0 else 0.0
        total_pnl = trades_df['final_pnl'].sum()
        total_return = (total_pnl / 100000.0) * 100  # Starting capital = 100k

        # Win/Loss metrics
        avg_win = winning_trades['final_pnl'].mean() if len(winning_trades) > 0 else 0.0
        avg_loss = losing_trades['final_pnl'].mean() if len(losing_trades) > 0 else 0.0

        # Profit factor
        gross_profit = winning_trades['final_pnl'].sum() if len(winning_trades) > 0 else 0.0
        gross_loss = abs(losing_trades['final_pnl'].sum()) if len(losing_trades) > 0 else 0.0
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0.0

        # Drawdown calculation (simplified)
        trades_df['cumulative_pnl'] = trades_df['final_pnl'].cumsum()
        trades_df['running_peak'] = trades_df['cumulative_pnl'].expanding().max()
        trades_df['drawdown'] = trades_df['cumulative_pnl'] - trades_df['running_peak']
        max_drawdown = abs(trades_df['drawdown'].min()) / 100000.0 * 100  # As percentage

        # Sharpe ratio (simplified)
        if len(trades_df) > 1:
            returns = trades_df['final_pnl'] / 100000.0  # Normalize by capital
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0.0
        else:
            sharpe_ratio = 0.0

        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'sharpe_ratio': sharpe_ratio,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'trades': closed_trades,
            'final_capital': final_capital
        }


if __name__ == "__main__":
    # Run timing optimization
    optimizer = TimingOptimizationFramework()
    results = optimizer.run_comprehensive_timing_optimization()
    
    print("\n🎯 TIMING OPTIMIZATION COMPLETE!")
    print("Check reports/timing_optimization_report.txt for detailed results")
