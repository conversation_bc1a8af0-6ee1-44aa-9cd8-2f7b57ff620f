#!/usr/bin/env python3
"""
Enhanced Cluster Strategy with Advanced Convergence Analysis

Integrates your new analytics engine and convergence processor:
- Comprehensive Greeks calculation (Delta, Gamma, Vega, Theta, Rho, Vomma, Vanna, Charm)
- Portfolio-level Greeks aggregation
- GEX (Gamma Exposure) analysis
- Convergence pattern detection (Bearish, Bullish, Explosive signals)
- Advanced position sizing based on convergence strength
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from src.dataloader import OptionsDataLoader
from src.config import Config
from src.emini_overnight_analyzer import EminiOvernightAnalyzer
from src.analytics_engine import <PERSON>Calculator, PortfolioGreeksCalculator
from src.constants import (
    START_YEAR, BACKTEST_END_YEAR, STRIKE_MULTIPLE, TARGET_EXPIRY_DAYS,
    MAX_HOLD_DAYS, DRIFT_THRESHOLD, WALL_STRENGTH_THRESHOLD, VOLUME_PERCENTILE,
    GAMMA_MULTIPLIER, DELTA_MULTIPLIER, BAND_WIDTH, INITIAL_CAPITAL,
    SPX_MULTIPLIER, MAX_CONTRACTS, MIN_CONTRACTS, MAX_RISK_PER_TRADE
)


class ConvergenceClusterStrategy:
    """
    Enhanced cluster strategy with advanced convergence analysis
    """

    def __init__(self, start_date: str, end_date: str):
        """Initialize the convergence cluster strategy"""
        self.start_date = pd.to_datetime(start_date)
        self.end_date = pd.to_datetime(end_date)

        # Initialize advanced analytics engines
        self.greeks_calc = GreeksCalculator(risk_free_rate=0.05, dividend_yield=0.015)
        self.portfolio_calc = PortfolioGreeksCalculator(self.greeks_calc, ticker="SPX")

        # Strategy parameters - ENHANCED FOR CONVERGENCE
        self.params = {
            'max_hold_days': 1,                               # Fast exits
            'target_expiry_days': 14,                         # Closer expiry
            'strike_multiple': STRIKE_MULTIPLE,
            'base_position_size': 1,                          # Conservative base
            'max_position_size': 3,                           # Allow larger positions for strong convergence
            'volume_percentile': 80,                          # High quality options
            'wall_strength_threshold': 5.0,                  # Strong walls required
            'drift_threshold': 0.002,                        # Tight drift threshold
            'convergence_threshold': 0.1,                    # Minimum convergence strength (lowered from 0.3)
            'explosive_multiplier': 2.0,                     # Position multiplier for explosive signals
            'max_risk_per_trade': 0.03                       # 3% max risk
        }

        # Initialize E-mini analyzer
        self.emini_analyzer = self._init_emini_analyzer()

    def _init_emini_analyzer(self):
        """Initialize E-mini analyzer with synchronized dates"""
        try:
            # Look for E-mini data file
            emini_data_path = "ES_full_5min_continuous_ratio_adjusted.txt"
            if not os.path.exists(emini_data_path):
                emini_data_path = "../ES_full_5min_continuous_ratio_adjusted.txt"

            if os.path.exists(emini_data_path):
                analyzer = EminiOvernightAnalyzer(emini_data_path)
                analyzer.initialize_data(
                    start_date=self.start_date.strftime('%Y-%m-%d'),
                    end_date=self.end_date.strftime('%Y-%m-%d')
                )
                return analyzer
            else:
                print(f"⚠️ Warning: E-mini data file not found: {emini_data_path}")
                return None
        except Exception as e:
            print(f"⚠️ Warning: Could not initialize E-mini analyzer: {e}")
            return None

    def prepare_options_data_for_analytics(self, options_data: pd.DataFrame,
                                         current_price: float, current_date: pd.Timestamp) -> pd.DataFrame:
        """
        Prepare options data for analytics engine compatibility with proper column mapping
        """
        if options_data.empty:
            return pd.DataFrame()

        # Create analytics-compatible DataFrame
        analytics_data = options_data.copy()

        # Map columns to analytics engine format - handle different column names
        # Strike column mapping
        if 'Strike' in analytics_data.columns:
            analytics_data['Strike'] = analytics_data['Strike']
        elif 'strike' in analytics_data.columns:
            analytics_data['Strike'] = analytics_data['strike']
        else:
            print("⚠️ Warning: No strike column found")
            return pd.DataFrame()

        # Open Interest mapping
        if 'Open Interest' in analytics_data.columns:
            analytics_data['Open Interest'] = analytics_data['Open Interest']
        elif 'open_interest' in analytics_data.columns:
            analytics_data['Open Interest'] = analytics_data['open_interest']
        else:
            analytics_data['Open Interest'] = 0

        # Volume mapping
        if 'Volume' in analytics_data.columns:
            analytics_data['Volume'] = analytics_data['Volume']
        elif 'volume' in analytics_data.columns:
            analytics_data['Volume'] = analytics_data['volume']
        else:
            analytics_data['Volume'] = 0

        # Spot price mapping
        analytics_data['spx_close'] = current_price  # Use current price as spot

        # Option type mapping
        if 'Call/Put' in analytics_data.columns:
            analytics_data['is_call'] = (analytics_data['Call/Put'].str.upper() == 'CALL').astype(int)
        elif 'option_type' in analytics_data.columns:
            analytics_data['is_call'] = (analytics_data['option_type'].str.lower() == 'c').astype(int)
        else:
            print("⚠️ Warning: No option type column found")
            return pd.DataFrame()

        # Expiration date mapping
        if 'Expiry Date' in analytics_data.columns:
            analytics_data['expiry_date'] = pd.to_datetime(analytics_data['Expiry Date'])
        elif 'expiration' in analytics_data.columns:
            analytics_data['expiry_date'] = pd.to_datetime(analytics_data['expiration'])
        else:
            print("⚠️ Warning: No expiration column found")
            return pd.DataFrame()

        # Calculate time to expiration
        analytics_data['tte'] = (analytics_data['expiry_date'] - current_date).dt.days / 365.0
        analytics_data['tte'] = analytics_data['tte'].clip(lower=1/365)  # Minimum 1 day

        # Calculate moneyness
        analytics_data['moneyness'] = analytics_data['Strike'] / current_price

        # Implied volatility mapping
        if 'Bid Implied Volatility' in analytics_data.columns and 'Ask Implied Volatility' in analytics_data.columns:
            analytics_data['bid_iv'] = analytics_data['Bid Implied Volatility'].fillna(0.2)
            analytics_data['ask_iv'] = analytics_data['Ask Implied Volatility'].fillna(0.2)
        else:
            analytics_data['bid_iv'] = 0.2
            analytics_data['ask_iv'] = 0.2

        analytics_data['iv_mid'] = (analytics_data['bid_iv'] + analytics_data['ask_iv']) / 2
        analytics_data['iv_mid'] = analytics_data['iv_mid'].fillna(0.2).clip(lower=0.05, upper=2.0)

        # Filter for significant options
        significant_options = analytics_data[
            (analytics_data['Volume'] > 0) |
            (analytics_data['Open Interest'] > 50)
        ].copy()

        return significant_options

    def calculate_convergence_metrics(self, options_data: pd.DataFrame,
                                    current_price: float, current_date: pd.Timestamp) -> dict:
        """
        Calculate comprehensive convergence metrics using your analytics engine
        """
        # Prepare data for analytics
        analytics_data = self.prepare_options_data_for_analytics(options_data, current_price, current_date)

        if analytics_data.empty:
            return self._empty_convergence_metrics()

        try:
            # Calculate portfolio Greeks using your engine with error handling
            portfolio_greeks = self.portfolio_calc.calculate_portfolio_greeks(analytics_data)

            # Validate portfolio Greeks for division by zero issues
            for key, value in portfolio_greeks.items():
                if not isinstance(value, (int, float)) or not np.isfinite(value):
                    portfolio_greeks[key] = 0.0

            # Calculate additional convergence metrics with error handling
            additional_metrics = self._calculate_additional_convergence_metrics(analytics_data, portfolio_greeks)

            # Scale Greeks to match your convergence patterns with zero checks
            scaled_metrics = self._scale_greeks_for_convergence(portfolio_greeks)

            # Calculate convergence signals with error handling
            convergence_signals = self._calculate_convergence_signals(scaled_metrics)

            # Combine all metrics with validation
            all_metrics = {
                **portfolio_greeks,
                **additional_metrics,
                **scaled_metrics,
                **convergence_signals,
                'spot_price': current_price,
                'date': current_date.strftime('%Y-%m-%d')
            }

            # Final validation - replace any NaN or inf values
            for key, value in all_metrics.items():
                if not isinstance(value, (int, float, str)) or (isinstance(value, float) and not np.isfinite(value)):
                    all_metrics[key] = 0.0

            return all_metrics

        except Exception as e:
            print(f"⚠️ Error calculating convergence metrics: {e}")
            return self._empty_convergence_metrics()

    def _empty_convergence_metrics(self) -> dict:
        """Return empty convergence metrics"""
        return {
            'total_delta': 0, 'total_gamma': 0, 'total_vega': 0, 'total_theta': 0,
            'total_vomma': 0, 'total_vanna': 0, 'total_charm': 0, 'gex': 0,
            'charm_scaled': 0, 'vanna_scaled': 0, 'gex_scaled': 0, 'vomma_scaled': 0,
            'bearish_signal': 0, 'bullish_signal': 0, 'explosive_signal': 0,
            'signal_strength': 0, 'convergence_quality': 0
        }

    def _calculate_additional_convergence_metrics(self, analytics_data: pd.DataFrame,
                                                portfolio_greeks: dict) -> dict:
        """Calculate additional metrics for convergence analysis"""
        metrics = {}

        # Volume-weighted flow (VolmBS) with zero checks
        call_volume = analytics_data[analytics_data['is_call'] == 1]['Volume'].sum()
        put_volume = analytics_data[analytics_data['is_call'] == 0]['Volume'].sum()
        metrics['volm_bs'] = call_volume - put_volume

        # VxOI (Volatility x Open Interest proxy) with zero checks
        total_oi = analytics_data['Open Interest'].sum()
        if total_oi > 0:
            try:
                weighted_iv = (analytics_data['iv_mid'] * analytics_data['Open Interest']).sum() / total_oi
                metrics['vx_oi'] = weighted_iv * total_oi / 1000
            except (ZeroDivisionError, ValueError):
                metrics['vx_oi'] = 0
        else:
            metrics['vx_oi'] = 0

        # Put/Call ratios with zero checks
        if call_volume > 0:
            try:
                metrics['pc_ratio_volume'] = put_volume / call_volume
            except ZeroDivisionError:
                metrics['pc_ratio_volume'] = 0
        else:
            metrics['pc_ratio_volume'] = 0 if put_volume == 0 else float('inf')

        call_oi = analytics_data[analytics_data['is_call'] == 1]['Open Interest'].sum()
        put_oi = analytics_data[analytics_data['is_call'] == 0]['Open Interest'].sum()
        if call_oi > 0:
            try:
                metrics['pc_ratio_oi'] = put_oi / call_oi
            except ZeroDivisionError:
                metrics['pc_ratio_oi'] = 0
        else:
            metrics['pc_ratio_oi'] = 0 if put_oi == 0 else float('inf')

        # Weighted average time to expiration with zero checks
        if total_oi > 0:
            try:
                metrics['avg_tte'] = (analytics_data['tte'] * analytics_data['Open Interest']).sum() / total_oi
            except (ZeroDivisionError, ValueError):
                metrics['avg_tte'] = 0
        else:
            metrics['avg_tte'] = 0

        # Skew calculation
        otm_calls = analytics_data[(analytics_data['is_call'] == 1) & (analytics_data['moneyness'] > 1.02)]
        otm_puts = analytics_data[(analytics_data['is_call'] == 0) & (analytics_data['moneyness'] < 0.98)]

        if not otm_calls.empty and not otm_puts.empty:
            call_iv_avg = otm_calls['iv_mid'].mean()
            put_iv_avg = otm_puts['iv_mid'].mean()
            metrics['skew'] = put_iv_avg - call_iv_avg
        else:
            metrics['skew'] = 0

        return metrics

    def _scale_greeks_for_convergence(self, portfolio_greeks: dict) -> dict:
        """Scale Greeks to match convergence analysis patterns with zero checks"""
        try:
            # Safe division with zero checks and validation
            def safe_divide(value, divisor, default=0):
                try:
                    if abs(value) < 1e-10 or divisor == 0:
                        return default
                    result = value / divisor
                    return result if np.isfinite(result) else default
                except (ZeroDivisionError, TypeError, ValueError):
                    return default

            scaled_metrics = {
                'charm_scaled': safe_divide(portfolio_greeks.get('total_charm', 0), 1000),
                'vanna_scaled': safe_divide(portfolio_greeks.get('total_vanna', 0), 1000),
                'gex_scaled': safe_divide(portfolio_greeks.get('gex', 0), 1000000),
                'vomma_scaled': safe_divide(portfolio_greeks.get('total_vomma', 0), 1000000),
                'volm_bs_scaled': safe_divide(portfolio_greeks.get('volm_bs', 0), 1000),
                'vx_oi_scaled': safe_divide(portfolio_greeks.get('vx_oi', 0), 1000000)
            }

            return scaled_metrics

        except Exception as e:
            print(f"⚠️ Error scaling Greeks: {e}")
            return {
                'charm_scaled': 0,
                'vanna_scaled': 0,
                'gex_scaled': 0,
                'vomma_scaled': 0,
                'volm_bs_scaled': 0,
                'vx_oi_scaled': 0
            }

    def _calculate_convergence_signals(self, scaled_metrics: dict) -> dict:
        """Calculate convergence signals based on your patterns"""

        # Bearish Recipe: High positive charm + High positive GEX (REALISTIC THRESHOLDS)
        bearish_charm = scaled_metrics['charm_scaled'] > 2    # Based on observed range -3 to 4
        bearish_gex = scaled_metrics['gex_scaled'] > 1000     # Based on observed range 1200-1300
        bearish_signal = int(bearish_charm and bearish_gex)

        # Bullish Recipe: Low vomma + High vanna + Moderate GEX (REALISTIC THRESHOLDS)
        bullish_vomma = scaled_metrics['vomma_scaled'] < 5     # Based on observed positive values ~1.6
        bullish_vanna = scaled_metrics['vanna_scaled'] > 10    # Based on observed range 12-16
        bullish_gex = scaled_metrics['gex_scaled'] > 1000      # Based on observed range 1200-1300
        bullish_signal = int(bullish_vomma and bullish_vanna and bullish_gex)

        # Explosive Recipe: Extreme conditions based on actual data ranges
        explosive_vomma = scaled_metrics['vomma_scaled'] < 2     # Very low vomma
        explosive_vanna = scaled_metrics['vanna_scaled'] > 15    # High vanna
        explosive_gex = scaled_metrics['gex_scaled'] > 1250      # High GEX
        explosive_charm = abs(scaled_metrics['charm_scaled']) > 2  # High absolute charm
        explosive_signal = int(explosive_vomma and explosive_vanna and explosive_gex and explosive_charm)

        # Overall signal strength
        signal_strength = explosive_signal * 3 + bullish_signal * 2 + bearish_signal * (-1)

        # Convergence quality (0-1 scale)
        convergence_quality = min(abs(signal_strength) / 3.0, 1.0)

        return {
            'bearish_signal': bearish_signal,
            'bullish_signal': bullish_signal,
            'explosive_signal': explosive_signal,
            'signal_strength': signal_strength,
            'convergence_quality': convergence_quality
        }

    def generate_convergence_enhanced_signal(self, options_data: pd.DataFrame,
                                           current_price: float, current_date: pd.Timestamp,
                                           overnight_drift: float) -> dict:
        """
        Generate enhanced trading signal using convergence analysis
        """
        # Calculate convergence metrics
        convergence_metrics = self.calculate_convergence_metrics(options_data, current_price, current_date)

        signal_type = 'NEUTRAL'
        signal_strength = 0.0
        rationale = "No convergence signal"
        position_size = 0

        # Extract key metrics
        bearish_signal = convergence_metrics['bearish_signal']
        bullish_signal = convergence_metrics['bullish_signal']
        explosive_signal = convergence_metrics['explosive_signal']
        convergence_quality = convergence_metrics['convergence_quality']
        signal_strength_raw = convergence_metrics['signal_strength']

        # Check minimum convergence quality
        if convergence_quality < self.params['convergence_threshold']:
            # Show near-miss convergence for debugging
            if convergence_quality > 0.1:  # Show if we're getting close
                rationale = f"Near convergence: {convergence_quality:.2f} (need {self.params['convergence_threshold']:.1f})"
            else:
                rationale = f"Low convergence quality: {convergence_quality:.2f}"

            return {
                'signal_type': signal_type,
                'signal_strength': signal_strength,
                'rationale': rationale,
                'position_size': position_size,
                'convergence_metrics': convergence_metrics
            }

        # Generate signals based on convergence patterns
        if explosive_signal:
            # Explosive signal - highest priority
            signal_type = 'BULLISH' if overnight_drift > 0 else 'BEARISH'
            signal_strength = min(convergence_quality * self.params['explosive_multiplier'], 1.0)
            position_size = int(self.params['max_position_size'] * self.params['explosive_multiplier'])
            rationale = f"EXPLOSIVE: {signal_type} convergence (Vomma<-100, Vanna>200, GEX moderate, Charm<0)"

        elif bullish_signal:
            # Bullish convergence signal
            signal_type = 'BULLISH'
            signal_strength = convergence_quality
            position_size = self.params['max_position_size']
            rationale = f"BULLISH: Convergence (Vomma<-50, Vanna>50, GEX<50) + drift {overnight_drift*100:.2f}%"

        elif bearish_signal:
            # Bearish convergence signal
            signal_type = 'BEARISH'
            signal_strength = convergence_quality
            position_size = self.params['max_position_size']
            rationale = f"BEARISH: Convergence (Charm>100, GEX>100) + drift {overnight_drift*100:.2f}%"

        # Enhance with drift confirmation
        if signal_type != 'NEUTRAL':
            # Require drift confirmation for stronger signals
            drift_threshold = self.params['drift_threshold']

            if signal_type == 'BULLISH' and overnight_drift < -drift_threshold:
                # Bullish signal but negative drift - reduce strength
                signal_strength *= 0.5
                rationale += f" (drift conflict: {overnight_drift*100:.2f}%)"
            elif signal_type == 'BEARISH' and overnight_drift > drift_threshold:
                # Bearish signal but positive drift - reduce strength
                signal_strength *= 0.5
                rationale += f" (drift conflict: {overnight_drift*100:.2f}%)"
            elif abs(overnight_drift) > drift_threshold * 2:
                # Strong drift confirmation - boost signal
                signal_strength = min(signal_strength * 1.2, 1.0)
                rationale += f" (strong drift: {overnight_drift*100:.2f}%)"

        # Final position sizing with risk management
        if signal_strength > 0:
            position_size = max(1, min(position_size, self.params['max_position_size']))

        return {
            'signal_type': signal_type,
            'signal_strength': signal_strength,
            'rationale': rationale,
            'position_size': position_size,
            'convergence_metrics': convergence_metrics
        }

    def find_option_strike(self, signal_type: str, current_price: float,
                          signal_date: pd.Timestamp, options_data: pd.DataFrame) -> dict:
        """Find appropriate option strike for trading with proper column mapping"""
        if signal_type == 'NEUTRAL':
            return None

        # Determine target strike based on signal type
        if signal_type == 'BULLISH':
            target_strike = ((int(current_price) // STRIKE_MULTIPLE) + 1) * STRIKE_MULTIPLE
            target_option_type = 'CALL'
        else:
            target_strike = (int(current_price) // STRIKE_MULTIPLE) * STRIKE_MULTIPLE
            target_option_type = 'PUT'

        # Target expiry date (closer expiry for faster decay)
        target_expiry = signal_date + pd.Timedelta(days=self.params['target_expiry_days'])

        # Handle different column names for strike and option type
        strike_col = 'Strike' if 'Strike' in options_data.columns else 'strike'

        if 'Call/Put' in options_data.columns:
            option_type_col = 'Call/Put'
            target_options = options_data[
                (options_data[strike_col] == target_strike) &
                (options_data[option_type_col].str.upper() == target_option_type)
            ].copy()
        elif 'option_type' in options_data.columns:
            option_type_col = 'option_type'
            target_type_code = 'c' if target_option_type == 'CALL' else 'p'
            target_options = options_data[
                (options_data[strike_col] == target_strike) &
                (options_data[option_type_col].str.lower() == target_type_code)
            ].copy()
        else:
            print("⚠️ Warning: No option type column found for strike selection")
            return None

        if target_options.empty:
            return None

        # Handle different expiration column names
        if 'Expiry Date' in target_options.columns:
            expiry_col = 'Expiry Date'
        elif 'expiration' in target_options.columns:
            expiry_col = 'expiration'
        else:
            print("⚠️ Warning: No expiration column found")
            return None

        # Find expiry closest to target
        target_options['expiry_date'] = pd.to_datetime(target_options[expiry_col])
        target_options['days_to_expiry'] = (target_options['expiry_date'] - signal_date).dt.days
        target_options = target_options[target_options['days_to_expiry'] > 0]

        if target_options.empty:
            return None

        # Get closest expiry to target
        closest_expiry_days = target_options['days_to_expiry'].iloc[
            (target_options['days_to_expiry'] - self.params['target_expiry_days']).abs().argsort()[:1]
        ].iloc[0]

        best_option = target_options[
            target_options['days_to_expiry'] == closest_expiry_days
        ].iloc[0]

        # Get entry price with proper column mapping
        entry_price = best_option.get('Last Trade Price', 0)
        if entry_price <= 0:
            bid = best_option.get('Bid Price', best_option.get('bid', 0))
            ask = best_option.get('Ask Price', best_option.get('ask', 0))
            if bid > 0 and ask > 0:
                entry_price = (bid + ask) / 2
            else:
                entry_price = ask if ask > 0 else bid

        if entry_price <= 0:
            return None

        return {
            'strike': best_option[strike_col],
            'expiry': best_option[expiry_col],
            'option_type': best_option[option_type_col],
            'entry_price': entry_price,
            'days_to_expiry': closest_expiry_days
        }


def load_all_spx_options_data(start_year=START_YEAR):
    """Load and combine all SPX options data from optionhistory/{year}_{quarter}_option_chain/spx_complete_{year}_{quarter}.csv"""
    import os
    import glob

    # Look for optionhistory directory
    optionhistory_path = "../optionhistory"
    if not os.path.exists(optionhistory_path):
        print(f"❌ Options history directory not found: {optionhistory_path}")
        return None

    print(f"📁 Found optionhistory directory: {optionhistory_path}")

    # Find all option chain directories
    option_dirs = glob.glob(f"{optionhistory_path}/*_option_chain")
    spx_files = []

    for option_dir in option_dirs:
        # Extract year from directory name
        dir_name = os.path.basename(option_dir)
        try:
            year = int(dir_name.split('_')[0])
            if year >= start_year:
                # Look for spx_complete files
                spx_pattern = f"{option_dir}/spx_complete_*.csv"
                files = glob.glob(spx_pattern)
                if files:
                    spx_files.extend(files)
                    print(f"   📂 Found option chain directory: {dir_name}")
                    for file in files:
                        print(f"      ✅ Found: {os.path.basename(file)}")
        except ValueError:
            continue

    if not spx_files:
        print(f"❌ No SPX complete files found for years >= {start_year}")
        return None

    print(f"📁 Found {len(spx_files)} SPX complete files:")
    for file in spx_files:
        print(f"   📄 {file}")

    # Load and combine all files
    combined_data = []
    for file_path in spx_files:
        try:
            print(f"📊 Loading {os.path.basename(file_path)}...")
            df = pd.read_csv(file_path)
            combined_data.append(df)
            print(f"   ✅ Loaded {len(df):,} records")
        except Exception as e:
            print(f"   ❌ Error loading {file_path}: {e}")

    if not combined_data:
        print("❌ No data could be loaded")
        return None

    # Combine all data
    print(f"\n🔄 Combining {len(combined_data)} files...")
    combined_df = pd.concat(combined_data, ignore_index=True)

    # Remove duplicates
    print("🧹 Removing duplicates...")
    original_len = len(combined_df)
    combined_df = combined_df.drop_duplicates()
    duplicates_removed = original_len - len(combined_df)

    # Convert date column
    combined_df['date'] = pd.to_datetime(combined_df['date'])

    print(f"📊 COMBINED DATASET SUMMARY:")
    print(f"   Total records: {len(combined_df):,} (removed {duplicates_removed:,} duplicates)")
    print(f"   Date range: {combined_df['date'].min()} to {combined_df['date'].max()}")

    # Check for strike column (might be different case)
    strike_col = None
    for col in ['strike', 'Strike', 'STRIKE']:
        if col in combined_df.columns:
            strike_col = col
            break

    if strike_col:
        print(f"   Strike range: {combined_df[strike_col].min()} to {combined_df[strike_col].max()}")
    else:
        print(f"   Available columns: {list(combined_df.columns)}")

    print(f"   Files combined: {len(combined_data)}")

    # Save combined data
    output_path = "data/SPX_COMPLETE_COMBINED.csv"
    os.makedirs("data", exist_ok=True)
    combined_df.to_csv(output_path, index=False)
    print(f"💾 Combined data saved to: {output_path}")

    return output_path


def run_convergence_backtest(start_year: int = 2020):
    """Run the convergence-enhanced cluster strategy backtest"""

    print("🧮 CONVERGENCE-ENHANCED CLUSTER STRATEGY")
    print("=" * 70)
    print("ADVANCED CONVERGENCE FEATURES:")
    print("  📊 Comprehensive Greeks: Delta, Gamma, Vega, Theta, Rho, Vomma, Vanna, Charm")
    print("  🎯 Portfolio-level Greeks aggregation with GEX analysis")
    print("  ⚡ Convergence pattern detection:")
    print("    • BEARISH: High positive Charm + High positive GEX")
    print("    • BULLISH: Negative Vomma + High Vanna + Low GEX")
    print("    • EXPLOSIVE: Extreme negative Vomma + Extreme Vanna + Moderate GEX + Negative Charm")
    print("  📏 Dynamic position sizing based on convergence strength")
    print("  🌙 E-mini drift confirmation for signal enhancement")
    print("  ⏰ Fast exits (1 day) with closer expiry (14 days)")
    print(f"  📅 Data from: {start_year} onwards")
    print("=" * 70)

    # Load all available SPX options data
    combined_data_path = load_all_spx_options_data(start_year)

    # Load the combined data directly
    if combined_data_path and os.path.exists(combined_data_path):
        print("📊 Loading combined options data...")
        raw_data = pd.read_csv(combined_data_path)
        print(f"Loaded {len(raw_data):,} options records from combined file")

        # Ensure date column is datetime
        raw_data['date'] = pd.to_datetime(raw_data['date'])

        # Get trading dates and determine actual data range
        all_trading_dates = sorted(raw_data['date'].unique())
        actual_start_date = all_trading_dates[0]
        actual_end_date = all_trading_dates[-1]

        print(f"📅 Backtest Period: {actual_start_date.strftime('%Y-%m-%d')} to {actual_end_date.strftime('%Y-%m-%d')}")

        # Get date range
        start_date = actual_start_date.strftime('%Y-%m-%d')
        end_date = actual_end_date.strftime('%Y-%m-%d')

    else:
        print("⚠️ Combined data not available, cannot proceed without proper data")
        return None

    print(f"📅 Backtest Period: {start_date} to {end_date}")

    # Initialize strategy with synchronized date range
    strategy = ConvergenceClusterStrategy(start_date, end_date)

    # Run backtest
    print(f"\n💰 Starting Capital: ${INITIAL_CAPITAL:,.2f}")
    print("🎯 Strategy: Convergence-Enhanced Cluster Strategy")

    # Use actual trading dates from data
    trading_dates = all_trading_dates

    # Initialize tracking
    current_capital = INITIAL_CAPITAL
    positions = []
    trades = []
    daily_analysis = []

    print(f"📈 Total trading days: {len(trading_dates)}")

    for i, date in enumerate(trading_dates):
        if i % 50 == 0:
            print(f"  Processing {i+1}/{len(trading_dates)}: {date.strftime('%Y-%m-%d')}")

        # Get options data for this day
        day_options = raw_data[raw_data['date'] == date]

        if day_options.empty:
            continue

        # Get current price from SPX close column
        if 'spx_close' in day_options.columns:
            current_price = day_options['spx_close'].iloc[0]
        else:
            print(f"⚠️ Warning: No spx_close column found for {date}")
            continue

        # Get overnight drift - simple calculation as fallback
        overnight_drift = 0.0
        if strategy.emini_analyzer:
            try:
                overnight_drift = strategy.emini_analyzer.get_overnight_drift(date.strftime('%Y-%m-%d'))
            except:
                pass
        else:
            # Simple drift calculation using previous day's close
            if i > 0:
                prev_date = trading_dates[i-1]
                prev_day_options = raw_data[raw_data['date'] == prev_date]
                if not prev_day_options.empty and 'spx_close' in prev_day_options.columns:
                    prev_price = prev_day_options['spx_close'].iloc[0]
                    overnight_drift = (current_price - prev_price) / prev_price

        # Generate convergence-enhanced signal
        signal = strategy.generate_convergence_enhanced_signal(day_options, current_price, date, overnight_drift)

        # Debug output for first few days
        if i < 3:
            conv_metrics = signal['convergence_metrics']
            print(f"    🔍 Day {i+1}: Signal={signal['signal_type']}, Strength={signal['signal_strength']:.3f}")
            print(f"        Convergence: Bearish={conv_metrics['bearish_signal']}, Bullish={conv_metrics['bullish_signal']}, Explosive={conv_metrics['explosive_signal']}")
            print(f"        Greeks: Charm={conv_metrics['charm_scaled']:.1f}, Vanna={conv_metrics['vanna_scaled']:.1f}, GEX={conv_metrics['gex_scaled']:.1f}, Vomma={conv_metrics['vomma_scaled']:.1f}")
            print(f"        Drift: {overnight_drift*100:.2f}%, Rationale: {signal['rationale']}")

        # Process existing positions (exit logic)
        for position in positions[:]:
            days_held = (date - position['entry_date']).days

            if days_held >= strategy.params['max_hold_days']:
                # Exit position
                exit_option = strategy.find_option_strike(
                    position['signal_type'], current_price, date, day_options
                )

                if exit_option:
                    exit_price = exit_option['entry_price']
                else:
                    exit_price = position['entry_price'] * 0.5  # Assume 50% loss if can't find

                # Calculate P&L
                pnl = (exit_price - position['entry_price']) * SPX_MULTIPLIER * position['position_size']
                current_capital += pnl

                # Record trade
                trade = {
                    'entry_date': position['entry_date'],
                    'exit_date': date,
                    'signal_type': position['signal_type'],
                    'strike': position['strike'],
                    'expiry': position['expiry'],
                    'option_type': position['option_type'],
                    'entry_price': position['entry_price'],
                    'exit_price': exit_price,
                    'position_size': position['position_size'],
                    'pnl': pnl,
                    'exit_reason': f"MAX_HOLD_CLOSE ({days_held} days)",
                    'days_held': days_held,
                    'convergence_quality': position.get('convergence_quality', 0),
                    'signal_strength': position.get('signal_strength', 0),
                    'convergence_type': position.get('convergence_type', 'unknown')
                }
                trades.append(trade)

                # Remove position
                positions.remove(position)

                if i < 5:  # Debug for first few days
                    result = "WIN" if pnl > 0 else "LOSS"
                    print(f"    🎉 {result}: {position['option_type']} ${position['strike']} = ${pnl:,.0f} (Conv: {position.get('convergence_type', 'N/A')})")

        # Entry logic - only high convergence quality signals
        if signal['signal_type'] != 'NEUTRAL' and len(positions) < 2:  # Max 2 positions for risk management
            option_details = strategy.find_option_strike(
                signal['signal_type'], current_price, date, day_options
            )

            if option_details:
                # Calculate position size with strict risk management
                contracts = signal['position_size']
                max_risk_dollars = current_capital * strategy.params['max_risk_per_trade']
                max_contracts_by_risk = int(max_risk_dollars / (100 * option_details['entry_price']))
                contracts = min(contracts, max(1, max_contracts_by_risk))

                # Determine convergence type for tracking
                conv_metrics = signal['convergence_metrics']
                if conv_metrics['explosive_signal']:
                    convergence_type = 'EXPLOSIVE'
                elif conv_metrics['bullish_signal']:
                    convergence_type = 'BULLISH'
                elif conv_metrics['bearish_signal']:
                    convergence_type = 'BEARISH'
                else:
                    convergence_type = 'NEUTRAL'

                # Create new position
                new_position = {
                    'entry_date': date + pd.Timedelta(days=1),  # Enter next day
                    'signal_date': date,
                    'entry_price': option_details['entry_price'],
                    'strike': option_details['strike'],
                    'expiry': option_details['expiry'],
                    'option_type': option_details['option_type'],
                    'signal_type': signal['signal_type'],
                    'position_size': contracts,
                    'signal_strength': signal['signal_strength'],
                    'rationale': signal['rationale'],
                    'convergence_quality': conv_metrics['convergence_quality'],
                    'convergence_type': convergence_type
                }

                positions.append(new_position)

                if i < 5:  # Debug for first few days
                    print(f"    📈 ENTRY: {option_details['option_type']} ${option_details['strike']} at ${option_details['entry_price']:.2f}, {contracts} contracts ({convergence_type})")

        # Store daily analysis
        conv_metrics = signal.get('convergence_metrics', {})
        daily_analysis.append({
            'date': date,
            'current_price': current_price,
            'overnight_drift': overnight_drift,
            'signal_type': signal['signal_type'],
            'signal_strength': signal['signal_strength'],
            'convergence_quality': conv_metrics.get('convergence_quality', 0),
            'bearish_signal': conv_metrics.get('bearish_signal', 0),
            'bullish_signal': conv_metrics.get('bullish_signal', 0),
            'explosive_signal': conv_metrics.get('explosive_signal', 0),
            'charm_scaled': conv_metrics.get('charm_scaled', 0),
            'vanna_scaled': conv_metrics.get('vanna_scaled', 0),
            'gex_scaled': conv_metrics.get('gex_scaled', 0),
            'vomma_scaled': conv_metrics.get('vomma_scaled', 0),
            'active_positions': len(positions),
            'current_capital': current_capital
        })

    # Calculate results
    if trades:
        trades_df = pd.DataFrame(trades)
        winning_trades = trades_df[trades_df['pnl'] > 0]
        losing_trades = trades_df[trades_df['pnl'] <= 0]

        total_trades = len(trades_df)
        win_rate = len(winning_trades) / total_trades * 100
        total_pnl = trades_df['pnl'].sum()
        total_return = total_pnl / INITIAL_CAPITAL * 100

        avg_win = winning_trades['pnl'].mean() if not winning_trades.empty else 0
        avg_loss = losing_trades['pnl'].mean() if not losing_trades.empty else 0

        profit_factor = abs(winning_trades['pnl'].sum() / losing_trades['pnl'].sum()) if not losing_trades.empty and losing_trades['pnl'].sum() != 0 else float('inf')

        # Convergence analysis
        explosive_trades = trades_df[trades_df['convergence_type'] == 'EXPLOSIVE']
        bullish_trades = trades_df[trades_df['convergence_type'] == 'BULLISH']
        bearish_trades = trades_df[trades_df['convergence_type'] == 'BEARISH']

        avg_convergence_quality = trades_df['convergence_quality'].mean()

        print(f"\n🧮 CONVERGENCE-ENHANCED CLUSTER STRATEGY RESULTS")
        print("=" * 70)
        print(f"Total Trades: {total_trades}")
        print(f"Winning Trades: {len(winning_trades)}")
        print(f"Win Rate: {win_rate:.1f}%")
        print(f"Total P&L: ${total_pnl:,.2f}")
        print(f"Total Return: {total_return:.1f}%")
        print(f"Final Capital: ${current_capital:,.2f}")
        print(f"Average Win: ${avg_win:,.0f}")
        print(f"Average Loss: ${avg_loss:,.0f}")
        print(f"Profit Factor: {profit_factor:.2f}")
        print(f"\n📊 CONVERGENCE ANALYSIS:")
        print(f"Average Convergence Quality: {avg_convergence_quality:.2f}")
        print(f"Explosive Trades: {len(explosive_trades)} (Avg P&L: ${explosive_trades['pnl'].mean():.0f})" if not explosive_trades.empty else "Explosive Trades: 0")
        print(f"Bullish Trades: {len(bullish_trades)} (Avg P&L: ${bullish_trades['pnl'].mean():.0f})" if not bullish_trades.empty else "Bullish Trades: 0")
        print(f"Bearish Trades: {len(bearish_trades)} (Avg P&L: ${bearish_trades['pnl'].mean():.0f})" if not bearish_trades.empty else "Bearish Trades: 0")

        return {
            'trades': trades_df,
            'daily_analysis': pd.DataFrame(daily_analysis),
            'total_return': total_return,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'profit_factor': profit_factor,
            'avg_convergence_quality': avg_convergence_quality,
            'explosive_trades': len(explosive_trades),
            'bullish_trades': len(bullish_trades),
            'bearish_trades': len(bearish_trades)
        }

    else:
        print("❌ No trades executed!")
        return None


if __name__ == "__main__":
    results = run_convergence_backtest(START_YEAR)