#!/usr/bin/env python3
"""
Test script to verify signal generation with lowered thresholds
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')

from constants import (
    DRIFT_THRESHOLD, WALL_STRENGTH_THRESHOLD, VOLUME_PERCENTILE,
    WEAK_SIGNAL_THRESHOLD, MODERATE_SIGNAL_THRESHOLD, STRONG_SIGNAL_THRESHOLD,
    MIN_SIGNAL_STRENGTH
)
from greeks_analytics_engine import GreeksAnalyticsEngine, ClusterGreeksAnalyzer, AdvancedSignalGenerator

def test_threshold_values():
    """Test that our threshold values have been lowered"""
    print("=== TESTING THRESHOLD VALUES ===")
    print(f"DRIFT_THRESHOLD: {DRIFT_THRESHOLD} (should be 0.002)")
    print(f"WALL_STRENGTH_THRESHOLD: {WALL_STRENGTH_THRESHOLD} (should be 1.0)")
    print(f"VOLUME_PERCENTILE: {VOLUME_PERCENTILE} (should be 60)")
    print(f"WEAK_SIGNAL_THRESHOLD: {WEAK_SIGNAL_THRESHOLD} (should be 0.15)")
    print(f"MODERATE_SIGNAL_THRESHOLD: {MODERATE_SIGNAL_THRESHOLD} (should be 0.4)")
    print(f"STRONG_SIGNAL_THRESHOLD: {STRONG_SIGNAL_THRESHOLD} (should be 0.65)")
    print(f"MIN_SIGNAL_STRENGTH: {MIN_SIGNAL_STRENGTH} (should be 0.05)")
    print()

def create_sample_options_data():
    """Create sample options data for testing"""
    np.random.seed(42)  # For reproducible results
    
    current_price = 4500
    strikes = np.arange(4200, 4800, 25)  # Strikes from 4200 to 4775
    
    options_data = []
    for strike in strikes:
        for option_type in ['call', 'put']:
            # Create realistic volume distribution
            distance_from_money = abs(strike - current_price)
            base_volume = max(100, 2000 - distance_from_money * 2)
            volume = int(np.random.normal(base_volume, base_volume * 0.3))
            volume = max(10, volume)
            
            # Create realistic pricing
            if option_type == 'call':
                intrinsic = max(0, current_price - strike)
            else:
                intrinsic = max(0, strike - current_price)
            
            time_value = max(5, 50 - distance_from_money * 0.1)
            mid_price = intrinsic + time_value + np.random.normal(0, 5)
            mid_price = max(1, mid_price)
            
            options_data.append({
                'strike': strike,
                'option_type': option_type,
                'volume': volume,
                'mid_price': mid_price,
                'expiry_days': 30,  # 30 days to expiry
                'underlying_price': current_price
            })
    
    return pd.DataFrame(options_data)

def test_signal_generation():
    """Test signal generation with sample data"""
    print("=== TESTING SIGNAL GENERATION ===")
    
    # Create sample data
    options_data = create_sample_options_data()
    current_price = 4500
    current_date = pd.Timestamp('2024-01-15')
    
    # Test different drift scenarios
    drift_scenarios = [
        0.001,   # Small positive drift
        -0.001,  # Small negative drift
        0.003,   # Medium positive drift
        -0.003,  # Medium negative drift
        0.0001,  # Very small drift
        -0.0001, # Very small negative drift
    ]
    
    # Initialize Greeks engine
    greeks_engine = GreeksAnalyticsEngine()
    cluster_analyzer = ClusterGreeksAnalyzer(greeks_engine)
    signal_generator = AdvancedSignalGenerator(cluster_analyzer)
    
    print(f"Testing with {len(options_data)} options contracts")
    print(f"Current price: {current_price}")
    print()
    
    signals_generated = 0
    
    for i, drift in enumerate(drift_scenarios):
        print(f"--- Scenario {i+1}: Drift = {drift:.4f} ({drift*100:.2f}%) ---")
        
        try:
            # Generate signal
            signal = signal_generator.generate_enhanced_signal(
                options_data, current_price, current_date, drift
            )
            
            if signal and signal.get('signal_type') != 'NEUTRAL':
                signals_generated += 1
                print(f"✅ SIGNAL GENERATED!")
                print(f"   Type: {signal['signal_type']}")
                print(f"   Strength: {signal['signal_strength']:.3f}")
                print(f"   Rationale: {signal['rationale']}")
                
                # Show threshold details
                if 'thresholds' in signal:
                    thresholds = signal['thresholds']
                    print(f"   Thresholds used:")
                    print(f"     - Vanna: {thresholds.get('vanna_threshold', 'N/A'):.3f}")
                    print(f"     - Charm: {thresholds.get('charm_threshold', 'N/A'):.3f}")
                    print(f"     - Vomma: {thresholds.get('vomma_threshold', 'N/A'):.3f}")
                    print(f"     - Wall: {thresholds.get('wall_strength_threshold', 'N/A'):.3f}")
            else:
                print(f"❌ No signal generated")
                if signal:
                    print(f"   Rationale: {signal.get('rationale', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ Error generating signal: {e}")
        
        print()
    
    print(f"=== SUMMARY ===")
    print(f"Signals generated: {signals_generated}/{len(drift_scenarios)}")
    print(f"Signal rate: {signals_generated/len(drift_scenarios)*100:.1f}%")
    
    if signals_generated == 0:
        print("⚠️  WARNING: No signals generated! Thresholds may still be too high.")
    elif signals_generated < len(drift_scenarios) * 0.5:
        print("⚠️  WARNING: Low signal generation rate. Consider lowering thresholds further.")
    else:
        print("✅ Good signal generation rate!")

if __name__ == "__main__":
    test_threshold_values()
    test_signal_generation()
