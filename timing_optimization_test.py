"""
Simplified Timing Optimization Test
Tests different entry/exit timing combinations on the existing 82-trade strategy
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def load_existing_trades():
    """Load the existing 82 trades from the enhanced regime-based strategy"""
    try:
        trades_df = pd.read_csv('reports/cluster_trades_refactored.csv')
        print(f"✅ Loaded {len(trades_df)} existing trades")
        return trades_df
    except FileNotFoundError:
        print("❌ No existing trades file found. Run cluster_strategy_refactored.py first.")
        return None

def simulate_timing_strategy(trades_df, timing_strategy):
    """
    Simulate different timing strategies on existing trades
    
    Args:
        trades_df: DataFrame with existing trades
        timing_strategy: Dict with entry_time and exit_time
    
    Returns:
        Modified trades with new timing-based performance
    """
    
    print(f"🕐 Testing {timing_strategy['name']}")
    
    # Create copy of trades
    modified_trades = trades_df.copy()
    
    # Apply timing-based price adjustments
    for idx, trade in modified_trades.iterrows():
        
        # Simulate timing effects on entry and exit prices
        entry_adjustment = get_timing_adjustment(timing_strategy['entry_time'], 'entry')
        exit_adjustment = get_timing_adjustment(timing_strategy['exit_time'], 'exit')
        
        # Adjust prices based on timing
        original_entry = trade['entry_price']
        original_exit = trade['exit_price']
        
        # Apply timing adjustments (based on typical intraday patterns)
        adjusted_entry = original_entry * (1 + entry_adjustment)
        adjusted_exit = original_exit * (1 + exit_adjustment)
        
        # Recalculate P&L
        price_change = adjusted_exit - adjusted_entry
        new_pnl = price_change * 100 * trade['position_size']  # SPX multiplier
        
        # Update trade
        modified_trades.at[idx, 'entry_price'] = adjusted_entry
        modified_trades.at[idx, 'exit_price'] = adjusted_exit
        modified_trades.at[idx, 'final_pnl'] = new_pnl
        modified_trades.at[idx, 'timing_strategy'] = timing_strategy['name']
    
    return modified_trades

def get_timing_adjustment(timing_type, trade_phase):
    """
    Get timing adjustment factor based on typical market patterns
    
    Args:
        timing_type: 'open' or 'close'
        trade_phase: 'entry' or 'exit'
    
    Returns:
        Adjustment factor (positive/negative percentage)
    """
    
    # Based on empirical options trading patterns:
    # - Opening prices tend to be more volatile
    # - Closing prices tend to be more stable
    # - Options premiums vary throughout the day
    
    adjustments = {
        'open': {
            'entry': np.random.normal(-0.02, 0.05),  # Slightly worse entry at open (higher premium)
            'exit': np.random.normal(0.01, 0.04)     # Slightly better exit at open (volatility premium)
        },
        'close': {
            'entry': np.random.normal(0.01, 0.03),   # Better entry at close (lower premium)
            'exit': np.random.normal(-0.01, 0.03)    # Slightly worse exit at close (time decay)
        }
    }
    
    return adjustments[timing_type][trade_phase]

def calculate_timing_performance(trades_df, timing_strategy_name):
    """Calculate performance metrics for timing strategy"""
    
    if trades_df.empty:
        return {}
    
    # Basic metrics
    total_trades = len(trades_df)
    winning_trades = trades_df[trades_df['final_pnl'] > 0]
    losing_trades = trades_df[trades_df['final_pnl'] <= 0]
    
    win_rate = (len(winning_trades) / total_trades) * 100
    total_pnl = trades_df['final_pnl'].sum()
    total_return = (total_pnl / 100000.0) * 100  # Starting capital = 100k
    
    # Win/Loss metrics
    avg_win = winning_trades['final_pnl'].mean() if len(winning_trades) > 0 else 0.0
    avg_loss = losing_trades['final_pnl'].mean() if len(losing_trades) > 0 else 0.0
    
    # Profit factor
    gross_profit = winning_trades['final_pnl'].sum() if len(winning_trades) > 0 else 0.0
    gross_loss = abs(losing_trades['final_pnl'].sum()) if len(losing_trades) > 0 else 0.0
    profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0.0
    
    # Drawdown calculation
    trades_df_sorted = trades_df.sort_values('entry_date')
    trades_df_sorted['cumulative_pnl'] = trades_df_sorted['final_pnl'].cumsum()
    trades_df_sorted['running_peak'] = trades_df_sorted['cumulative_pnl'].expanding().max()
    trades_df_sorted['drawdown'] = trades_df_sorted['cumulative_pnl'] - trades_df_sorted['running_peak']
    max_drawdown = abs(trades_df_sorted['drawdown'].min()) / 100000.0 * 100
    
    return {
        'strategy': timing_strategy_name,
        'total_trades': total_trades,
        'win_rate': win_rate,
        'total_return': total_return,
        'max_drawdown': max_drawdown,
        'profit_factor': profit_factor,
        'avg_win': avg_win,
        'avg_loss': avg_loss,
        'gross_profit': gross_profit,
        'gross_loss': gross_loss
    }

def run_timing_optimization():
    """Run comprehensive timing optimization test"""
    
    print("🎯 TIMING OPTIMIZATION FOR ENHANCED REGIME-BASED OPTIONS PLAYBOOK")
    print("=" * 80)
    
    # Load existing trades
    original_trades = load_existing_trades()
    if original_trades is None:
        return
    
    # Define timing strategies to test
    timing_strategies = [
        {
            'name': 'Open-to-Close (Current)',
            'entry_time': 'open',
            'exit_time': 'close',
            'description': 'Enter at market open, exit at market close'
        },
        {
            'name': 'Close-to-Close',
            'entry_time': 'close',
            'exit_time': 'close',
            'description': 'Enter at market close, exit at market close'
        },
        {
            'name': 'Close-to-Open',
            'entry_time': 'close',
            'exit_time': 'open',
            'description': 'Enter at market close, exit at next market open'
        },
        {
            'name': 'Open-to-Open',
            'entry_time': 'open',
            'exit_time': 'open',
            'description': 'Enter at market open, exit at next market open'
        }
    ]
    
    # Calculate baseline performance (current strategy)
    baseline_performance = calculate_timing_performance(original_trades, 'Baseline (Current)')
    
    print(f"\n📊 BASELINE PERFORMANCE (Current Strategy):")
    print(f"   Total Trades: {baseline_performance['total_trades']}")
    print(f"   Win Rate: {baseline_performance['win_rate']:.1f}%")
    print(f"   Total Return: {baseline_performance['total_return']:.1f}%")
    print(f"   Max Drawdown: {baseline_performance['max_drawdown']:.1f}%")
    print(f"   Profit Factor: {baseline_performance['profit_factor']:.2f}")
    
    # Test each timing strategy
    results = []
    results.append(baseline_performance)
    
    print(f"\n🔄 TESTING TIMING STRATEGIES:")
    print("-" * 60)
    
    for strategy in timing_strategies:
        print(f"\n🕐 Testing: {strategy['name']}")
        
        # Run multiple simulations to account for randomness
        strategy_results = []
        
        for sim in range(10):  # 10 simulations per strategy
            modified_trades = simulate_timing_strategy(original_trades, strategy)
            performance = calculate_timing_performance(modified_trades, strategy['name'])
            strategy_results.append(performance)
        
        # Average the results
        avg_performance = {
            'strategy': strategy['name'],
            'total_trades': strategy_results[0]['total_trades'],
            'win_rate': np.mean([r['win_rate'] for r in strategy_results]),
            'total_return': np.mean([r['total_return'] for r in strategy_results]),
            'max_drawdown': np.mean([r['max_drawdown'] for r in strategy_results]),
            'profit_factor': np.mean([r['profit_factor'] for r in strategy_results]),
            'avg_win': np.mean([r['avg_win'] for r in strategy_results]),
            'avg_loss': np.mean([r['avg_loss'] for r in strategy_results]),
            'win_rate_std': np.std([r['win_rate'] for r in strategy_results]),
            'return_std': np.std([r['total_return'] for r in strategy_results])
        }
        
        results.append(avg_performance)
        
        print(f"   Win Rate: {avg_performance['win_rate']:.1f}% (±{avg_performance['win_rate_std']:.1f}%)")
        print(f"   Return: {avg_performance['total_return']:.1f}% (±{avg_performance['return_std']:.1f}%)")
        print(f"   Max DD: {avg_performance['max_drawdown']:.1f}%")
        print(f"   Profit Factor: {avg_performance['profit_factor']:.2f}")
    
    # Analyze results
    results_df = pd.DataFrame(results)
    results_df_sorted = results_df.sort_values('win_rate', ascending=False)
    
    print(f"\n🏆 TIMING STRATEGY RANKINGS (by Win Rate):")
    print("=" * 80)
    
    for idx, row in results_df_sorted.iterrows():
        improvement = row['win_rate'] - baseline_performance['win_rate']
        improvement_str = f"(+{improvement:.1f}%)" if improvement > 0 else f"({improvement:.1f}%)"
        
        print(f"{idx+1:2d}. {row['strategy']:<25} | "
              f"Win Rate: {row['win_rate']:5.1f}% {improvement_str:>8s} | "
              f"Return: {row['total_return']:6.1f}% | "
              f"Max DD: {row['max_drawdown']:5.1f}% | "
              f"PF: {row['profit_factor']:4.2f}")
    
    # Identify best strategy
    best_strategy = results_df_sorted.iloc[0]
    win_rate_improvement = best_strategy['win_rate'] - baseline_performance['win_rate']
    
    print(f"\n🥇 BEST TIMING STRATEGY: {best_strategy['strategy']}")
    print(f"   Win Rate: {best_strategy['win_rate']:.1f}% (improvement: +{win_rate_improvement:.1f}%)")
    print(f"   Total Return: {best_strategy['total_return']:.1f}%")
    print(f"   Max Drawdown: {best_strategy['max_drawdown']:.1f}%")
    print(f"   Profit Factor: {best_strategy['profit_factor']:.2f}")
    
    # Save results
    results_df.to_csv('reports/timing_optimization_results.csv', index=False)
    print(f"\n💾 Results saved to: reports/timing_optimization_results.csv")
    
    return results_df_sorted

if __name__ == "__main__":
    results = run_timing_optimization()
    print("\n✅ Timing optimization test completed!")
