"""
Data loader module for JPM Collar Strategy
Handles loading and preprocessing of options chain data
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class OptionsDataLoader:
    """Data loader for options chain data"""
    
    def __init__(self, data_path: str):
        """Initialize data loader with path to options data"""
        self.data_path = data_path
        self.raw_data = None
        self.processed_data = None
        self.column_names = [
            'date', 'strike', 'expiration', 'option_type', 'price',
            'bid', 'ask', 'volume', 'open_interest', 'delta',
            'gamma', 'theta', 'vega', 'rho', 'extra1', 'extra2'
        ]

        # Cache for underlying prices
        self.underlying_prices = {}

        # Initialize E-mini overnight analyzer for drift calculation
        self.emini_analyzer = None
        self._initialize_emini_analyzer()
    
    def load_raw_data(self) -> pd.DataFrame:
        """Load raw options data from file"""
        try:
            # Check if file has headers by reading first line
            with open(self.data_path, 'r') as f:
                first_line = f.readline().strip()

            # If first line contains 'date' or 'strike', it's likely a header
            has_headers = 'date' in first_line.lower() or 'strike' in first_line.lower()

            if has_headers:
                # Load with headers (for combined CSV files)
                self.raw_data = pd.read_csv(self.data_path)
                print(f"Loaded CSV with headers. Columns: {list(self.raw_data.columns)}")
            else:
                # Load without headers (for original format)
                self.raw_data = pd.read_csv(
                    self.data_path,
                    header=None,
                    names=self.column_names
                )
                print(f"Loaded CSV without headers using predefined column names")

            # Convert date columns to datetime
            self.raw_data['date'] = pd.to_datetime(self.raw_data['date'])
            self.raw_data['expiration'] = pd.to_datetime(self.raw_data['expiration'])

            # Calculate days to expiration
            self.raw_data['dte'] = (self.raw_data['expiration'] - self.raw_data['date']).dt.days

            # Clean numeric columns (handle different column names)
            numeric_cols = ['strike', 'bid', 'ask', 'volume', 'open_interest',
                          'delta', 'gamma', 'theta', 'vega', 'rho']

            # Add 'price' column if it exists, otherwise use 'Last Trade Price'
            if 'price' in self.raw_data.columns:
                numeric_cols.append('price')
            elif 'Last Trade Price' in self.raw_data.columns:
                self.raw_data['price'] = self.raw_data['Last Trade Price']
                numeric_cols.append('price')

            for col in numeric_cols:
                if col in self.raw_data.columns:
                    self.raw_data[col] = pd.to_numeric(self.raw_data[col], errors='coerce')

            print(f"Loaded {len(self.raw_data)} options records")
            print(f"Date range: {self.raw_data['date'].min()} to {self.raw_data['date'].max()}")
            print(f"Strike range: {self.raw_data['strike'].min()} to {self.raw_data['strike'].max()}")

            return self.raw_data

        except Exception as e:
            print(f"Error loading data: {e}")
            return None
    
    def get_options_by_date(self, date: str, option_type: str = 'both') -> pd.DataFrame:
        """Get options data for a specific date"""
        if self.raw_data is None:
            self.load_raw_data()
        
        date_filter = self.raw_data['date'] == pd.to_datetime(date)
        
        if option_type == 'both':
            return self.raw_data[date_filter].copy()
        else:
            type_filter = self.raw_data['option_type'] == option_type
            return self.raw_data[date_filter & type_filter].copy()
    
    def get_options_by_expiration(self, expiration: str, option_type: str = 'both') -> pd.DataFrame:
        """Get options data for a specific expiration"""
        if self.raw_data is None:
            self.load_raw_data()
        
        exp_filter = self.raw_data['expiration'] == pd.to_datetime(expiration)
        
        if option_type == 'both':
            return self.raw_data[exp_filter].copy()
        else:
            type_filter = self.raw_data['option_type'] == option_type
            return self.raw_data[exp_filter & type_filter].copy()
    
    def get_atm_options(self, date: str, underlying_price: float, 
                       tolerance: float = 50) -> pd.DataFrame:
        """Get at-the-money options within tolerance"""
        date_data = self.get_options_by_date(date)
        
        # Find strikes within tolerance of underlying price
        strike_diff = abs(date_data['strike'] - underlying_price)
        atm_filter = strike_diff <= tolerance
        
        return date_data[atm_filter].copy()
    
    def calculate_greeks_aggregates(self, data: pd.DataFrame) -> Dict[str, float]:
        """Calculate aggregate Greeks metrics"""
        if data.empty:
            return {}
        
        # Separate calls and puts
        calls = data[data['option_type'] == 'c']
        puts = data[data['option_type'] == 'p']
        
        # Calculate weighted Greeks by open interest
        def weighted_greek(df, greek_col):
            if df.empty or df['open_interest'].sum() == 0:
                return 0
            weights = df['open_interest'] / df['open_interest'].sum()
            return (df[greek_col] * weights).sum()
        
        # Calculate Vanna (delta sensitivity to volatility)
        # Approximated as delta * vega / 100
        data['vanna'] = data['delta'] * data['vega'] / 100
        
        # Calculate Charm (delta decay)
        # Approximated as theta * delta
        data['charm'] = data['theta'] * data['delta']
        
        # Calculate Vomma (vega sensitivity to volatility)
        # Approximated as vega * vega / 100
        data['vomma'] = data['vega'] * data['vega'] / 100
        
        # Calculate Zomma (gamma sensitivity to volatility)
        # Approximated as gamma * vega / 100
        data['zomma'] = data['gamma'] * data['vega'] / 100
        
        aggregates = {
            'total_volume': data['volume'].sum(),
            'total_open_interest': data['open_interest'].sum(),
            'call_volume': calls['volume'].sum(),
            'put_volume': puts['volume'].sum(),
            'call_oi': calls['open_interest'].sum(),
            'put_oi': puts['open_interest'].sum(),
            'net_delta': weighted_greek(data, 'delta'),
            'net_gamma': weighted_greek(data, 'gamma'),
            'net_theta': weighted_greek(data, 'theta'),
            'net_vega': weighted_greek(data, 'vega'),
            'net_vanna': weighted_greek(data, 'vanna'),
            'net_charm': weighted_greek(data, 'charm'),
            'net_vomma': weighted_greek(data, 'vomma'),
            'net_zomma': weighted_greek(data, 'zomma'),
            'put_call_ratio': puts['volume'].sum() / max(calls['volume'].sum(), 1),
            'put_call_oi_ratio': puts['open_interest'].sum() / max(calls['open_interest'].sum(), 1)
        }
        
        return aggregates
    
    def get_collar_strikes(self, date: str, underlying_price: float, 
                          width_percent: float = 0.05) -> Dict[str, float]:
        """Get collar strike prices based on underlying price and width"""
        
        # Calculate collar strikes
        call_strike = underlying_price * (1 + width_percent)
        put_strike_long = underlying_price * (1 - width_percent)
        put_strike_short = underlying_price * (1 - 2 * width_percent)
        
        # Round to nearest available strikes
        date_data = self.get_options_by_date(date)
        available_strikes = sorted(date_data['strike'].unique())
        
        def find_nearest_strike(target_strike, strikes):
            return min(strikes, key=lambda x: abs(x - target_strike))
        
        collar_strikes = {
            'call_strike': find_nearest_strike(call_strike, available_strikes),
            'put_long_strike': find_nearest_strike(put_strike_long, available_strikes),
            'put_short_strike': find_nearest_strike(put_strike_short, available_strikes),
            'underlying_price': underlying_price
        }
        
        return collar_strikes
    
    def get_quarterly_expirations(self) -> List[str]:
        """Get quarterly expiration dates from the data"""
        if self.raw_data is None:
            self.load_raw_data()
        
        # Get unique expirations and filter for quarterly (typically 3rd Friday)
        expirations = self.raw_data['expiration'].unique()
        quarterly_exps = []
        
        for exp in sorted(expirations):
            exp_date = pd.to_datetime(exp)
            # Check if it's near end of quarter (March, June, September, December)
            if exp_date.month in [3, 6, 9, 12]:
                # Check if it's the 3rd Friday (typical quarterly expiration)
                if exp_date.day >= 15 and exp_date.day <= 21 and exp_date.weekday() == 4:
                    quarterly_exps.append(exp_date.strftime('%Y-%m-%d'))
        
        return quarterly_exps
    
    def get_data_summary(self) -> Dict[str, any]:
        """Get summary statistics of the loaded data"""
        if self.raw_data is None:
            self.load_raw_data()
        
        summary = {
            'total_records': len(self.raw_data),
            'date_range': {
                'start': self.raw_data['date'].min().strftime('%Y-%m-%d'),
                'end': self.raw_data['date'].max().strftime('%Y-%m-%d')
            },
            'unique_dates': self.raw_data['date'].nunique(),
            'unique_expirations': self.raw_data['expiration'].nunique(),
            'strike_range': {
                'min': self.raw_data['strike'].min(),
                'max': self.raw_data['strike'].max()
            },
            'option_types': self.raw_data['option_type'].value_counts().to_dict(),
            'avg_daily_volume': self.raw_data.groupby('date')['volume'].sum().mean(),
            'avg_daily_oi': self.raw_data.groupby('date')['open_interest'].sum().mean()
        }
        
        return summary

    def extract_underlying_price(self, options_data: pd.DataFrame, date: pd.Timestamp) -> float:
        """
        Extract underlying price from options data using SPX price columns or deep ITM calls
        Priority: underlying_close > underlying_open > underlying_high > underlying_low > deep ITM calls
        """

        # Check cache first
        date_str = date.strftime('%Y-%m-%d')
        if date_str in self.underlying_prices:
            return self.underlying_prices[date_str]

        # Method 0: Use underlying price columns directly (most reliable when available)
        for col in ['underlying_close', 'underlying_open', 'underlying_high', 'underlying_low']:
            if col in options_data.columns:
                price_series = options_data[col].dropna()
                if not price_series.empty:
                    price = price_series.iloc[0]
                    if pd.notna(price) and price > 0:
                        underlying_price = float(price)
                        self.underlying_prices[date_str] = underlying_price
                        return underlying_price

        # Method 1: Deep ITM calls (fallback method)
        deep_itm_calls = options_data[
            (options_data['option_type'] == 'c') &
            (options_data['strike'] <= 500) &  # Very deep ITM
            (options_data['price'] > 1000)     # Significant intrinsic value
        ]

        if not deep_itm_calls.empty:
            # For deep ITM calls: underlying ≈ strike + option_price
            deep_itm_calls = deep_itm_calls.copy()
            deep_itm_calls['implied_underlying'] = deep_itm_calls['strike'] + deep_itm_calls['price']

            # Filter for reasonable range (SPX typically 3000-6000)
            reasonable_prices = deep_itm_calls[
                (deep_itm_calls['implied_underlying'] >= 3000) &
                (deep_itm_calls['implied_underlying'] <= 6000)
            ]

            if not reasonable_prices.empty:
                underlying_price = reasonable_prices['implied_underlying'].median()
                self.underlying_prices[date_str] = underlying_price
                return underlying_price

        # Method 2: Put-call parity (backup method)
        calls = options_data[options_data['option_type'] == 'c']
        puts = options_data[options_data['option_type'] == 'p']

        if not calls.empty and not puts.empty:
            # Find common strikes
            common_strikes = set(calls['strike']) & set(puts['strike'])

            if common_strikes:
                implied_prices = []
                for strike in list(common_strikes)[:10]:  # Test first 10 strikes
                    call_data = calls[calls['strike'] == strike]
                    put_data = puts[puts['strike'] == strike]

                    if not call_data.empty and not put_data.empty:
                        call_price = call_data['price'].iloc[0]
                        put_price = put_data['price'].iloc[0]

                        # Put-call parity: S = C - P + K (simplified, ignoring interest rate)
                        implied_underlying = call_price - put_price + strike

                        if 3000 <= implied_underlying <= 6000:
                            implied_prices.append(implied_underlying)

                if implied_prices:
                    underlying_price = np.median(implied_prices)
                    self.underlying_prices[date_str] = underlying_price
                    return underlying_price

        # Method 3: Fallback to median strike (original flawed method)
        underlying_price = np.median(options_data['strike'].unique())
        self.underlying_prices[date_str] = underlying_price
        return underlying_price

    def get_underlying_price_series(self, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        Get a time series of underlying prices extracted from options data
        """

        if self.raw_data is None:
            self.load_raw_data()

        # Get date range
        all_dates = sorted(self.raw_data['date'].unique())

        if start_date:
            all_dates = [d for d in all_dates if d >= pd.to_datetime(start_date)]
        if end_date:
            all_dates = [d for d in all_dates if d <= pd.to_datetime(end_date)]

        price_series = []

        print(f"Extracting underlying prices for {len(all_dates)} trading days...")

        for i, date in enumerate(all_dates):
            if i % 50 == 0:
                print(f"  Processing day {i+1}/{len(all_dates)}: {date.strftime('%Y-%m-%d')}")

            options_data = self.raw_data[self.raw_data['date'] == date]

            if not options_data.empty:
                underlying_price = self.extract_underlying_price(options_data, date)

                price_series.append({
                    'date': date,
                    'underlying_price': underlying_price
                })

        price_df = pd.DataFrame(price_series)

        if not price_df.empty:
            # Calculate daily returns
            price_df = price_df.sort_values('date')
            price_df['daily_return'] = price_df['underlying_price'].pct_change()

            # Smooth extreme moves (>5% daily) to be more realistic
            for i in range(1, len(price_df)):
                if abs(price_df.iloc[i]['daily_return']) > 0.05:
                    prev_price = price_df.iloc[i-1]['underlying_price']
                    current_return = price_df.iloc[i]['daily_return']

                    # Cap at ±3% daily move
                    if current_return > 0.03:
                        price_df.iloc[i, price_df.columns.get_loc('underlying_price')] = prev_price * 1.03
                    elif current_return < -0.03:
                        price_df.iloc[i, price_df.columns.get_loc('underlying_price')] = prev_price * 0.97

            # Recalculate returns after smoothing
            price_df['daily_return'] = price_df['underlying_price'].pct_change()

            print(f"✅ Extracted {len(price_df)} underlying prices")
            print(f"   Price range: ${price_df['underlying_price'].min():.2f} - ${price_df['underlying_price'].max():.2f}")
            print(f"   Avg daily return: {price_df['daily_return'].mean()*100:.3f}%")
            print(f"   Daily volatility: {price_df['daily_return'].std()*100:.3f}%")

        return price_df

    def _initialize_emini_analyzer(self):
        """Initialize E-mini overnight analyzer for drift calculation"""
        try:
            from emini_overnight_analyzer import EminiOvernightAnalyzer

            emini_path = "/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities/ES_full_30min_continuous_ratio_adjusted.txt"
            self.emini_analyzer = EminiOvernightAnalyzer(emini_path)

            # Initialize overnight sessions for the analysis period
            self.emini_analyzer.identify_overnight_sessions(start_date='2022-01-01', end_date='2024-12-31')
            print("✅ E-mini overnight analyzer initialized for drift calculation")

        except Exception as e:
            print(f"⚠️  E-mini analyzer not available for drift calculation: {e}")
            self.emini_analyzer = None

