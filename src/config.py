"""
Configuration module for JPM Collar Strategy
Handles environment variables, strategy parameters, and system configuration
"""

import os
from pathlib import Path
from dotenv import load_dotenv
from typing import Dict, Any, Optional
from src.constants import *

class Config:
    """
    Comprehensive configuration class for JPM Collar Strategy

    This class manages all configuration parameters including:
    - Environment variables from .env file
    - Directory paths and file locations
    - Trading parameters and risk management settings
    - Strategy-specific configurations
    - Reporting and output settings
    """

    def __init__(self, env_file: str = '.env'):
        """Initialize configuration from environment file and constants"""
        # Load environment variables
        load_dotenv(env_file)

        # =============================================================================
        # DIRECTORY AND PATH CONFIGURATION
        # =============================================================================

        # Base directories - adjust for running from src directory
        current_dir = Path.cwd()
        if current_dir.name == 'src':
            self.base_dir = current_dir.parent
        else:
            self.base_dir = current_dir

        self.data_dir = self.base_dir / 'data'
        self.reports_dir = self.base_dir / 'reports'  # Changed from 'report' to 'reports'
        self.src_dir = self.base_dir / 'src'
        self.optionshistory_dir = self.base_dir / 'optionshistory'

        # Ensure directories exist
        self.data_dir.mkdir(exist_ok=True)
        self.reports_dir.mkdir(exist_ok=True)

        # File paths
        self.data_path = os.getenv('DATA_PATH', str(self.data_dir / 'SPX_2022_2024_combined.txt'))
        self.report_path = str(self.reports_dir)

        # =============================================================================
        # MARKET DATA CONFIGURATION
        # =============================================================================

        self.ticker_symbol = os.getenv('TICKER_SYMBOL', 'SPX')
        self.risk_free_rate = float(os.getenv('RISK_FREE_RATE', RISK_FREE_RATE))

        # =============================================================================
        # TRADING CONFIGURATION
        # =============================================================================

        # Capital and position sizing
        self.initial_capital = float(os.getenv('INITIAL_CAPITAL', 1000000))
        self.max_position_size = float(os.getenv('MAX_POSITION_SIZE', 0.60))

        # Contract sizing (NEW: Based on signal strength)
        self.min_contracts = int(os.getenv('MIN_CONTRACTS', MIN_CONTRACTS))
        self.max_contracts = int(os.getenv('MAX_CONTRACTS', MAX_CONTRACTS))
        self.default_contracts = int(os.getenv('DEFAULT_CONTRACTS', DEFAULT_CONTRACTS))

        # Execution costs
        self.commission_per_contract = float(os.getenv('COMMISSION_PER_CONTRACT', DEFAULT_COMMISSION_PER_CONTRACT))
        self.slippage_bps = float(os.getenv('SLIPPAGE_BPS', DEFAULT_SLIPPAGE_BPS))

        # =============================================================================
        # STRATEGY PARAMETERS
        # =============================================================================

        # Collar configuration
        self.collar_width_percent = float(os.getenv('COLLAR_WIDTH', 0.05))
        self.rebalance_frequency = int(os.getenv('REBALANCE_FREQUENCY', DEFAULT_REBALANCE_FREQUENCY))

        # Exit strategy parameters
        self.profit_target = float(os.getenv('PROFIT_TARGET', DEFAULT_PROFIT_TARGET))
        self.loss_limit = float(os.getenv('LOSS_LIMIT', DEFAULT_LOSS_LIMIT))
        self.dte_threshold = int(os.getenv('DTE_THRESHOLD', DEFAULT_DTE_THRESHOLD))

        # =============================================================================
        # GREEKS THRESHOLDS
        # =============================================================================

        # Basic Greeks
        self.delta_threshold = float(os.getenv('DELTA_THRESHOLD', DEFAULT_DELTA_THRESHOLD))
        self.gamma_threshold = float(os.getenv('GAMMA_THRESHOLD', DEFAULT_GAMMA_THRESHOLD))
        self.vega_threshold = float(os.getenv('VEGA_THRESHOLD', DEFAULT_VEGA_THRESHOLD))
        self.theta_threshold = float(os.getenv('THETA_THRESHOLD', DEFAULT_THETA_THRESHOLD))

        # Advanced Greeks
        self.vanna_threshold = float(os.getenv('VANNA_THRESHOLD', DEFAULT_VANNA_THRESHOLD))
        self.charm_threshold = float(os.getenv('CHARM_THRESHOLD', DEFAULT_CHARM_THRESHOLD))
        self.vomma_threshold = float(os.getenv('VOMMA_THRESHOLD', DEFAULT_VOMMA_THRESHOLD))
        self.zomma_threshold = float(os.getenv('ZOMMA_THRESHOLD', DEFAULT_ZOMMA_THRESHOLD))

        # =============================================================================
        # INTELLIGENT STRIKE SELECTION
        # =============================================================================

        self.enable_intelligent_strikes = os.getenv('ENABLE_INTELLIGENT_STRIKES', 'true').lower() == 'true'
        self.enable_dynamic_strike_selection = os.getenv('ENABLE_DYNAMIC_STRIKE_SELECTION', 'true').lower() == 'true'
        self.enable_radical_strike_selection = os.getenv('ENABLE_RADICAL_STRIKE_SELECTION', 'true').lower() == 'true'

        # Valuation thresholds
        self.valuation_edge_threshold = float(os.getenv('VALUATION_EDGE_THRESHOLD', -999.0))
        self.overvalued_threshold = float(os.getenv('OVERVALUED_THRESHOLD', 0.50))
        self.undervalued_threshold = float(os.getenv('UNDERVALUED_THRESHOLD', 2.00))
        self.min_valuation_confidence = float(os.getenv('MIN_VALUATION_CONFIDENCE', 0.001))

        # Position multipliers
        self.intelligent_position_multiplier = float(os.getenv('INTELLIGENT_POSITION_MULTIPLIER', INTELLIGENT_POSITION_MULTIPLIER))
        self.radical_position_multiplier = float(os.getenv('RADICAL_POSITION_MULTIPLIER', RADICAL_POSITION_MULTIPLIER))
        self.dynamic_position_multiplier = float(os.getenv('DYNAMIC_POSITION_MULTIPLIER', DYNAMIC_POSITION_MULTIPLIER))

        # =============================================================================
        # MACRO FILTER CONFIGURATION
        # =============================================================================

        self.macro_filter_enabled = os.getenv('MACRO_FILTER_ENABLED', 'true').lower() == 'true'
        self.vix_low_threshold = float(os.getenv('VIX_LOW_THRESHOLD', LOW_VOLATILITY_THRESHOLD))
        self.vix_high_threshold = float(os.getenv('VIX_HIGH_THRESHOLD', HIGH_VOLATILITY_THRESHOLD))
        self.stress_threshold = float(os.getenv('STRESS_THRESHOLD', 0.8))
        self.volatility_premium_threshold = float(os.getenv('VOLATILITY_PREMIUM_THRESHOLD', 0.15))

        # Regime adjustments
        self.regime_position_adjustment = os.getenv('REGIME_POSITION_ADJUSTMENT', 'true').lower() == 'true'
        self.high_vol_multiplier = float(os.getenv('HIGH_VOL_MULTIPLIER', 1.1))
        self.low_vol_multiplier = float(os.getenv('LOW_VOL_MULTIPLIER', 0.9))
        self.sideways_multiplier = float(os.getenv('SIDEWAYS_MULTIPLIER', 1.2))
        self.trending_multiplier = float(os.getenv('TRENDING_MULTIPLIER', 0.8))

        # Enhanced filtering
        self.minimum_volatility_premium = float(os.getenv('MINIMUM_VOLATILITY_PREMIUM', 0.12))
        self.maximum_stress_level = float(os.getenv('MAXIMUM_STRESS_LEVEL', 0.85))
        self.trend_strength_threshold = float(os.getenv('TREND_STRENGTH_THRESHOLD', 0.7))

        # =============================================================================
        # REPORTING CONFIGURATION
        # =============================================================================

        self.export_trades = os.getenv('EXPORT_TRADES', 'true').lower() == 'true'
        self.generate_charts = os.getenv('GENERATE_CHARTS', 'true').lower() == 'true'
        self.generate_pdf_report = os.getenv('GENERATE_PDF_REPORT', 'true').lower() == 'true'

        # Chart configuration
        self.chart_width = int(os.getenv('CHART_WIDTH', CHART_WIDTH))
        self.chart_height = int(os.getenv('CHART_HEIGHT', CHART_HEIGHT))
        self.chart_dpi = int(os.getenv('CHART_DPI', CHART_DPI))

    def calculate_contracts_from_signal_strength(self, signal_strength: float) -> int:
        """
        Calculate number of contracts based on signal strength

        Args:
            signal_strength: Signal strength between 0.0 and 1.0

        Returns:
            Number of contracts between min_contracts and max_contracts
        """
        if signal_strength < 0.0 or signal_strength > 1.0:
            signal_strength = max(0.0, min(1.0, signal_strength))

        # Linear interpolation between min and max contracts
        contract_range = self.max_contracts - self.min_contracts
        contracts = self.min_contracts + int(signal_strength * contract_range)

        return max(self.min_contracts, min(self.max_contracts, contracts))

    def get_collar_strikes_config(self, strategy_type: str) -> Dict[str, float]:
        """
        Get collar strike configuration for different strategy types

        Args:
            strategy_type: Type of collar strategy

        Returns:
            Dictionary with strike percentages
        """
        strategy_configs = {
            'TIGHT_COLLAR': {
                'call_otm': TIGHT_COLLAR_CALL_OTM,
                'put_long_otm': TIGHT_COLLAR_PUT_LONG_OTM,
                'put_short_otm': TIGHT_COLLAR_PUT_SHORT_OTM
            },
            'WIDE_COLLAR': {
                'call_otm': WIDE_COLLAR_CALL_OTM,
                'put_long_otm': WIDE_COLLAR_PUT_LONG_OTM,
                'put_short_otm': WIDE_COLLAR_PUT_SHORT_OTM
            },
            'ASYMMETRIC_COLLAR': {
                'call_otm': ASYMMETRIC_COLLAR_CALL_OTM,
                'put_long_otm': ASYMMETRIC_COLLAR_PUT_LONG_OTM,
                'put_short_otm': ASYMMETRIC_COLLAR_PUT_SHORT_OTM
            },
            'VOLATILITY_COLLAR': {
                'call_otm': VOLATILITY_COLLAR_CALL_OTM,
                'put_long_otm': VOLATILITY_COLLAR_PUT_LONG_OTM,
                'put_short_otm': VOLATILITY_COLLAR_PUT_SHORT_OTM
            }
        }

        return strategy_configs.get(strategy_type, strategy_configs['TIGHT_COLLAR'])

    def get_all_params(self) -> Dict[str, Any]:
        """Return all configuration parameters as dictionary"""
        return {attr: getattr(self, attr) for attr in dir(self)
                if not attr.startswith('_') and not callable(getattr(self, attr))}

    def update_param(self, param_name: str, value: Any) -> None:
        """Update a configuration parameter"""
        if hasattr(self, param_name):
            setattr(self, param_name, value)
        else:
            raise ValueError(f"Parameter {param_name} not found in configuration")

    def validate_config(self) -> bool:
        """Validate configuration parameters"""
        try:
            # Validate contract sizing
            assert self.min_contracts >= 1, "min_contracts must be >= 1"
            assert self.max_contracts >= self.min_contracts, "max_contracts must be >= min_contracts"

            # Validate profit/loss targets
            assert 0 < self.profit_target <= MAX_PROFIT_TARGET, f"profit_target must be between 0 and {MAX_PROFIT_TARGET}"
            assert MAX_LOSS_LIMIT <= self.loss_limit < 0, f"loss_limit must be between {MAX_LOSS_LIMIT} and 0"

            # Validate position sizing
            assert 0 < self.max_position_size <= 1.0, "max_position_size must be between 0 and 1.0"

            # Validate paths exist
            assert Path(self.data_path).exists(), f"Data path does not exist: {self.data_path}"

            return True

        except AssertionError as e:
            print(f"❌ Configuration validation failed: {e}")
            return False

    def __str__(self) -> str:
        """String representation of configuration"""
        params = self.get_all_params()
        return '\n'.join([f"{k}: {v}" for k, v in sorted(params.items())])

