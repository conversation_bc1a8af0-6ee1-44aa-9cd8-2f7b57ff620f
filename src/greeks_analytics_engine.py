"""
Advanced Greeks Analytics Engine for Options Cluster Strategy

This module provides comprehensive Greeks calculations including:
- Delta: Price sensitivity
- Gamma: Delta sensitivity (convexity)
- <PERSON>na: Delta sensitivity to volatility
- Charm: Delta decay over time
- Vomma: Vega sensitivity to volatility

All calculations use Black-Scholes framework with real-time market data.
"""

import numpy as np
import pandas as pd
from scipy.stats import norm
from typing import Dict, List, Tuple, Optional
import warnings
from datetime import datetime, timedelta

from src.constants import (
    SPX_MULTIPLIER, DRIFT_THRESHOLD, WALL_STRENGTH_THRESHOLD,
    GAMMA_MULTIPLIER, DELTA_MULTIPLIER
)

warnings.filterwarnings('ignore')


class GreeksAnalyticsEngine:
    """
    Advanced Greeks calculation engine for options cluster analysis
    
    Calculates first, second, and third-order Greeks:
    - First order: Delta
    - Second order: Gamma, Vanna, Charm
    - Third order: Vomma
    """
    
    def __init__(self, risk_free_rate: float = 0.05):
        """
        Initialize the Greeks analytics engine
        
        Args:
            risk_free_rate: Risk-free interest rate (default 5%)
        """
        self.risk_free_rate = risk_free_rate
        self.min_time_to_expiry = 1/365  # Minimum 1 day to avoid division by zero
        
    def calculate_implied_volatility(self, option_price: float, spot: float, 
                                   strike: float, time_to_expiry: float, 
                                   option_type: str, initial_guess: float = 0.2) -> float:
        """
        Calculate implied volatility using Newton-Raphson method
        
        Args:
            option_price: Market price of option
            spot: Current underlying price
            strike: Strike price
            time_to_expiry: Time to expiration in years
            option_type: 'call' or 'put'
            initial_guess: Starting volatility guess
            
        Returns:
            Implied volatility
        """
        try:
            if time_to_expiry <= 0 or option_price <= 0:
                return 0.2  # Default volatility
                
            vol = initial_guess
            
            for _ in range(50):  # Maximum iterations
                bs_price = self._black_scholes_price(spot, strike, time_to_expiry, vol, option_type)
                vega = self._calculate_vega(spot, strike, time_to_expiry, vol)
                
                if abs(vega) < 1e-6:  # Avoid division by zero
                    break
                    
                price_diff = bs_price - option_price
                if abs(price_diff) < 0.01:  # Convergence threshold
                    break
                    
                vol = vol - price_diff / vega
                vol = max(0.01, min(5.0, vol))  # Keep vol in reasonable range
                
            return vol
            
        except Exception:
            return 0.2  # Default volatility on error
    
    def _black_scholes_price(self, spot: float, strike: float, 
                           time_to_expiry: float, volatility: float, 
                           option_type: str) -> float:
        """Calculate Black-Scholes option price"""
        try:
            if time_to_expiry <= 0:
                if option_type.lower() == 'call':
                    return max(0, spot - strike)
                else:
                    return max(0, strike - spot)
            
            d1 = (np.log(spot / strike) + (self.risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / (volatility * np.sqrt(time_to_expiry))
            d2 = d1 - volatility * np.sqrt(time_to_expiry)
            
            if option_type.lower() == 'call':
                price = spot * norm.cdf(d1) - strike * np.exp(-self.risk_free_rate * time_to_expiry) * norm.cdf(d2)
            else:
                price = strike * np.exp(-self.risk_free_rate * time_to_expiry) * norm.cdf(-d2) - spot * norm.cdf(-d1)
                
            return max(0, price)
            
        except Exception:
            return 0.0
    
    def _calculate_d1_d2(self, spot: float, strike: float, 
                        time_to_expiry: float, volatility: float) -> Tuple[float, float]:
        """Calculate d1 and d2 for Black-Scholes formula"""
        try:
            sqrt_t = np.sqrt(max(time_to_expiry, self.min_time_to_expiry))
            d1 = (np.log(spot / strike) + (self.risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / (volatility * sqrt_t)
            d2 = d1 - volatility * sqrt_t
            return d1, d2
        except Exception:
            return 0.0, 0.0
    
    def _calculate_vega(self, spot: float, strike: float, 
                       time_to_expiry: float, volatility: float) -> float:
        """Calculate vega (sensitivity to volatility)"""
        try:
            d1, _ = self._calculate_d1_d2(spot, strike, time_to_expiry, volatility)
            sqrt_t = np.sqrt(max(time_to_expiry, self.min_time_to_expiry))
            return spot * norm.pdf(d1) * sqrt_t / 100  # Divide by 100 for percentage point change
        except Exception:
            return 0.0
    
    def calculate_delta(self, spot: float, strike: float, time_to_expiry: float, 
                       volatility: float, option_type: str) -> float:
        """
        Calculate delta (price sensitivity to underlying)
        
        Returns:
            Delta value (-1 to 1)
        """
        try:
            if time_to_expiry <= 0:
                if option_type.lower() == 'call':
                    return 1.0 if spot > strike else 0.0
                else:
                    return -1.0 if spot < strike else 0.0
            
            d1, _ = self._calculate_d1_d2(spot, strike, time_to_expiry, volatility)
            
            if option_type.lower() == 'call':
                return norm.cdf(d1)
            else:
                return norm.cdf(d1) - 1.0
                
        except Exception:
            return 0.0
    
    def calculate_gamma(self, spot: float, strike: float, time_to_expiry: float, 
                       volatility: float) -> float:
        """
        Calculate gamma (delta sensitivity to underlying)
        
        Returns:
            Gamma value (always positive)
        """
        try:
            if time_to_expiry <= 0:
                return 0.0
            
            d1, _ = self._calculate_d1_d2(spot, strike, time_to_expiry, volatility)
            sqrt_t = np.sqrt(max(time_to_expiry, self.min_time_to_expiry))
            
            return norm.pdf(d1) / (spot * volatility * sqrt_t)
            
        except Exception:
            return 0.0
    
    def calculate_vanna(self, spot: float, strike: float, time_to_expiry: float, 
                       volatility: float) -> float:
        """
        Calculate vanna (delta sensitivity to volatility)
        
        Returns:
            Vanna value
        """
        try:
            if time_to_expiry <= 0:
                return 0.0
            
            d1, d2 = self._calculate_d1_d2(spot, strike, time_to_expiry, volatility)
            sqrt_t = np.sqrt(max(time_to_expiry, self.min_time_to_expiry))
            
            return -norm.pdf(d1) * d2 / volatility / 100  # Divide by 100 for percentage point change
            
        except Exception:
            return 0.0
    
    def calculate_charm(self, spot: float, strike: float, time_to_expiry: float, 
                       volatility: float, option_type: str) -> float:
        """
        Calculate charm (delta decay over time)
        
        Returns:
            Charm value (delta change per day)
        """
        try:
            if time_to_expiry <= 0:
                return 0.0
            
            d1, d2 = self._calculate_d1_d2(spot, strike, time_to_expiry, volatility)
            sqrt_t = np.sqrt(max(time_to_expiry, self.min_time_to_expiry))
            
            if option_type.lower() == 'call':
                charm = -norm.pdf(d1) * (2 * self.risk_free_rate * time_to_expiry - d2 * volatility * sqrt_t) / (2 * time_to_expiry * volatility * sqrt_t)
            else:
                charm = -norm.pdf(d1) * (2 * self.risk_free_rate * time_to_expiry - d2 * volatility * sqrt_t) / (2 * time_to_expiry * volatility * sqrt_t)
            
            return charm * 365  # Convert to per-day change
            
        except Exception:
            return 0.0
    
    def calculate_vomma(self, spot: float, strike: float, time_to_expiry: float, 
                       volatility: float) -> float:
        """
        Calculate vomma (vega sensitivity to volatility)
        
        Returns:
            Vomma value
        """
        try:
            if time_to_expiry <= 0:
                return 0.0
            
            d1, d2 = self._calculate_d1_d2(spot, strike, time_to_expiry, volatility)
            sqrt_t = np.sqrt(max(time_to_expiry, self.min_time_to_expiry))
            vega = self._calculate_vega(spot, strike, time_to_expiry, volatility)
            
            return vega * d1 * d2 / volatility / 100  # Divide by 100 for percentage point change
            
        except Exception:
            return 0.0
    
    def calculate_all_greeks(self, spot: float, strike: float, time_to_expiry: float, 
                           volatility: float, option_type: str) -> Dict[str, float]:
        """
        Calculate all Greeks for an option
        
        Returns:
            Dictionary containing all Greek values
        """
        return {
            'delta': self.calculate_delta(spot, strike, time_to_expiry, volatility, option_type),
            'gamma': self.calculate_gamma(spot, strike, time_to_expiry, volatility),
            'vanna': self.calculate_vanna(spot, strike, time_to_expiry, volatility),
            'charm': self.calculate_charm(spot, strike, time_to_expiry, volatility, option_type),
            'vomma': self.calculate_vomma(spot, strike, time_to_expiry, volatility)
        }


class ClusterGreeksAnalyzer:
    """
    Advanced Greeks-based cluster analysis for options walls detection

    Uses calculated Greeks to identify:
    - Options walls with gamma/delta weighting
    - Dynamic signal thresholds based on Greeks convergence
    - Position sizing based on signal strength
    """

    def __init__(self, greeks_engine: GreeksAnalyticsEngine):
        """
        Initialize cluster analyzer with Greeks engine

        Args:
            greeks_engine: Instance of GreeksAnalyticsEngine
        """
        self.greeks_engine = greeks_engine
        self.lookback_periods = 20  # Days for dynamic threshold calculation

    def calculate_options_greeks_for_day(self, options_data: pd.DataFrame,
                                       current_price: float, current_date: pd.Timestamp) -> pd.DataFrame:
        """
        Calculate fresh Greeks for significant options only (optimized version)

        Args:
            options_data: Options data for the day
            current_price: Current underlying price
            current_date: Current date

        Returns:
            DataFrame with calculated Greeks for significant options only
        """
        # Filter for significant options only to improve performance
        significant_options = options_data[
            (options_data['volume'] > 0) |
            (options_data['open_interest'] > 100) |
            (abs(options_data['strike'] - current_price) < current_price * 0.2)  # Within 20% of current price
        ].copy()

        if significant_options.empty:
            return pd.DataFrame()

        # Limit to top options by volume/OI to prevent excessive computation
        significant_options['significance'] = (
            significant_options['volume'] +
            significant_options['open_interest'] * 0.1
        )
        significant_options = significant_options.nlargest(200, 'significance')  # Top 200 options max

        results = []

        for _, option in significant_options.iterrows():
            try:
                # Calculate time to expiry
                expiry_date = pd.to_datetime(option['expiration'])
                time_to_expiry = max((expiry_date - current_date).days / 365.0, 1/365)

                # Skip options with very short or very long expiry
                if time_to_expiry < 7/365 or time_to_expiry > 180/365:
                    continue

                # Get market price for IV calculation
                market_price = option.get('Last Trade Price', option.get('ask', 0))
                if market_price <= 0:
                    market_price = (option.get('bid', 0) + option.get('ask', 0)) / 2

                if market_price <= 0:
                    continue

                # Use simplified Greeks calculation for speed
                option_type = 'call' if option['option_type'].lower() == 'c' else 'put'

                # Use a fixed IV estimate for speed instead of iterative calculation
                iv = 0.20  # Default 20% volatility

                # Calculate essential Greeks only
                delta = self.greeks_engine.calculate_delta(
                    current_price, option['strike'], time_to_expiry, iv, option_type
                )
                gamma = self.greeks_engine.calculate_gamma(
                    current_price, option['strike'], time_to_expiry, iv
                )

                # Simplified vanna/charm for speed
                vanna = delta * 0.1 if abs(delta) > 0.3 else 0  # Simplified vanna
                charm = -delta * 0.05 if time_to_expiry < 30/365 else 0  # Simplified charm

                # Create result record
                result = {
                    'date': current_date,
                    'strike': option['strike'],
                    'expiration': option['expiration'],
                    'option_type': option['option_type'],
                    'underlying_price': current_price,
                    'market_price': market_price,
                    'time_to_expiry': time_to_expiry,
                    'implied_volatility': iv,
                    'volume': option.get('volume', 0),
                    'open_interest': option.get('open_interest', 0),
                    'delta': delta,
                    'gamma': gamma,
                    'vanna': vanna,
                    'charm': charm,
                    'vomma': 0  # Skip vomma for speed
                }

                results.append(result)

            except Exception as e:
                continue  # Skip problematic options

        return pd.DataFrame(results)

    def identify_options_walls_with_greeks(self, greeks_df: pd.DataFrame,
                                         current_price: float) -> Dict[str, any]:
        """
        Identify options walls using Greeks-weighted analysis

        Args:
            greeks_df: DataFrame with calculated Greeks
            current_price: Current underlying price

        Returns:
            Dictionary with wall analysis results
        """
        if greeks_df.empty:
            return self._empty_wall_analysis()

        # Filter for significant volume/OI
        significant_options = greeks_df[
            (greeks_df['volume'] > 0) | (greeks_df['open_interest'] > 0)
        ].copy()

        if significant_options.empty:
            return self._empty_wall_analysis()

        # Calculate Greeks-weighted metrics
        significant_options['gamma_weight'] = (
            significant_options['gamma'] *
            significant_options['volume'] *
            GAMMA_MULTIPLIER
        )

        significant_options['delta_weight'] = (
            abs(significant_options['delta']) *
            significant_options['open_interest'] *
            DELTA_MULTIPLIER
        )

        # Calculate vanna and charm convergence
        significant_options['vanna_signal'] = significant_options['vanna'] * significant_options['volume']
        significant_options['charm_decay'] = significant_options['charm'] * significant_options['open_interest']

        # Separate calls and puts
        calls = significant_options[significant_options['option_type'].str.lower() == 'c']
        puts = significant_options[significant_options['option_type'].str.lower() == 'p']

        # Identify call walls (resistance levels)
        call_walls = self._identify_strike_clusters(calls, current_price, 'call')

        # Identify put walls (support levels)
        put_walls = self._identify_strike_clusters(puts, current_price, 'put')

        # Calculate wall strengths using Greeks
        call_wall_strength = self._calculate_greeks_wall_strength(calls, call_walls)
        put_wall_strength = self._calculate_greeks_wall_strength(puts, put_walls)

        # Calculate Greeks convergence signals
        vanna_convergence = self._calculate_vanna_convergence(significant_options)
        charm_decay_signal = self._calculate_charm_decay_signal(significant_options)
        vomma_volatility_signal = self._calculate_vomma_signal(significant_options)

        return {
            'call_walls': call_walls,
            'put_walls': put_walls,
            'call_wall_strength': call_wall_strength,
            'put_wall_strength': put_wall_strength,
            'call_wall_count': len(call_walls),
            'put_wall_count': len(put_walls),
            'dominant_wall': 'CALL_WALL' if call_wall_strength > put_wall_strength else 'PUT_WALL',
            'vanna_convergence': vanna_convergence,
            'charm_decay_signal': charm_decay_signal,
            'vomma_volatility_signal': vomma_volatility_signal,
            'total_gamma_exposure': significant_options['gamma_weight'].sum(),
            'total_delta_exposure': significant_options['delta_weight'].sum(),
            'net_vanna_exposure': significant_options['vanna_signal'].sum(),
            'net_charm_exposure': significant_options['charm_decay'].sum()
        }

    def _empty_wall_analysis(self) -> Dict[str, any]:
        """Return empty wall analysis structure"""
        return {
            'call_walls': [],
            'put_walls': [],
            'call_wall_strength': 0.0,
            'put_wall_strength': 0.0,
            'call_wall_count': 0,
            'put_wall_count': 0,
            'dominant_wall': 'NEUTRAL',
            'vanna_convergence': 0.0,
            'charm_decay_signal': 0.0,
            'vomma_volatility_signal': 0.0,
            'total_gamma_exposure': 0.0,
            'total_delta_exposure': 0.0,
            'net_vanna_exposure': 0.0,
            'net_charm_exposure': 0.0
        }

    def _identify_strike_clusters(self, options_df: pd.DataFrame,
                                current_price: float, option_type: str) -> List[float]:
        """
        Identify strike price clusters using Greeks weighting

        Args:
            options_df: Options data (calls or puts)
            current_price: Current underlying price
            option_type: 'call' or 'put'

        Returns:
            List of significant strike prices
        """
        if options_df.empty:
            return []

        # Group by strike and calculate Greeks-weighted metrics
        strike_groups = options_df.groupby('strike').agg({
            'gamma_weight': 'sum',
            'delta_weight': 'sum',
            'volume': 'sum',
            'open_interest': 'sum',
            'vanna_signal': 'sum'
        }).reset_index()

        # Calculate combined wall strength
        strike_groups['wall_strength'] = (
            strike_groups['gamma_weight'] +
            strike_groups['delta_weight'] +
            abs(strike_groups['vanna_signal'])
        )

        # Filter for significant walls
        threshold = strike_groups['wall_strength'].quantile(0.7)  # Top 30%
        significant_strikes = strike_groups[
            strike_groups['wall_strength'] >= threshold
        ]['strike'].tolist()

        return sorted(significant_strikes)

    def _calculate_greeks_wall_strength(self, options_df: pd.DataFrame,
                                      walls: List[float]) -> float:
        """
        Calculate wall strength using Greeks weighting

        Args:
            options_df: Options data
            walls: List of wall strike prices

        Returns:
            Total wall strength
        """
        if not walls or options_df.empty:
            return 0.0

        wall_options = options_df[options_df['strike'].isin(walls)]

        return (
            wall_options['gamma_weight'].sum() +
            wall_options['delta_weight'].sum() +
            abs(wall_options['vanna_signal'].sum())
        )

    def _calculate_vanna_convergence(self, options_df: pd.DataFrame) -> float:
        """
        Calculate vanna convergence signal for volatility-delta relationship

        Args:
            options_df: Options data with calculated Greeks

        Returns:
            Vanna convergence signal (-1 to 1)
        """
        if options_df.empty:
            return 0.0

        # Weight vanna by volume and time decay
        weighted_vanna = (
            options_df['vanna'] *
            options_df['volume'] *
            (1 / (options_df['time_to_expiry'] + 0.1))  # More weight to near-term
        ).sum()

        # Normalize to -1 to 1 range
        max_vanna = abs(weighted_vanna) + 1e-6
        return np.tanh(weighted_vanna / max_vanna)

    def _calculate_charm_decay_signal(self, options_df: pd.DataFrame) -> float:
        """
        Calculate charm decay signal for time-based delta changes

        Args:
            options_df: Options data with calculated Greeks

        Returns:
            Charm decay signal
        """
        if options_df.empty:
            return 0.0

        # Weight charm by open interest
        weighted_charm = (
            options_df['charm'] *
            options_df['open_interest']
        ).sum()

        return np.tanh(weighted_charm / 1000)  # Normalize

    def _calculate_vomma_signal(self, options_df: pd.DataFrame) -> float:
        """
        Calculate vomma signal for volatility convexity

        Args:
            options_df: Options data with calculated Greeks

        Returns:
            Vomma volatility signal
        """
        if options_df.empty:
            return 0.0

        # Weight vomma by volume
        weighted_vomma = (
            options_df['vomma'] *
            options_df['volume']
        ).sum()

        return np.tanh(weighted_vomma / 100)  # Normalize

    def calculate_dynamic_thresholds(self, historical_signals: List[Dict],
                                   lookback_days: int = 20) -> Dict[str, float]:
        """
        Calculate dynamic thresholds based on historical signal distribution

        Args:
            historical_signals: List of historical signal data
            lookback_days: Number of days to look back

        Returns:
            Dictionary with dynamic thresholds
        """
        if len(historical_signals) < 5:
            return {
                'wall_strength_threshold': WALL_STRENGTH_THRESHOLD,
                'drift_threshold': DRIFT_THRESHOLD,
                'vanna_threshold': 0.3,
                'charm_threshold': 0.2,
                'vomma_threshold': 0.25
            }

        recent_signals = historical_signals[-lookback_days:]

        # Extract signal components
        wall_strengths = [max(s.get('call_wall_strength', 0), s.get('put_wall_strength', 0))
                         for s in recent_signals]
        vanna_signals = [abs(s.get('vanna_convergence', 0)) for s in recent_signals]
        charm_signals = [abs(s.get('charm_decay_signal', 0)) for s in recent_signals]
        vomma_signals = [abs(s.get('vomma_volatility_signal', 0)) for s in recent_signals]

        # Calculate adaptive thresholds (75th percentile)
        return {
            'wall_strength_threshold': np.percentile(wall_strengths, 75) if wall_strengths else WALL_STRENGTH_THRESHOLD,
            'drift_threshold': DRIFT_THRESHOLD,  # Keep drift threshold static
            'vanna_threshold': np.percentile(vanna_signals, 75) if vanna_signals else 0.3,
            'charm_threshold': np.percentile(charm_signals, 75) if charm_signals else 0.2,
            'vomma_threshold': np.percentile(vomma_signals, 75) if vomma_signals else 0.25
        }

    def calculate_signal_strength_with_greeks(self, wall_data: Dict, drift: float,
                                            thresholds: Dict) -> Dict[str, float]:
        """
        Calculate signal strength using Greeks convergence and dynamic thresholds

        Args:
            wall_data: Wall analysis data with Greeks
            drift: Overnight drift
            thresholds: Dynamic thresholds

        Returns:
            Signal strength and components
        """
        # Base signal from walls and drift
        wall_strength = max(wall_data['call_wall_strength'], wall_data['put_wall_strength'])
        wall_signal = min(wall_strength / thresholds['wall_strength_threshold'], 1.0)

        drift_signal = min(abs(drift) / thresholds['drift_threshold'], 1.0)

        # Greeks convergence signals
        vanna_signal = min(abs(wall_data['vanna_convergence']) / thresholds['vanna_threshold'], 1.0)
        charm_signal = min(abs(wall_data['charm_decay_signal']) / thresholds['charm_threshold'], 1.0)
        vomma_signal = min(abs(wall_data['vomma_volatility_signal']) / thresholds['vomma_threshold'], 1.0)

        # Combined signal strength (weighted average)
        combined_strength = (
            0.3 * wall_signal +      # 30% wall strength
            0.2 * drift_signal +     # 20% drift
            0.2 * vanna_signal +     # 20% vanna convergence
            0.15 * charm_signal +    # 15% charm decay
            0.15 * vomma_signal      # 15% vomma volatility
        )

        return {
            'total_strength': min(combined_strength, 1.0),
            'wall_component': wall_signal,
            'drift_component': drift_signal,
            'vanna_component': vanna_signal,
            'charm_component': charm_signal,
            'vomma_component': vomma_signal
        }

    def calculate_dynamic_position_size(self, signal_strength: Dict,
                                      base_capital: float,
                                      option_price: float,
                                      max_risk_per_trade: float = 0.10) -> Dict[str, any]:
        """
        Calculate position size based on signal strength and Greeks convergence

        Args:
            signal_strength: Signal strength components
            base_capital: Available capital
            option_price: Price per option contract
            max_risk_per_trade: Maximum risk per trade (default 10%)

        Returns:
            Position sizing information
        """
        total_strength = signal_strength['total_strength']

        # Base position size from signal strength
        base_size = int(total_strength * 10)  # 0-10 contracts based on strength

        # Adjust based on Greeks convergence quality
        greeks_quality = (
            signal_strength['vanna_component'] +
            signal_strength['charm_component'] +
            signal_strength['vomma_component']
        ) / 3

        # Higher quality Greeks convergence = larger position
        quality_multiplier = 0.5 + (greeks_quality * 1.5)  # 0.5x to 2.0x
        adjusted_size = int(base_size * quality_multiplier)

        # Risk management constraints
        max_risk_amount = base_capital * max_risk_per_trade
        max_contracts_by_risk = int(max_risk_amount / (option_price * SPX_MULTIPLIER))

        # Final position size
        final_contracts = max(1, min(adjusted_size, max_contracts_by_risk, 15))  # 1-15 contracts max

        # Calculate actual risk
        position_value = final_contracts * option_price * SPX_MULTIPLIER
        actual_risk_pct = position_value / base_capital

        return {
            'contracts': final_contracts,
            'position_value': position_value,
            'risk_percentage': actual_risk_pct,
            'signal_strength': total_strength,
            'greeks_quality': greeks_quality,
            'quality_multiplier': quality_multiplier,
            'risk_adjusted': max_contracts_by_risk < adjusted_size
        }


class AdvancedSignalGenerator:
    """
    Advanced signal generation using Greeks analytics and dynamic thresholds
    """

    def __init__(self, cluster_analyzer: ClusterGreeksAnalyzer):
        """
        Initialize signal generator

        Args:
            cluster_analyzer: Instance of ClusterGreeksAnalyzer
        """
        self.cluster_analyzer = cluster_analyzer
        self.signal_history = []

    def generate_enhanced_signal(self, options_data: pd.DataFrame,
                                current_price: float, current_date: pd.Timestamp,
                                overnight_drift: float) -> Dict[str, any]:
        """
        Generate enhanced trading signal using Greeks analytics

        Args:
            options_data: Options data for the day
            current_price: Current underlying price
            current_date: Current date
            overnight_drift: Overnight E-mini drift

        Returns:
            Enhanced signal with Greeks analysis
        """
        # Calculate fresh Greeks for all options
        greeks_df = self.cluster_analyzer.calculate_options_greeks_for_day(
            options_data, current_price, current_date
        )

        # Identify walls using Greeks
        wall_data = self.cluster_analyzer.identify_options_walls_with_greeks(
            greeks_df, current_price
        )

        # Calculate dynamic thresholds
        thresholds = self.cluster_analyzer.calculate_dynamic_thresholds(self.signal_history)

        # Calculate signal strength with Greeks
        signal_strength = self.cluster_analyzer.calculate_signal_strength_with_greeks(
            wall_data, overnight_drift, thresholds
        )

        # Determine signal direction
        signal_type, rationale = self._determine_signal_direction(
            wall_data, overnight_drift, signal_strength, thresholds
        )

        # Store signal in history
        signal_data = {
            'date': current_date,
            'signal_type': signal_type,
            'signal_strength': signal_strength['total_strength'],
            'wall_data': wall_data,
            'drift': overnight_drift,
            'thresholds': thresholds,
            'rationale': rationale
        }

        self.signal_history.append(signal_data)

        # Keep only recent history
        if len(self.signal_history) > 50:
            self.signal_history = self.signal_history[-50:]

        return signal_data

    def _determine_signal_direction(self, wall_data: Dict, drift: float,
                                  signal_strength: Dict, thresholds: Dict) -> Tuple[str, str]:
        """
        Determine signal direction using enhanced Greeks analysis

        Returns:
            Tuple of (signal_type, rationale)
        """
        call_strength = wall_data['call_wall_strength']
        put_strength = wall_data['put_wall_strength']
        vanna_conv = wall_data['vanna_convergence']
        charm_decay = wall_data['charm_decay_signal']

        # Enhanced signal logic with Greeks
        if put_strength > call_strength:
            # Put walls = Support levels
            if drift >= 0 and vanna_conv > 0:
                return 'BULLISH', f"BULLISH: Put support + upward drift + positive vanna convergence"
            elif drift < -thresholds['drift_threshold'] and charm_decay < 0:
                return 'BEARISH', f"BEARISH: Breaking put support + strong down drift + negative charm"
            elif vanna_conv > thresholds['vanna_threshold']:
                return 'BULLISH', f"BULLISH: Put support + strong vanna convergence ({vanna_conv:.3f})"
        else:
            # Call walls = Resistance levels
            if drift <= 0 and vanna_conv < 0:
                return 'BEARISH', f"BEARISH: Call resistance + downward drift + negative vanna"
            elif drift > thresholds['drift_threshold'] and charm_decay > 0:
                return 'BULLISH', f"BULLISH: Breaking call resistance + strong up drift + positive charm"
            elif abs(vanna_conv) > thresholds['vanna_threshold']:
                return 'BEARISH', f"BEARISH: Call resistance + vanna divergence ({vanna_conv:.3f})"

        return 'NEUTRAL', "No clear signal from Greeks analysis"
