"""
Market Regime Filter Module for JPM Collar Strategy
Provides VIX-based filtering and UTY momentum-based option type selection
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Tuple, Optional, List
import logging
from pathlib import Path

class MarketRegimeFilter:
    """
    Market regime filter that provides:
    1. VIX-based trade filtering (avoid VIX < 15)
    2. UTY momentum-based option type selection
    3. Dynamic position sizing based on VIX regime
    """
    
    def __init__(self, securities_path: str = "/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities/"):
        """Initialize market regime filter with data paths"""
        self.securities_path = securities_path
        self.vix_data = None
        self.uty_data = None
        self.market_data = None
        self.logger = logging.getLogger(__name__)
        
        # Regime thresholds based on correlation analysis
        self.VIX_LOW_THRESHOLD = 15.0
        self.VIX_NORMAL_THRESHOLD = 25.0
        self.UTY_MOMENTUM_DAYS = 5
        
        # Position sizing multipliers
        self.POSITION_MULTIPLIERS = {
            'low_vix': 0.0,      # No trades in low VIX
            'normal_vix': 1.5,   # Increase sizing in favorable regime
            'high_vix': 1.0      # Standard sizing in high VIX
        }
        
        self.load_market_data()
    
    def load_market_data(self) -> bool:
        """Load VIX and UTY data from securities directory"""
        try:
            # Load VIX data
            vix_path = Path(self.securities_path) / "VIX_full_5min.txt"
            self.logger.info(f"Loading VIX data from: {vix_path}")
            
            self.vix_data = pd.read_csv(
                vix_path,
                names=['datetime', 'open', 'high', 'low', 'close', 'volume'],
                parse_dates=['datetime']
            )
            
            # Convert to daily data (use close price)
            vix_daily = self.vix_data.groupby(self.vix_data['datetime'].dt.date).agg({
                'close': 'last',
                'high': 'max',
                'low': 'min',
                'volume': 'sum'
            }).reset_index()
            vix_daily['date'] = pd.to_datetime(vix_daily['datetime'])
            vix_daily = vix_daily.rename(columns={'close': 'vix'})
            
            self.logger.info(f"✅ Loaded VIX data: {len(vix_daily)} days, range: {vix_daily['vix'].min():.1f} - {vix_daily['vix'].max():.1f}")
            
            # Load UTY data
            uty_path = Path(self.securities_path) / "UTY_full_5min.txt"
            self.logger.info(f"Loading UTY data from: {uty_path}")
            
            self.uty_data = pd.read_csv(
                uty_path,
                names=['datetime', 'open', 'high', 'low', 'close', 'volume'],
                parse_dates=['datetime']
            )
            
            # Convert to daily data
            uty_daily = self.uty_data.groupby(self.uty_data['datetime'].dt.date).agg({
                'close': 'last',
                'high': 'max',
                'low': 'min',
                'volume': 'sum'
            }).reset_index()
            uty_daily['date'] = pd.to_datetime(uty_daily['datetime'])
            uty_daily = uty_daily.rename(columns={'close': 'uty'})
            
            self.logger.info(f"✅ Loaded UTY data: {len(uty_daily)} days, range: {uty_daily['uty'].min():.1f} - {uty_daily['uty'].max():.1f}")
            
            # Merge VIX and UTY data
            self.market_data = vix_daily[['date', 'vix']].merge(
                uty_daily[['date', 'uty']], on='date', how='outer'
            )
            
            # Forward fill missing values and sort
            self.market_data = self.market_data.sort_values('date')
            self.market_data['vix'] = self.market_data['vix'].fillna(method='ffill')
            self.market_data['uty'] = self.market_data['uty'].fillna(method='ffill')
            
            # Calculate UTY momentum
            self.market_data['uty_momentum'] = self.market_data['uty'].pct_change(self.UTY_MOMENTUM_DAYS) * 100
            self.market_data['uty_momentum_direction'] = np.where(
                self.market_data['uty_momentum'] > 0, 'rising', 'falling'
            )
            
            # Define VIX regimes
            self.market_data['vix_regime'] = pd.cut(
                self.market_data['vix'],
                bins=[0, self.VIX_LOW_THRESHOLD, self.VIX_NORMAL_THRESHOLD, 100],
                labels=['low_vix', 'normal_vix', 'high_vix']
            )
            
            self.logger.info(f"✅ Combined market data: {len(self.market_data)} days")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to load market data: {e}")
            return False
    
    def get_market_regime(self, date: datetime) -> Dict:
        """Get market regime information for a specific date"""
        try:
            # Convert to date if datetime
            if isinstance(date, datetime):
                date = date.date()
            
            # Find closest market data
            market_row = self.market_data[self.market_data['date'].dt.date <= date].iloc[-1]
            
            return {
                'date': market_row['date'],
                'vix': market_row['vix'],
                'vix_regime': market_row['vix_regime'],
                'uty': market_row['uty'],
                'uty_momentum': market_row['uty_momentum'],
                'uty_direction': market_row['uty_momentum_direction'],
                'position_multiplier': self.POSITION_MULTIPLIERS.get(market_row['vix_regime'], 1.0)
            }
            
        except Exception as e:
            self.logger.warning(f"Could not get market regime for {date}: {e}")
            return {
                'date': date,
                'vix': 20.0,  # Default values
                'vix_regime': 'normal_vix',
                'uty': 500.0,
                'uty_momentum': 0.0,
                'uty_direction': 'neutral',
                'position_multiplier': 1.0
            }
    
    def should_trade(self, date: datetime) -> Tuple[bool, str]:
        """
        Determine if trading should occur on given date based on VIX regime
        Returns: (should_trade, reason)
        """
        regime = self.get_market_regime(date)
        
        if regime['vix_regime'] == 'low_vix':
            return False, f"VIX too low ({regime['vix']:.1f} < {self.VIX_LOW_THRESHOLD})"
        
        return True, f"VIX regime favorable ({regime['vix_regime']}: {regime['vix']:.1f})"
    
    def get_preferred_option_type(self, date: datetime, signal_type: str) -> Tuple[str, str]:
        """
        Get preferred option type based on UTY momentum and signal
        Returns: (option_type, reason)
        """
        regime = self.get_market_regime(date)
        
        # Base signal preference
        base_preference = 'call' if signal_type == 'BULLISH' else 'put'
        
        # UTY momentum adjustment
        if regime['uty_direction'] == 'rising':
            # UTY rising favors put trades (defensive rotation)
            preferred = 'put'
            confidence = "high" if base_preference == 'put' else "low"
            reason = f"UTY rising ({regime['uty_momentum']:+.1f}%) favors puts"
        else:
            # UTY falling favors call trades (risk-on rotation)
            preferred = 'call'
            confidence = "high" if base_preference == 'call' else "low"
            reason = f"UTY falling ({regime['uty_momentum']:+.1f}%) favors calls"
        
        # If signal and UTY momentum align, use preferred type
        # If they conflict, stick with signal but note lower confidence
        if confidence == "high":
            return preferred, f"{reason} (aligns with {signal_type} signal)"
        else:
            return base_preference, f"{reason} (conflicts with {signal_type} signal - using signal)"
    
    def get_position_multiplier(self, date: datetime) -> float:
        """Get position size multiplier based on VIX regime"""
        regime = self.get_market_regime(date)
        return regime['position_multiplier']
    
    def get_regime_summary(self, date: datetime) -> str:
        """Get human-readable regime summary for logging"""
        regime = self.get_market_regime(date)
        return (f"VIX: {regime['vix']:.1f} ({regime['vix_regime']}) | "
                f"UTY: {regime['uty']:.1f} ({regime['uty_direction']} {regime['uty_momentum']:+.1f}%) | "
                f"Position Multiplier: {regime['position_multiplier']:.1f}x")

    def get_regime_stats(self, start_date: datetime, end_date: datetime) -> Dict:
        """Get regime statistics for a date range"""
        try:
            mask = (self.market_data['date'] >= start_date) & (self.market_data['date'] <= end_date)
            period_data = self.market_data[mask]

            if len(period_data) == 0:
                return {}

            regime_counts = period_data['vix_regime'].value_counts()
            uty_direction_counts = period_data['uty_momentum_direction'].value_counts()

            return {
                'total_days': len(period_data),
                'vix_regimes': {
                    'low_vix_days': regime_counts.get('low_vix', 0),
                    'normal_vix_days': regime_counts.get('normal_vix', 0),
                    'high_vix_days': regime_counts.get('high_vix', 0),
                    'low_vix_pct': (regime_counts.get('low_vix', 0) / len(period_data)) * 100,
                    'normal_vix_pct': (regime_counts.get('normal_vix', 0) / len(period_data)) * 100,
                    'high_vix_pct': (regime_counts.get('high_vix', 0) / len(period_data)) * 100
                },
                'uty_momentum': {
                    'rising_days': uty_direction_counts.get('rising', 0),
                    'falling_days': uty_direction_counts.get('falling', 0),
                    'rising_pct': (uty_direction_counts.get('rising', 0) / len(period_data)) * 100,
                    'falling_pct': (uty_direction_counts.get('falling', 0) / len(period_data)) * 100
                },
                'avg_vix': period_data['vix'].mean(),
                'avg_uty_momentum': period_data['uty_momentum'].mean()
            }

        except Exception as e:
            self.logger.error(f"Error calculating regime stats: {e}")
            return {}

    def validate_regime_filter(self) -> bool:
        """Validate that regime filter is working correctly"""
        try:
            if self.market_data is None or len(self.market_data) == 0:
                self.logger.error("No market data loaded")
                return False

            # Check for required columns
            required_cols = ['date', 'vix', 'uty', 'vix_regime', 'uty_momentum', 'uty_momentum_direction']
            missing_cols = [col for col in required_cols if col not in self.market_data.columns]

            if missing_cols:
                self.logger.error(f"Missing required columns: {missing_cols}")
                return False

            # Check data quality
            vix_nulls = self.market_data['vix'].isnull().sum()
            uty_nulls = self.market_data['uty'].isnull().sum()

            if vix_nulls > 0 or uty_nulls > 0:
                self.logger.warning(f"Found null values - VIX: {vix_nulls}, UTY: {uty_nulls}")

            # Test regime classification
            test_date = datetime.now().date()
            regime = self.get_market_regime(test_date)

            if not all(key in regime for key in ['vix', 'vix_regime', 'uty_direction', 'position_multiplier']):
                self.logger.error("Regime classification missing required fields")
                return False

            self.logger.info("✅ Market regime filter validation passed")
            return True

        except Exception as e:
            self.logger.error(f"Regime filter validation failed: {e}")
            return False
