"""
Regime-Based Options Trading Playbook
Implements comprehensive trading strategies based on enhanced market regime analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass

@dataclass
class OptionStrategy:
    """Data class for option strategy specifications"""
    name: str
    option_type: str  # 'call', 'put', 'call_spread', 'put_spread', 'iron_condor', 'straddle', etc.
    strike_selection: str  # 'ATM', 'OTM_25', 'ITM_25', etc.
    expiration_days: Tuple[int, int]  # (min_days, max_days)
    position_multiplier: float
    max_risk_percent: float
    profit_target_percent: float
    stop_loss_percent: float
    quality_threshold: float
    entry_criteria: List[str]
    exit_criteria: List[str]

class RegimeBasedOptionsPlaybook:
    """
    Comprehensive options trading playbook based on market regime analysis
    Combines VIX levels, term structure, UTY momentum, and Greeks analysis
    """
    
    def __init__(self):
        """Initialize the options trading playbook"""
        self.logger = logging.getLogger(__name__)
        
        # Optimized quality score thresholds (more permissive for increased trade frequency)
        self.QUALITY_THRESHOLDS = {
            'maximum': 0.6,      # Maximum position sizing + aggressive strategies
            'standard': 0.4,     # Standard position sizing + preferred strategies
            'reduced': 0.3,      # Reduced position sizing + conservative strategies
            'minimal': 0.2,      # Minimal position sizing + defensive strategies only
            'no_trade': 0.15     # Below this = no trades (lowered from 0.2 to 0.15)
        }
        
        # Greeks convergence thresholds
        self.VANNA_STRONG_THRESHOLD = 0.5
        self.VANNA_WEAK_THRESHOLD = 0.3
        self.CHARM_HIGH_THRESHOLD = 0.8
        self.CHARM_LOW_THRESHOLD = 0.3
        
        # Drift thresholds for neutral handling
        self.DRIFT_BULLISH_THRESHOLD = 0.003  # 0.3%
        self.DRIFT_BEARISH_THRESHOLD = -0.003  # -0.3%
        
        # Initialize strategy definitions
        self._initialize_strategies()
    
    def _initialize_strategies(self):
        """Initialize all strategy definitions for different regime combinations"""
        
        # OPTIMAL VIX REGIME (12-18) STRATEGIES
        self.optimal_vix_strategies = {
            ('contango', 'rising'): OptionStrategy(
                name="Defensive Put Spreads",
                option_type="put_spread",
                strike_selection="ATM_sell_OTM25_buy",
                expiration_days=(21, 30),
                position_multiplier=2.5,
                max_risk_percent=2.0,
                profit_target_percent=50.0,
                stop_loss_percent=150.0,
                quality_threshold=0.2,
                entry_criteria=["put_wall_strength>5.0", "negative_drift", "quality>0.4"],
                exit_criteria=["profit_50%", "7_days_to_expiry"]
            ),
            
            ('contango', 'falling'): OptionStrategy(
                name="Aggressive Call Spreads",
                option_type="call_spread", 
                strike_selection="ATM_buy_OTM50_sell",
                expiration_days=(14, 21),
                position_multiplier=3.0,
                max_risk_percent=3.0,
                profit_target_percent=75.0,
                stop_loss_percent=100.0,
                quality_threshold=0.25,
                entry_criteria=["call_wall_strength>5.0", "positive_drift", "quality>0.5"],
                exit_criteria=["profit_75%", "5_days_to_expiry"]
            ),
            
            ('backwardation', 'rising'): OptionStrategy(
                name="Long Puts",
                option_type="long_put",
                strike_selection="OTM_25",
                expiration_days=(7, 14),
                position_multiplier=2.0,
                max_risk_percent=2.0,
                profit_target_percent=100.0,
                stop_loss_percent=50.0,
                quality_threshold=0.2,
                entry_criteria=["vanna_convergence>0.5", "negative_drift", "quality>0.3"],
                exit_criteria=["profit_100%", "3_days_to_expiry"]
            ),
            
            ('backwardation', 'falling'): OptionStrategy(
                name="Iron Condors",
                option_type="iron_condor",
                strike_selection="OTM_25_wings_50",
                expiration_days=(21, 28),
                position_multiplier=1.5,
                max_risk_percent=1.5,
                profit_target_percent=50.0,
                stop_loss_percent=200.0,
                quality_threshold=0.2,
                entry_criteria=["low_gamma_walls", "minimal_drift", "quality>0.2"],
                exit_criteria=["profit_50%", "10_days_to_expiry"]
            ),
            
            ('neutral', 'rising'): OptionStrategy(
                name="Put Butterflies",
                option_type="put_butterfly",
                strike_selection="ATM_center_25_wings",
                expiration_days=(14, 21),
                position_multiplier=2.0,
                max_risk_percent=1.5,
                profit_target_percent=75.0,
                stop_loss_percent=100.0,
                quality_threshold=0.2,
                entry_criteria=["put_wall_dominance", "charm_convergence", "quality>0.2"],
                exit_criteria=["profit_75%", "7_days_to_expiry"]
            ),
            
            ('neutral', 'falling'): OptionStrategy(
                name="Call Butterflies",
                option_type="call_butterfly",
                strike_selection="OTM_25_center_25_wings",
                expiration_days=(14, 21),
                position_multiplier=2.5,
                max_risk_percent=2.0,
                profit_target_percent=75.0,
                stop_loss_percent=100.0,
                quality_threshold=0.25,
                entry_criteria=["call_wall_resistance", "positive_vanna", "quality>0.25"],
                exit_criteria=["profit_75%", "7_days_to_expiry"]
            )
        }
        
        # NORMAL VIX REGIME (18-25) STRATEGIES
        self.normal_vix_strategies = {
            ('contango', 'rising'): OptionStrategy(
                name="Conservative Put Spreads",
                option_type="put_spread",
                strike_selection="ATM_sell_OTM75_buy",
                expiration_days=(28, 35),
                position_multiplier=1.5,
                max_risk_percent=1.5,
                profit_target_percent=50.0,
                stop_loss_percent=150.0,
                quality_threshold=0.2,
                entry_criteria=["strong_put_walls", "quality>0.2"],
                exit_criteria=["profit_50%", "7_days_to_expiry"]
            ),
            
            ('contango', 'falling'): OptionStrategy(
                name="Conservative Call Spreads",
                option_type="call_spread",
                strike_selection="OTM_25_buy_OTM75_sell",
                expiration_days=(21, 28),
                position_multiplier=2.0,
                max_risk_percent=2.0,
                profit_target_percent=60.0,
                stop_loss_percent=120.0,
                quality_threshold=0.2,
                entry_criteria=["call_wall_support", "quality>0.2"],
                exit_criteria=["profit_60%", "7_days_to_expiry"]
            ),
            
            ('backwardation', 'rising'): OptionStrategy(
                name="Moderate Long Puts",
                option_type="long_put",
                strike_selection="ATM_to_OTM25",
                expiration_days=(14, 21),
                position_multiplier=1.5,
                max_risk_percent=1.5,
                profit_target_percent=80.0,
                stop_loss_percent=40.0,
                quality_threshold=0.2,
                entry_criteria=["high_vanna_readings", "quality>0.2"],
                exit_criteria=["profit_80%", "5_days_to_expiry"]
            ),
            
            ('backwardation', 'falling'): OptionStrategy(
                name="Long Straddles",
                option_type="long_straddle",
                strike_selection="ATM",
                expiration_days=(21, 28),
                position_multiplier=1.0,
                max_risk_percent=1.5,
                profit_target_percent=100.0,
                stop_loss_percent=50.0,
                quality_threshold=0.2,
                entry_criteria=["low_realized_vol", "quality>0.2"],
                exit_criteria=["vol_expansion", "10_days_to_expiry"]
            ),
            
            ('neutral', 'rising'): OptionStrategy(
                name="Defensive Put Spreads",
                option_type="put_spread",
                strike_selection="conservative_50_point",
                expiration_days=(21, 28),
                position_multiplier=1.5,
                max_risk_percent=1.5,
                profit_target_percent=50.0,
                stop_loss_percent=150.0,
                quality_threshold=0.2,
                entry_criteria=["quality>0.2"],
                exit_criteria=["profit_50%", "7_days_to_expiry"]
            ),
            
            ('neutral', 'falling'): OptionStrategy(
                name="Moderate Call Spreads",
                option_type="call_spread",
                strike_selection="OTM_slight_50_point",
                expiration_days=(21, 28),
                position_multiplier=1.8,
                max_risk_percent=1.8,
                profit_target_percent=60.0,
                stop_loss_percent=120.0,
                quality_threshold=0.2,
                entry_criteria=["quality>0.2"],
                exit_criteria=["profit_60%", "7_days_to_expiry"]
            )
        }
        
        # HIGH VIX REGIME (25-30) STRATEGIES
        self.high_vix_strategies = {
            ('contango', 'rising'): OptionStrategy(
                name="Cash Secured Puts",
                option_type="cash_secured_put",
                strike_selection="OTM_10_15_percent",
                expiration_days=(30, 45),
                position_multiplier=1.0,
                max_risk_percent=1.0,
                profit_target_percent=50.0,
                stop_loss_percent=200.0,
                quality_threshold=0.2,
                entry_criteria=["high_put_premiums", "quality>0.2"],
                exit_criteria=["profit_50%", "assignment_acceptable"]
            ),
            
            ('contango', 'falling'): OptionStrategy(
                name="Covered Calls",
                option_type="covered_call",
                strike_selection="OTM_5_10_percent",
                expiration_days=(21, 30),
                position_multiplier=1.0,
                max_risk_percent=1.0,
                profit_target_percent=50.0,
                stop_loss_percent=200.0,
                quality_threshold=0.2,
                entry_criteria=["high_call_premiums", "quality>0.2"],
                exit_criteria=["profit_50%", "income_focus"]
            ),
            
            ('backwardation', 'rising'): OptionStrategy(
                name="Protective Puts",
                option_type="protective_put",
                strike_selection="OTM_5_10_percent",
                expiration_days=(30, 60),
                position_multiplier=0.5,
                max_risk_percent=1.0,
                profit_target_percent=0.0,  # Insurance, not profit
                stop_loss_percent=100.0,
                quality_threshold=0.2,
                entry_criteria=["portfolio_protection_needed", "quality>0.2"],
                exit_criteria=["protection_no_longer_needed", "expiry"]
            ),
            
            ('backwardation', 'falling'): OptionStrategy(
                name="Volatility Straddles",
                option_type="long_straddle",
                strike_selection="ATM",
                expiration_days=(30, 45),
                position_multiplier=0.8,
                max_risk_percent=1.0,
                profit_target_percent=100.0,
                stop_loss_percent=50.0,
                quality_threshold=0.2,
                entry_criteria=["expecting_large_moves", "quality>0.2"],
                exit_criteria=["vol_contraction", "profit_100%"]
            ),
            
            ('neutral', 'rising'): OptionStrategy(
                name="Wide Put Spreads",
                option_type="put_spread",
                strike_selection="wide_100_150_point",
                expiration_days=(30, 45),
                position_multiplier=1.0,
                max_risk_percent=1.0,
                profit_target_percent=40.0,
                stop_loss_percent=150.0,
                quality_threshold=0.2,
                entry_criteria=["quality>0.2"],
                exit_criteria=["profit_40%", "10_days_to_expiry"]
            ),
            
            ('neutral', 'falling'): OptionStrategy(
                name="Wide Call Spreads",
                option_type="call_spread",
                strike_selection="wide_100_150_point",
                expiration_days=(30, 45),
                position_multiplier=1.0,
                max_risk_percent=1.0,
                profit_target_percent=40.0,
                stop_loss_percent=150.0,
                quality_threshold=0.2,
                entry_criteria=["quality>0.2"],
                exit_criteria=["profit_40%", "10_days_to_expiry"]
            )
        }
    
    def get_strategy_for_regime(self, vix_regime: str, term_structure: str, uty_direction: str, 
                               quality_score: float) -> Optional[OptionStrategy]:
        """
        Get the appropriate trading strategy for the given market regime
        
        Args:
            vix_regime: 'optimal_vix', 'normal_vix', 'high_vix', 'extreme_vix', 'low_vix'
            term_structure: 'contango', 'backwardation', 'neutral'
            uty_direction: 'rising', 'falling', 'neutral'
            quality_score: Regime quality score (0-1)
            
        Returns:
            OptionStrategy object or None if no suitable strategy
        """
        
        # Check minimum quality threshold
        if quality_score < self.QUALITY_THRESHOLDS['no_trade']:
            self.logger.info(f"Quality score {quality_score:.2f} below minimum threshold {self.QUALITY_THRESHOLDS['no_trade']}")
            return None
        
        # Handle extreme regimes
        if vix_regime == 'extreme_vix':
            self.logger.info(f"VIX regime {vix_regime} - no trading")
            return None
            
        if vix_regime == 'low_vix':
            self.logger.info(f"VIX regime {vix_regime} - no trading")
            return None
        
        # Select strategy set based on VIX regime
        if vix_regime == 'optimal_vix':
            strategy_set = self.optimal_vix_strategies
        elif vix_regime == 'normal_vix':
            strategy_set = self.normal_vix_strategies
        elif vix_regime == 'high_vix':
            strategy_set = self.high_vix_strategies
        else:
            self.logger.warning(f"Unknown VIX regime: {vix_regime}")
            return None
        
        # Get strategy key
        strategy_key = (term_structure, uty_direction)
        
        # Try to get exact match
        if strategy_key in strategy_set:
            strategy = strategy_set[strategy_key]
            
            # Check if quality meets strategy threshold
            if quality_score >= strategy.quality_threshold:
                return strategy
            else:
                self.logger.info(f"Quality score {quality_score:.2f} below strategy threshold {strategy.quality_threshold}")
                return None
        
        # Handle neutral UTY direction with fallback logic
        if uty_direction == 'neutral':
            return self._handle_neutral_uty_direction(strategy_set, term_structure, quality_score)
        
        self.logger.warning(f"No strategy found for regime: {vix_regime}, {term_structure}, {uty_direction}")
        return None

    def _handle_neutral_uty_direction(self, strategy_set: Dict, term_structure: str,
                                    quality_score: float) -> Optional[OptionStrategy]:
        """
        Handle neutral UTY direction with fallback logic
        Try both rising and falling, then use Greeks/drift analysis
        """

        # Try rising first, then falling
        for fallback_direction in ['rising', 'falling']:
            strategy_key = (term_structure, fallback_direction)
            if strategy_key in strategy_set:
                strategy = strategy_set[strategy_key]
                if quality_score >= strategy.quality_threshold:
                    self.logger.info(f"Using {fallback_direction} strategy for neutral UTY direction")
                    return strategy

        # If no suitable strategy found, return None
        return None

    def enhance_strategy_with_greeks(self, strategy: OptionStrategy, greeks_data: Dict,
                                   wall_data: Dict, drift: float) -> OptionStrategy:
        """
        Enhance strategy selection and parameters based on Greeks convergence and options walls

        Args:
            strategy: Base strategy from regime analysis
            greeks_data: Dictionary with vanna, charm, gamma, etc.
            wall_data: Dictionary with call/put wall strengths
            drift: Overnight drift value

        Returns:
            Enhanced OptionStrategy with modified parameters
        """

        enhanced_strategy = strategy

        # Greeks-based modifications
        vanna = greeks_data.get('vanna_convergence', 0)
        charm = greeks_data.get('charm_decay_signal', 0)

        # Vanna convergence adjustments
        if abs(vanna) > self.VANNA_STRONG_THRESHOLD:
            if vanna > 0 and 'call' in strategy.option_type:
                # Strong positive vanna supports call strategies
                enhanced_strategy.position_multiplier *= 1.2
                enhanced_strategy.profit_target_percent *= 1.1
            elif vanna < 0 and 'put' in strategy.option_type:
                # Strong negative vanna supports put strategies
                enhanced_strategy.position_multiplier *= 1.2
                enhanced_strategy.profit_target_percent *= 1.1
            else:
                # Vanna divergence - reduce confidence
                enhanced_strategy.position_multiplier *= 0.8

        # Charm decay adjustments
        if abs(charm) > self.CHARM_HIGH_THRESHOLD:
            # High charm - use shorter expiries
            min_days, max_days = enhanced_strategy.expiration_days
            enhanced_strategy.expiration_days = (max(7, min_days - 7), max(14, max_days - 7))
        elif abs(charm) < self.CHARM_LOW_THRESHOLD:
            # Low charm - use longer expiries
            min_days, max_days = enhanced_strategy.expiration_days
            enhanced_strategy.expiration_days = (min_days + 7, max_days + 7)

        # Options wall adjustments
        call_strength = wall_data.get('call_wall_strength', 0)
        put_strength = wall_data.get('put_wall_strength', 0)

        if call_strength > 5.0 and 'call' in strategy.option_type:
            # Strong call walls provide resistance - reduce call strategy confidence
            enhanced_strategy.position_multiplier *= 0.9
        elif put_strength > 5.0 and 'put' in strategy.option_type:
            # Strong put walls provide support - reduce put strategy confidence
            enhanced_strategy.position_multiplier *= 0.9

        # Drift confirmation
        if abs(drift) > self.DRIFT_BULLISH_THRESHOLD:
            if drift > 0 and 'call' in strategy.option_type:
                # Positive drift supports call strategies
                enhanced_strategy.position_multiplier *= 1.1
            elif drift < 0 and 'put' in strategy.option_type:
                # Negative drift supports put strategies
                enhanced_strategy.position_multiplier *= 1.1

        # Cap position multiplier
        enhanced_strategy.position_multiplier = min(enhanced_strategy.position_multiplier, 4.0)
        enhanced_strategy.position_multiplier = max(enhanced_strategy.position_multiplier, 0.5)

        return enhanced_strategy

    def handle_neutral_regime_fallback(self, greeks_data: Dict, wall_data: Dict,
                                     drift: float, quality_score: float) -> Optional[OptionStrategy]:
        """
        Handle cases where regime analysis returns 'neutral' for option type preference
        Uses Greeks dominance, wall analysis, and drift to determine strategy
        """

        vanna = greeks_data.get('vanna_convergence', 0)
        call_strength = wall_data.get('call_wall_strength', 0)
        put_strength = wall_data.get('put_wall_strength', 0)

        # Check Greeks dominance first
        if abs(vanna) > self.VANNA_STRONG_THRESHOLD:
            if vanna > 0:
                # Positive vanna - bullish bias
                return self._create_neutral_fallback_strategy('call', quality_score, "positive_vanna")
            else:
                # Negative vanna - bearish bias
                return self._create_neutral_fallback_strategy('put', quality_score, "negative_vanna")

        # Check wall dominance
        if call_strength > put_strength * 1.5:
            # Strong call walls - resistance, favor puts
            return self._create_neutral_fallback_strategy('put', quality_score, "call_wall_resistance")
        elif put_strength > call_strength * 1.5:
            # Strong put walls - support, favor calls
            return self._create_neutral_fallback_strategy('call', quality_score, "put_wall_support")

        # Check drift
        if abs(drift) > self.DRIFT_BULLISH_THRESHOLD:
            if drift > 0:
                return self._create_neutral_fallback_strategy('call', quality_score, "positive_drift")
            else:
                return self._create_neutral_fallback_strategy('put', quality_score, "negative_drift")

        # Default to range-bound strategy
        return self._create_neutral_fallback_strategy('iron_condor', quality_score, "range_bound_default")

    def _create_neutral_fallback_strategy(self, option_bias: str, quality_score: float,
                                        reason: str) -> OptionStrategy:
        """Create a fallback strategy for neutral regime conditions"""

        if option_bias == 'call':
            return OptionStrategy(
                name=f"Neutral Fallback Call Strategy ({reason})",
                option_type="call_spread",
                strike_selection="OTM_25_buy_OTM50_sell",
                expiration_days=(21, 28),
                position_multiplier=1.0,
                max_risk_percent=1.5,
                profit_target_percent=60.0,
                stop_loss_percent=120.0,
                quality_threshold=0.15,
                entry_criteria=[f"neutral_fallback_{reason}", "quality>0.15"],
                exit_criteria=["profit_60%", "7_days_to_expiry"]
            )
        elif option_bias == 'put':
            return OptionStrategy(
                name=f"Neutral Fallback Put Strategy ({reason})",
                option_type="put_spread",
                strike_selection="ATM_sell_OTM25_buy",
                expiration_days=(21, 28),
                position_multiplier=1.0,
                max_risk_percent=1.5,
                profit_target_percent=60.0,
                stop_loss_percent=120.0,
                quality_threshold=0.15,
                entry_criteria=[f"neutral_fallback_{reason}", "quality>0.15"],
                exit_criteria=["profit_60%", "7_days_to_expiry"]
            )
        else:  # iron_condor
            return OptionStrategy(
                name=f"Neutral Fallback Range Strategy ({reason})",
                option_type="iron_condor",
                strike_selection="OTM_25_wings_50",
                expiration_days=(21, 28),
                position_multiplier=1.0,
                max_risk_percent=1.5,
                profit_target_percent=50.0,
                stop_loss_percent=200.0,
                quality_threshold=0.15,
                entry_criteria=[f"neutral_fallback_{reason}", "quality>0.15"],
                exit_criteria=["profit_50%", "10_days_to_expiry"]
            )

    def calculate_position_size(self, strategy: OptionStrategy, base_capital: float,
                              current_price: float, option_price: float) -> int:
        """
        Calculate position size based on strategy parameters and risk management

        Args:
            strategy: OptionStrategy with position multiplier and risk limits
            base_capital: Available trading capital
            current_price: Current underlying price
            option_price: Option entry price

        Returns:
            Number of contracts to trade
        """

        # Calculate maximum risk amount
        max_risk_amount = base_capital * (strategy.max_risk_percent / 100.0)

        # Calculate base position size (1-2 contracts base)
        base_contracts = 1

        # Apply strategy multiplier
        target_contracts = int(base_contracts * strategy.position_multiplier)

        # Risk-based position sizing
        if option_price > 0:
            # For long options: risk = premium paid
            if 'long' in strategy.option_type or strategy.option_type in ['call', 'put']:
                max_contracts_by_risk = int(max_risk_amount / (option_price * 100))
            # For spreads: estimate max risk (simplified)
            else:
                estimated_spread_risk = option_price * 100 * 2  # Conservative estimate
                max_contracts_by_risk = int(max_risk_amount / estimated_spread_risk)

            # Use smaller of target and risk-limited size
            final_contracts = min(target_contracts, max(1, max_contracts_by_risk))
        else:
            final_contracts = target_contracts

        # Cap at reasonable limits
        final_contracts = max(1, min(final_contracts, 20))

        return final_contracts

    def should_exit_position(self, strategy: OptionStrategy, entry_date: datetime,
                           current_date: datetime, entry_price: float, current_price: float,
                           current_pnl: float) -> Tuple[bool, str]:
        """
        Determine if position should be exited based on strategy criteria

        Returns:
            (should_exit, reason)
        """

        days_held = (current_date - entry_date).days
        pnl_percent = (current_pnl / (entry_price * 100)) * 100 if entry_price > 0 else 0

        # Profit target check
        if pnl_percent >= strategy.profit_target_percent:
            return True, f"Profit target reached: {pnl_percent:.1f}% >= {strategy.profit_target_percent:.1f}%"

        # Stop loss check
        if pnl_percent <= -strategy.stop_loss_percent:
            return True, f"Stop loss hit: {pnl_percent:.1f}% <= -{strategy.stop_loss_percent:.1f}%"

        # Time-based exit (extract from expiration_days)
        min_days, max_days = strategy.expiration_days
        if days_held >= max_days - 7:  # Exit 7 days before expiry
            return True, f"Time exit: {days_held} days held, approaching expiry"

        return False, "No exit criteria met"
