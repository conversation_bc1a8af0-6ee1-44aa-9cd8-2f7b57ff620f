"""
Enhanced Market Regime Filter for JPM Collar Strategy
Combines optimized VIX thresholds with VIX term structure analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Tuple, Optional
import logging
from pathlib import Path
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.market_regime_filter import MarketRegimeFilter
from src.vix_term_structure_analyzer import VIXTermStructureAnalyzer

class EnhancedMarketRegimeFilter:
    """
    Enhanced market regime filter combining:
    1. Optimized VIX thresholds (12-18 range)
    2. VIX term structure analysis (contango/backwardation)
    3. UTY momentum analysis
    4. Dynamic position sizing
    """
    
    def __init__(self, securities_path: str = "/Users/<USER>/Downloads/systems/strategy_package/data/securities/"):
        """Initialize enhanced market regime filter"""
        self.securities_path = securities_path
        self.logger = logging.getLogger(__name__)
        
        # Initialize base components
        self.base_regime_filter = MarketRegimeFilter(securities_path)
        self.term_structure_analyzer = VIXTermStructureAnalyzer(securities_path)
        
        # Enhanced VIX thresholds (optimized from analysis)
        self.VIX_LOW_THRESHOLD = 12.0    # Optimized from 15.0
        self.VIX_HIGH_THRESHOLD = 18.0   # Optimized from 25.0
        self.VIX_EXTREME_THRESHOLD = 30.0
        
        # Enhanced position sizing multipliers
        self.POSITION_MULTIPLIERS = {
            'optimal_vix': 2.0,      # VIX 12-18 range (optimal)
            'normal_vix': 1.5,       # VIX 18-25 range
            'high_vix': 1.0,         # VIX 25-30 range
            'extreme_vix': 0.5,      # VIX >30 range
            'low_vix': 0.0           # VIX <12 range (no trades)
        }
        
        # Term structure adjustments
        self.TERM_STRUCTURE_MULTIPLIERS = {
            'contango': 1.1,         # Slightly increase in contango
            'neutral': 1.0,          # No adjustment
            'backwardation': 0.9     # Slightly decrease in backwardation
        }
        
        # Initialize components
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize all regime analysis components"""
        try:
            # Initialize term structure analyzer
            self.term_structure_analyzer.calculate_term_structure_proxies()
            print("✅ Enhanced market regime filter initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize enhanced regime filter: {e}")
            print(f"⚠️ Enhanced regime filter initialization failed: {e}")
    
    def get_enhanced_market_regime(self, date: datetime) -> Dict:
        """Get comprehensive market regime analysis for a specific date"""
        
        # Get base regime data
        base_regime = self.base_regime_filter.get_market_regime(date)
        
        # Get term structure data
        term_structure = self.term_structure_analyzer.get_term_structure_regime(date)
        
        # Enhanced VIX regime classification
        vix = base_regime['vix']
        if vix < self.VIX_LOW_THRESHOLD:
            vix_regime = 'low_vix'
        elif vix <= self.VIX_HIGH_THRESHOLD:
            vix_regime = 'optimal_vix'
        elif vix <= 25.0:
            vix_regime = 'normal_vix'
        elif vix <= self.VIX_EXTREME_THRESHOLD:
            vix_regime = 'high_vix'
        else:
            vix_regime = 'extreme_vix'
        
        # Calculate enhanced position multiplier
        base_multiplier = self.POSITION_MULTIPLIERS[vix_regime]
        term_structure_multiplier = self.TERM_STRUCTURE_MULTIPLIERS.get(
            term_structure['term_structure_regime'], 1.0
        )
        
        # Additional term structure adjustment
        ts_adjustment = self.term_structure_analyzer.get_position_adjustment(date)
        
        # Combined position multiplier
        enhanced_position_multiplier = base_multiplier * term_structure_multiplier * ts_adjustment
        enhanced_position_multiplier = max(0.0, min(3.0, enhanced_position_multiplier))  # Cap at 3x
        
        # Combine all regime data
        enhanced_regime = {
            **base_regime,  # Include all base regime data
            **term_structure,  # Include all term structure data
            'enhanced_vix_regime': vix_regime,
            'base_position_multiplier': base_multiplier,
            'term_structure_multiplier': term_structure_multiplier,
            'ts_adjustment': ts_adjustment,
            'enhanced_position_multiplier': enhanced_position_multiplier,
            'regime_quality_score': self._calculate_regime_quality(base_regime, term_structure, vix_regime)
        }
        
        return enhanced_regime
    
    def _calculate_regime_quality(self, base_regime: Dict, term_structure: Dict, vix_regime: str) -> float:
        """Calculate a quality score for the current market regime (0-1 scale)"""
        
        score = 0.5  # Base score
        
        # VIX regime scoring
        vix_scores = {
            'optimal_vix': 1.0,
            'normal_vix': 0.7,
            'high_vix': 0.4,
            'extreme_vix': 0.2,
            'low_vix': 0.0
        }
        score *= vix_scores.get(vix_regime, 0.5)
        
        # Term structure scoring
        ts_regime = term_structure['term_structure_regime']
        if ts_regime == 'neutral':
            score *= 1.0
        elif ts_regime == 'contango':
            score *= 0.9  # Slightly less favorable
        else:  # backwardation
            score *= 0.8  # Less favorable
        
        # VIX z-score adjustment
        vix_zscore = abs(term_structure.get('vix_zscore', 0))
        if vix_zscore > 2.0:
            score *= 0.6  # Extreme VIX levels
        elif vix_zscore > 1.0:
            score *= 0.8  # Elevated VIX levels
        
        # Volatility clustering adjustment
        vol_clustering = term_structure.get('vol_clustering', 1.0)
        if vol_clustering > 1.5:
            score *= 0.7  # High volatility clustering
        elif vol_clustering < 0.7:
            score *= 1.1  # Low volatility clustering (favorable)
        
        return max(0.0, min(1.0, score))
    
    def should_trade_enhanced(self, date: datetime) -> Tuple[bool, str]:
        """
        Enhanced trade decision based on multiple regime factors
        Returns: (should_trade, detailed_reason)
        """
        
        regime = self.get_enhanced_market_regime(date)
        
        # Primary filter: VIX regime
        if regime['enhanced_vix_regime'] == 'low_vix':
            return False, f"VIX too low ({regime['vix']:.1f} < {self.VIX_LOW_THRESHOLD})"
        
        if regime['enhanced_vix_regime'] == 'extreme_vix':
            return False, f"VIX too high ({regime['vix']:.1f} > {self.VIX_EXTREME_THRESHOLD})"
        
        # Secondary filter: Term structure stress
        should_trade_ts, ts_reason = self.term_structure_analyzer.should_trade_term_structure(date)
        if not should_trade_ts:
            return False, f"Term structure filter: {ts_reason}"
        
        # Quality filter: Regime quality score
        if regime['regime_quality_score'] < 0.3:
            return False, f"Low regime quality score ({regime['regime_quality_score']:.2f})"
        
        # All filters passed
        reason = (f"Enhanced regime favorable: VIX {regime['vix']:.1f} ({regime['enhanced_vix_regime']}), "
                 f"TS {regime['term_structure_regime']}, Quality {regime['regime_quality_score']:.2f}")
        
        return True, reason
    
    def get_enhanced_position_multiplier(self, date: datetime) -> float:
        """Get enhanced position multiplier for a specific date"""
        regime = self.get_enhanced_market_regime(date)
        return regime['enhanced_position_multiplier']
    
    def get_option_type_preference(self, date: datetime) -> Tuple[str, str]:
        """
        Get option type preference based on enhanced regime analysis
        Returns: (preferred_type, reason)
        """
        
        regime = self.get_enhanced_market_regime(date)
        
        # Base UTY momentum preference
        base_preference, base_reason = self.base_regime_filter.get_option_type_preference(date)
        
        # Term structure adjustment
        ts_regime = regime['term_structure_regime']
        
        if ts_regime == 'backwardation':
            # Backwardation often precedes volatility spikes (favor puts)
            if base_preference == 'puts':
                return 'puts', f"{base_reason} + backwardation reinforces bearish bias"
            else:
                return 'neutral', f"{base_reason} but backwardation suggests caution"
        
        elif ts_regime == 'contango':
            # Contango often indicates lower expected volatility (favor calls)
            if base_preference == 'calls':
                return 'calls', f"{base_reason} + contango reinforces bullish bias"
            else:
                return 'neutral', f"{base_reason} but contango suggests lower volatility"
        
        # Neutral term structure - use base preference
        return base_preference, f"{base_reason} (neutral term structure)"
    
    def generate_enhanced_regime_report(self, start_date: datetime, end_date: datetime) -> Dict:
        """Generate comprehensive regime analysis report"""
        
        date_range = pd.date_range(start_date, end_date, freq='D')
        regime_data = []
        
        for date in date_range:
            regime = self.get_enhanced_market_regime(date)
            should_trade, reason = self.should_trade_enhanced(date)
            
            regime_data.append({
                'date': date,
                'vix': regime['vix'],
                'enhanced_vix_regime': regime['enhanced_vix_regime'],
                'term_structure_regime': regime['term_structure_regime'],
                'combined_regime': regime['combined_regime'],
                'should_trade': should_trade,
                'position_multiplier': regime['enhanced_position_multiplier'],
                'regime_quality_score': regime['regime_quality_score'],
                'reason': reason
            })
        
        regime_df = pd.DataFrame(regime_data)
        
        # Calculate regime statistics
        regime_stats = {
            'total_days': len(regime_df),
            'trading_days': regime_df['should_trade'].sum(),
            'trading_percentage': regime_df['should_trade'].mean() * 100,
            'avg_position_multiplier': regime_df[regime_df['should_trade']]['position_multiplier'].mean(),
            'regime_distribution': regime_df['enhanced_vix_regime'].value_counts().to_dict(),
            'term_structure_distribution': regime_df['term_structure_regime'].value_counts().to_dict(),
            'avg_quality_score': regime_df['regime_quality_score'].mean()
        }
        
        return {
            'regime_data': regime_df,
            'regime_stats': regime_stats
        }

    def get_preferred_option_type(self, date: datetime, signal_type: str) -> Tuple[str, str]:
        """Get preferred option type based on enhanced regime analysis"""
        return self.get_option_type_preference(date)

    def get_regime_summary(self, date: datetime) -> str:
        """Get a summary string of the current market regime"""
        regime = self.get_enhanced_market_regime(date)
        return (f"VIX: {regime['vix']:.1f} ({regime['enhanced_vix_regime']}) | "
                f"TS: {regime['term_structure_regime']} | "
                f"UTY: {regime['uty_direction']} | "
                f"Quality: {regime['regime_quality_score']:.2f}")

if __name__ == "__main__":
    # Test enhanced regime filter
    enhanced_filter = EnhancedMarketRegimeFilter()
    
    # Test with sample dates
    test_dates = [
        datetime(2023, 3, 15),  # Banking crisis
        datetime(2023, 8, 15),  # Normal market
        datetime(2024, 1, 15),  # Low VIX period
        datetime(2024, 8, 15),  # Recent period
    ]
    
    print("🔍 ENHANCED REGIME ANALYSIS TEST")
    print("=" * 60)
    
    for test_date in test_dates:
        regime = enhanced_filter.get_enhanced_market_regime(test_date)
        should_trade, reason = enhanced_filter.should_trade_enhanced(test_date)
        
        print(f"\n📅 {test_date.date()}:")
        print(f"   VIX: {regime['vix']:.1f} ({regime['enhanced_vix_regime']})")
        print(f"   Term Structure: {regime['term_structure_regime']}")
        print(f"   Position Multiplier: {regime['enhanced_position_multiplier']:.2f}x")
        print(f"   Quality Score: {regime['regime_quality_score']:.2f}")
        print(f"   Should Trade: {should_trade}")
        print(f"   Reason: {reason}")
    
    print("\n✅ Enhanced regime filter test completed")
