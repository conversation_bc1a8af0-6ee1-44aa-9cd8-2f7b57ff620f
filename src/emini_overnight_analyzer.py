"""
E-mini Overnight Analyzer for JPM Collar Strategy
Analyzes overnight E-mini S&P 500 futures price action to inform next-day collar decisions
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class EminiOvernightAnalyzer:
    """Analyzes E-mini overnight price action for collar strategy enhancement"""
    
    def __init__(self, emini_data_path: str):
        """Initialize with E-mini 5-minute data path"""
        self.emini_data_path = emini_data_path
        self.emini_data = None
        self.overnight_sessions = []
        self.load_emini_data()
        
    def load_emini_data(self):
        """Load E-mini 5-minute continuous futures data"""
        try:
            # Load E-mini data with proper datetime parsing
            self.emini_data = pd.read_csv(
                self.emini_data_path,
                names=['datetime', 'open', 'high', 'low', 'close', 'volume'],
                parse_dates=['datetime'],
                index_col='datetime'
            )
            print(f"✅ Loaded {len(self.emini_data)} E-mini 5-minute bars")
            print(f"   Date range: {self.emini_data.index.min()} to {self.emini_data.index.max()}")
            
        except Exception as e:
            print(f"❌ Error loading E-mini data: {e}")
            self.emini_data = pd.DataFrame()
    
    def identify_overnight_sessions(self, start_date: str = None, end_date: str = None) -> List[Dict]:
        """Identify overnight trading sessions (4:00 PM ET to 9:30 AM ET next day)"""
        if self.emini_data.empty:
            return []
        
        # Filter date range if specified
        data = self.emini_data.copy()
        if start_date:
            data = data[data.index >= start_date]
        if end_date:
            data = data[data.index <= end_date]

        print(f"Processing {len(data)} E-mini bars for overnight sessions...")
        
        overnight_sessions = []
        
        # Group by date and identify overnight periods (process unique dates only)
        unique_dates = sorted(set(data.index.date))
        print(f"Processing {len(unique_dates)} unique dates for overnight sessions...")

        for i, date in enumerate(unique_dates):
            if i % 100 == 0:  # Progress indicator
                print(f"  Processed {i}/{len(unique_dates)} dates...")
            date_str = date.strftime('%Y-%m-%d')
            
            # Define overnight session: 4:00 PM to 9:30 AM next day
            session_start = pd.Timestamp(f"{date_str} 16:00:00")
            next_date = date + timedelta(days=1)
            session_end = pd.Timestamp(f"{next_date.strftime('%Y-%m-%d')} 09:30:00")
            
            # Get overnight data
            overnight_data = data[(data.index >= session_start) & (data.index < session_end)]
            
            if len(overnight_data) > 10:  # Minimum bars for analysis
                session_info = self.analyze_overnight_session(overnight_data, date_str)
                if session_info:
                    overnight_sessions.append(session_info)
        
        self.overnight_sessions = overnight_sessions
        return overnight_sessions
    
    def analyze_overnight_session(self, overnight_data: pd.DataFrame, date: str) -> Dict:
        """Analyze a single overnight session for key metrics"""
        if overnight_data.empty:
            return None
        
        # Key price levels
        session_open = overnight_data['open'].iloc[0]
        session_close = overnight_data['close'].iloc[-1]
        session_high = overnight_data['high'].max()
        session_low = overnight_data['low'].min()
        
        # Calculate overnight metrics
        overnight_return = (session_close - session_open) / session_open
        overnight_range = session_high - session_low
        overnight_volatility = overnight_data['close'].pct_change().std() * np.sqrt(288)  # Annualized
        
        # Volume analysis
        total_volume = overnight_data['volume'].sum()
        avg_volume = overnight_data['volume'].mean()
        volume_weighted_price = (overnight_data['close'] * overnight_data['volume']).sum() / total_volume
        
        # Momentum analysis
        early_overnight = overnight_data.iloc[:len(overnight_data)//3]  # First third
        late_overnight = overnight_data.iloc[-len(overnight_data)//3:]  # Last third
        
        early_momentum = (early_overnight['close'].iloc[-1] - early_overnight['open'].iloc[0]) / early_overnight['open'].iloc[0]
        late_momentum = (late_overnight['close'].iloc[-1] - late_overnight['open'].iloc[0]) / late_overnight['open'].iloc[0]
        
        # Gap analysis (compared to previous regular session close)
        # This would require SPX regular session data - for now use overnight open as proxy
        gap_size = overnight_return
        
        return {
            'date': date,
            'session_open': session_open,
            'session_close': session_close,
            'session_high': session_high,
            'session_low': session_low,
            'overnight_return': overnight_return,
            'overnight_range': overnight_range,
            'overnight_volatility': overnight_volatility,
            'total_volume': total_volume,
            'avg_volume': avg_volume,
            'volume_weighted_price': volume_weighted_price,
            'early_momentum': early_momentum,
            'late_momentum': late_momentum,
            'gap_size': gap_size,
            'bars_count': len(overnight_data)
        }
    
    def generate_trade_filter(self, target_date: str) -> Dict:
        """Generate overnight-based trade filter to determine if AM trade should be taken"""
        
        # Get overnight session for the previous night
        target_dt = pd.to_datetime(target_date)
        prev_date = (target_dt - timedelta(days=1)).strftime('%Y-%m-%d')
        
        # Find the overnight session
        overnight_session = None
        for session in self.overnight_sessions:
            if session['date'] == prev_date:
                overnight_session = session
                break
        
        if not overnight_session:
            return {
                'take_trade': True,  # Default to taking trade if no overnight data
                'filter_strength': 0.0,
                'reasoning': 'No overnight data available - default to trade'
            }

        # Analyze overnight patterns for trade filtering
        filter_score = 0.0
        reasoning_factors = []
        take_trade = True  # Default to taking the trade
        
        # Factor 1: Extreme overnight moves (filter out trades)
        abs_return = abs(overnight_session['overnight_return'])
        if abs_return > 0.025:  # >2.5% overnight move - too extreme, skip trade
            take_trade = False
            filter_score -= 1.0
            reasoning_factors.append(f"Extreme overnight move ({overnight_session['overnight_return']:.2%}) - skipping trade")
        elif abs_return > 0.015:  # >1.5% overnight move - reduce confidence
            filter_score -= 0.3
            reasoning_factors.append(f"Large overnight move: {overnight_session['overnight_return']:.2%}")
        elif abs_return < 0.003:  # <0.3% overnight move - good for collar
            filter_score += 0.2
            reasoning_factors.append(f"Calm overnight session: {overnight_session['overnight_return']:.2%}")

        # Factor 2: Overnight volatility (high vol = skip trade)
        if overnight_session['overnight_volatility'] > 0.35:  # Very high volatility
            take_trade = False
            filter_score -= 1.0
            reasoning_factors.append(f"Extreme overnight volatility ({overnight_session['overnight_volatility']:.1%}) - skipping trade")
        elif overnight_session['overnight_volatility'] > 0.25:  # High volatility
            filter_score -= 0.2
            reasoning_factors.append(f"High overnight volatility: {overnight_session['overnight_volatility']:.1%}")

        # Factor 3: Volume analysis (very high volume = potential continuation, skip)
        if overnight_session['total_volume'] > 100000:  # Very high overnight volume
            filter_score -= 0.3
            reasoning_factors.append(f"Very high overnight volume: {overnight_session['total_volume']:,}")
        elif overnight_session['total_volume'] > 50000:  # High overnight volume
            filter_score -= 0.1
            reasoning_factors.append(f"High overnight volume: {overnight_session['total_volume']:,}")

        # Factor 4: Momentum consistency (inconsistent = skip)
        momentum_consistency = abs(overnight_session['early_momentum'] - overnight_session['late_momentum'])
        if momentum_consistency > 0.015:  # Very inconsistent momentum
            take_trade = False
            filter_score -= 0.5
            reasoning_factors.append("Highly inconsistent overnight momentum - skipping trade")
        elif momentum_consistency < 0.005:  # Consistent momentum
            filter_score += 0.1
            reasoning_factors.append("Consistent overnight momentum")

        # Factor 5: Gap analysis (large gaps suggest continuation, not good for collar)
        if abs(overnight_session['gap_size']) > 0.02:  # >2% gap
            take_trade = False
            filter_score -= 0.8
            reasoning_factors.append(f"Large gap ({overnight_session['gap_size']:.2%}) suggests continuation - skipping trade")
        elif abs(overnight_session['gap_size']) > 0.01:  # >1% gap
            filter_score -= 0.2
            reasoning_factors.append(f"Moderate gap: {overnight_session['gap_size']:.2%}")

        # Final decision: if filter_score is very negative, skip trade
        if filter_score < -0.5:
            take_trade = False

        return {
            'take_trade': take_trade,
            'filter_strength': filter_score,
            'reasoning': '; '.join(reasoning_factors) if reasoning_factors else 'Normal overnight activity',
            'overnight_return': overnight_session['overnight_return'],
            'overnight_volatility': overnight_session['overnight_volatility'],
            'overnight_volume': overnight_session['total_volume'],
            'session_data': overnight_session
        }
    
    def get_overnight_drift_for_date(self, date_str: str) -> float:
        """Get overnight drift (return) for a specific date"""
        if not self.overnight_sessions:
            return 0.0

        # Find the overnight session for the given date
        for session in self.overnight_sessions:
            if session['date'] == date_str:
                return session['overnight_return']

        # If no exact match, try to find the closest previous date
        target_date = pd.to_datetime(date_str).date()
        closest_session = None
        min_days_diff = float('inf')

        for session in self.overnight_sessions:
            session_date = pd.to_datetime(session['date']).date()
            days_diff = (target_date - session_date).days

            # Only consider sessions from the past (not future)
            if 0 <= days_diff < min_days_diff:
                min_days_diff = days_diff
                closest_session = session

        if closest_session and min_days_diff <= 3:  # Within 3 days
            return closest_session['overnight_return']

        return 0.0  # No suitable session found

    def get_overnight_statistics(self, days: int = 30) -> Dict:
        """Get overnight trading statistics for the last N days"""
        if not self.overnight_sessions:
            return {}

        recent_sessions = self.overnight_sessions[-days:] if len(self.overnight_sessions) > days else self.overnight_sessions

        if not recent_sessions:
            return {}

        returns = [s['overnight_return'] for s in recent_sessions]
        volatilities = [s['overnight_volatility'] for s in recent_sessions]
        volumes = [s['total_volume'] for s in recent_sessions]

        return {
            'avg_overnight_return': np.mean(returns),
            'overnight_return_std': np.std(returns),
            'avg_overnight_volatility': np.mean(volatilities),
            'avg_overnight_volume': np.mean(volumes),
            'positive_nights': sum(1 for r in returns if r > 0),
            'negative_nights': sum(1 for r in returns if r < 0),
            'large_moves': sum(1 for r in returns if abs(r) > 0.01),
            'sessions_analyzed': len(recent_sessions)
        }
