"""
Sophisticated Strike Selection Engine
Implements cost-optimized strike selection for three distinct trading scenarios:
1. Directional Options Trading
2. Delta-Neutral Butterfly Spreads  
3. Neutral Market Strategies
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from scipy.optimize import minimize_scalar
from scipy.stats import norm

class SophisticatedStrikeSelector:
    """
    Advanced strike selection engine for optimized options trading
    """
    
    def __init__(self):
        """Initialize sophisticated strike selector"""
        self.logger = logging.getLogger(__name__)
        
        # Strike selection parameters
        self.DIRECTIONAL_OTM_RANGE = (0.01, 0.03)  # 1-3% OTM for directional trades
        self.BUTTERFLY_WING_SPACING = [25, 50]     # Wing spacing options for butterflies
        self.DELTA_TARGETS = {
            'aggressive': 60,    # 60-delta butterflies
            'moderate': 40,      # 40-delta butterflies  
            'conservative': 20   # 20-delta butterflies
        }
        self.NEUTRAL_ATM_TOLERANCE = 0.005  # 0.5% tolerance for ATM strikes
        
        # Cost optimization parameters
        self.MAX_PREMIUM_RATIO = 0.15      # Max premium as % of underlying price
        self.MIN_PROFIT_POTENTIAL = 2.0    # Minimum profit/loss ratio
        self.LIQUIDITY_MIN_VOLUME = 10     # Minimum option volume for liquidity
        
        print("✅ Sophisticated Strike Selection Engine initialized")
        print(f"   🎯 Directional OTM Range: {self.DIRECTIONAL_OTM_RANGE[0]*100:.1f}%-{self.DIRECTIONAL_OTM_RANGE[1]*100:.1f}%")
        print(f"   🦋 Butterfly Wing Spacing: {self.BUTTERFLY_WING_SPACING}")
        print(f"   ⚖️ Delta Targets: {list(self.DELTA_TARGETS.values())}")
    
    def select_directional_strike(self, options_data: pd.DataFrame, signal_type: str, 
                                current_price: float, target_expiry: datetime,
                                cost_optimization: bool = True) -> Optional[Dict]:
        """
        Select cost-optimized strike for directional trading
        
        Args:
            options_data: Available options data
            signal_type: 'BULLISH' or 'BEARISH'
            current_price: Current underlying price
            target_expiry: Target expiration date
            cost_optimization: Whether to optimize for cost efficiency
            
        Returns:
            Dictionary with optimal strike details
        """
        
        # Filter options for target expiry and type
        option_type = 'c' if signal_type == 'BULLISH' else 'p'
        
        filtered_options = options_data[
            (options_data['expiration'] == target_expiry) &
            (options_data['option_type'] == option_type) &
            (options_data['volume'] >= self.LIQUIDITY_MIN_VOLUME)
        ].copy()
        
        if filtered_options.empty:
            return None
        
        # Calculate OTM range for directional trades
        if signal_type == 'BULLISH':
            # For calls, look for strikes above current price (OTM calls)
            otm_min = current_price * (1 + self.DIRECTIONAL_OTM_RANGE[0])
            otm_max = current_price * (1 + self.DIRECTIONAL_OTM_RANGE[1])
            candidate_options = filtered_options[
                (filtered_options['strike'] >= otm_min) &
                (filtered_options['strike'] <= otm_max)
            ]
        else:  # BEARISH
            # For puts, look for strikes below current price (OTM puts)
            otm_min = current_price * (1 - self.DIRECTIONAL_OTM_RANGE[1])
            otm_max = current_price * (1 - self.DIRECTIONAL_OTM_RANGE[0])
            candidate_options = filtered_options[
                (filtered_options['strike'] >= otm_min) &
                (filtered_options['strike'] <= otm_max)
            ]
        
        if candidate_options.empty:
            # Fallback to closest available strikes
            if signal_type == 'BULLISH':
                candidate_options = filtered_options[filtered_options['strike'] > current_price].head(5)
            else:
                candidate_options = filtered_options[filtered_options['strike'] < current_price].tail(5)
        
        if candidate_options.empty:
            return None
        
        # Cost optimization scoring
        if cost_optimization:
            optimal_strike = self._optimize_directional_cost(candidate_options, current_price, signal_type)
        else:
            # Simple selection: closest to target OTM percentage
            target_otm = (self.DIRECTIONAL_OTM_RANGE[0] + self.DIRECTIONAL_OTM_RANGE[1]) / 2
            if signal_type == 'BULLISH':
                target_strike = current_price * (1 + target_otm)
            else:
                target_strike = current_price * (1 - target_otm)
            
            candidate_options['strike_distance'] = abs(candidate_options['strike'] - target_strike)
            optimal_strike = candidate_options.loc[candidate_options['strike_distance'].idxmin()]
        
        return self._format_strike_result(optimal_strike, 'directional', signal_type)
    
    def select_butterfly_strike(self, options_data: pd.DataFrame, delta_target: str,
                              current_price: float, target_expiry: datetime,
                              wing_spacing: int = 25) -> Optional[Dict]:
        """
        Select optimal butterfly spread strikes based on delta targeting
        
        Args:
            options_data: Available options data
            delta_target: 'aggressive', 'moderate', or 'conservative'
            current_price: Current underlying price
            target_expiry: Target expiration date
            wing_spacing: Wing spacing in points
            
        Returns:
            Dictionary with butterfly spread details
        """
        
        target_delta = self.DELTA_TARGETS[delta_target]
        
        # Filter options for target expiry
        call_options = options_data[
            (options_data['expiration'] == target_expiry) &
            (options_data['option_type'] == 'c') &
            (options_data['volume'] >= self.LIQUIDITY_MIN_VOLUME)
        ].copy()
        
        if call_options.empty:
            return None
        
        # Find center strike based on delta target
        call_options['delta_distance'] = abs(call_options['delta'] * 100 - target_delta)
        center_option = call_options.loc[call_options['delta_distance'].idxmin()]
        center_strike = center_option['strike']
        
        # Define wing strikes
        lower_wing = center_strike - wing_spacing
        upper_wing = center_strike + wing_spacing
        
        # Find wing options
        lower_wing_option = call_options[call_options['strike'] == lower_wing]
        upper_wing_option = call_options[call_options['strike'] == upper_wing]
        
        if lower_wing_option.empty or upper_wing_option.empty:
            # Try alternative wing spacing
            for alt_spacing in self.BUTTERFLY_WING_SPACING:
                if alt_spacing != wing_spacing:
                    lower_wing = center_strike - alt_spacing
                    upper_wing = center_strike + alt_spacing
                    
                    lower_wing_option = call_options[call_options['strike'] == lower_wing]
                    upper_wing_option = call_options[call_options['strike'] == upper_wing]
                    
                    if not lower_wing_option.empty and not upper_wing_option.empty:
                        wing_spacing = alt_spacing
                        break
            else:
                return None
        
        # Calculate butterfly cost and profit potential
        butterfly_cost = self._calculate_butterfly_cost(
            lower_wing_option.iloc[0], center_option, upper_wing_option.iloc[0]
        )
        
        max_profit = wing_spacing - butterfly_cost
        profit_ratio = max_profit / butterfly_cost if butterfly_cost > 0 else 0
        
        return {
            'strategy_type': 'butterfly',
            'delta_target': delta_target,
            'center_strike': center_strike,
            'lower_wing': lower_wing,
            'upper_wing': upper_wing,
            'wing_spacing': wing_spacing,
            'butterfly_cost': butterfly_cost,
            'max_profit': max_profit,
            'profit_ratio': profit_ratio,
            'center_delta': center_option['delta'],
            'expiry': target_expiry,
            'cost_efficiency_score': self._calculate_butterfly_efficiency(
                butterfly_cost, max_profit, center_option['delta']
            )
        }
    
    def select_neutral_strategy(self, options_data: pd.DataFrame, vix_level: float,
                              current_price: float, target_expiry: datetime,
                              market_regime: str = 'normal') -> Optional[Dict]:
        """
        Select optimal neutral strategy based on market conditions
        
        Args:
            options_data: Available options data
            vix_level: Current VIX level
            current_price: Current underlying price
            target_expiry: Target expiration date
            market_regime: Market volatility regime
            
        Returns:
            Dictionary with neutral strategy details
        """
        
        # Determine optimal neutral strategy based on VIX and regime
        if vix_level < 15:
            # Low volatility: prefer strategies that benefit from volatility expansion
            strategy_type = 'long_straddle'
        elif vix_level > 30:
            # High volatility: prefer strategies that benefit from volatility contraction
            strategy_type = 'iron_condor'
        else:
            # Normal volatility: prefer ATM butterflies
            strategy_type = 'atm_butterfly'
        
        if strategy_type == 'long_straddle':
            return self._select_straddle_strikes(options_data, current_price, target_expiry)
        elif strategy_type == 'iron_condor':
            return self._select_iron_condor_strikes(options_data, current_price, target_expiry)
        else:  # atm_butterfly
            return self._select_atm_butterfly_strikes(options_data, current_price, target_expiry)
    
    def _optimize_directional_cost(self, candidate_options: pd.DataFrame, 
                                 current_price: float, signal_type: str) -> pd.Series:
        """Optimize directional strike selection for cost efficiency"""
        
        candidate_options = candidate_options.copy()
        
        # Calculate mid price
        candidate_options['mid_price'] = (candidate_options['bid'] + candidate_options['ask']) / 2
        
        # Calculate cost efficiency metrics
        candidate_options['premium_ratio'] = candidate_options['mid_price'] / current_price
        
        # Calculate potential profit (simplified)
        if signal_type == 'BULLISH':
            # For calls: profit potential based on how far OTM
            candidate_options['moneyness'] = candidate_options['strike'] / current_price
            candidate_options['profit_potential'] = (1.05 - candidate_options['moneyness']) * current_price
        else:
            # For puts: profit potential based on how far OTM  
            candidate_options['moneyness'] = current_price / candidate_options['strike']
            candidate_options['profit_potential'] = (candidate_options['moneyness'] - 0.95) * current_price
        
        # Cost efficiency score: profit potential / premium paid
        candidate_options['cost_efficiency'] = (
            candidate_options['profit_potential'] / candidate_options['mid_price']
        ).fillna(0)
        
        # Liquidity score
        candidate_options['liquidity_score'] = np.log1p(candidate_options['volume'])
        
        # Combined score
        candidate_options['total_score'] = (
            candidate_options['cost_efficiency'] * 0.6 +
            candidate_options['liquidity_score'] * 0.4
        )
        
        return candidate_options.loc[candidate_options['total_score'].idxmax()]
    
    def _calculate_butterfly_cost(self, lower_wing: pd.Series, center: pd.Series, 
                                upper_wing: pd.Series) -> float:
        """Calculate net cost of butterfly spread"""
        
        # Long butterfly: Buy lower wing, Sell 2x center, Buy upper wing
        lower_cost = (lower_wing['ask'] + lower_wing['bid']) / 2
        center_credit = (center['bid'] + center['ask']) / 2
        upper_cost = (upper_wing['ask'] + upper_wing['bid']) / 2
        
        net_cost = lower_cost - (2 * center_credit) + upper_cost
        return max(0, net_cost)  # Ensure non-negative cost
    
    def _calculate_butterfly_efficiency(self, cost: float, max_profit: float, 
                                      center_delta: float) -> float:
        """Calculate butterfly cost efficiency score"""
        
        if cost <= 0:
            return 0
        
        # Base efficiency: profit potential / cost
        base_efficiency = max_profit / cost
        
        # Delta adjustment: prefer moderate deltas for butterflies
        delta_penalty = abs(center_delta - 0.5) * 2  # Penalty for extreme deltas
        delta_adjusted_efficiency = base_efficiency * (1 - delta_penalty)
        
        return max(0, delta_adjusted_efficiency)
    
    def _select_straddle_strikes(self, options_data: pd.DataFrame, 
                               current_price: float, target_expiry: datetime) -> Optional[Dict]:
        """Select ATM straddle strikes"""
        
        # Find ATM strike
        available_strikes = options_data[
            options_data['expiration'] == target_expiry
        ]['strike'].unique()
        
        atm_strike = min(available_strikes, key=lambda x: abs(x - current_price))
        
        # Get call and put options
        call_option = options_data[
            (options_data['expiration'] == target_expiry) &
            (options_data['option_type'] == 'c') &
            (options_data['strike'] == atm_strike)
        ]
        
        put_option = options_data[
            (options_data['expiration'] == target_expiry) &
            (options_data['option_type'] == 'p') &
            (options_data['strike'] == atm_strike)
        ]
        
        if call_option.empty or put_option.empty:
            return None
        
        call_price = (call_option.iloc[0]['bid'] + call_option.iloc[0]['ask']) / 2
        put_price = (put_option.iloc[0]['bid'] + put_option.iloc[0]['ask']) / 2
        straddle_cost = call_price + put_price
        
        return {
            'strategy_type': 'long_straddle',
            'strike': atm_strike,
            'call_price': call_price,
            'put_price': put_price,
            'total_cost': straddle_cost,
            'breakeven_up': atm_strike + straddle_cost,
            'breakeven_down': atm_strike - straddle_cost,
            'expiry': target_expiry
        }
    
    def _select_iron_condor_strikes(self, options_data: pd.DataFrame,
                                  current_price: float, target_expiry: datetime) -> Optional[Dict]:
        """Select iron condor strikes for high volatility environments"""
        
        # Iron condor: Sell call spread + Sell put spread
        # Target strikes around 10-15% OTM
        
        otm_distance = 0.12  # 12% OTM
        call_short_strike = current_price * (1 + otm_distance)
        call_long_strike = current_price * (1 + otm_distance + 0.05)
        put_short_strike = current_price * (1 - otm_distance)
        put_long_strike = current_price * (1 - otm_distance - 0.05)
        
        # Find closest available strikes
        available_strikes = sorted(options_data[
            options_data['expiration'] == target_expiry
        ]['strike'].unique())
        
        call_short = min(available_strikes, key=lambda x: abs(x - call_short_strike))
        call_long = min(available_strikes, key=lambda x: abs(x - call_long_strike))
        put_short = min(available_strikes, key=lambda x: abs(x - put_short_strike))
        put_long = min(available_strikes, key=lambda x: abs(x - put_long_strike))
        
        return {
            'strategy_type': 'iron_condor',
            'call_short_strike': call_short,
            'call_long_strike': call_long,
            'put_short_strike': put_short,
            'put_long_strike': put_long,
            'expiry': target_expiry,
            'estimated_credit': 50  # Placeholder - would calculate from actual prices
        }
    
    def _select_atm_butterfly_strikes(self, options_data: pd.DataFrame,
                                    current_price: float, target_expiry: datetime) -> Optional[Dict]:
        """Select ATM butterfly strikes for neutral markets"""
        
        # Use moderate delta target for ATM butterflies
        return self.select_butterfly_strike(
            options_data, 'moderate', current_price, target_expiry, wing_spacing=25
        )
    
    def _format_strike_result(self, option_data: pd.Series, strategy_type: str, 
                            signal_type: str = None) -> Dict:
        """Format strike selection result"""
        
        mid_price = (option_data['bid'] + option_data['ask']) / 2
        
        return {
            'strategy_type': strategy_type,
            'signal_type': signal_type,
            'strike': option_data['strike'],
            'option_type': option_data['option_type'],
            'expiry': option_data['expiration'],
            'bid': option_data['bid'],
            'ask': option_data['ask'],
            'mid_price': mid_price,
            'volume': option_data['volume'],
            'delta': option_data.get('delta', 0),
            'gamma': option_data.get('gamma', 0),
            'theta': option_data.get('theta', 0),
            'vega': option_data.get('vega', 0),
            'cost_efficiency_score': getattr(option_data, 'cost_efficiency', 0)
        }


if __name__ == "__main__":
    # Test the sophisticated strike selector
    selector = SophisticatedStrikeSelector()
    
    print("\n🧪 SOPHISTICATED STRIKE SELECTION ENGINE TEST")
    print("=" * 60)
    print("✅ Strike selection engine test completed")
