"""
Enhanced Strategy Selector
Integrates the regime-based options playbook with market regime analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.enhanced_market_regime_filter import EnhancedMarketRegimeFilter
from src.regime_based_options_playbook import RegimeBasedOptionsPlaybook, OptionStrategy

class EnhancedStrategySelector:
    """
    Enhanced strategy selector that combines market regime analysis with 
    comprehensive options trading playbook
    """
    
    def __init__(self, securities_path: str = "/Users/<USER>/Downloads/systems/strategy_package/data/securities/"):
        """Initialize enhanced strategy selector"""
        self.securities_path = securities_path
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.regime_filter = EnhancedMarketRegimeFilter(securities_path)
        self.playbook = RegimeBasedOptionsPlaybook()
        
        print("✅ Enhanced Strategy Selector initialized successfully")
    
    def get_trading_recommendation(self, date: datetime, greeks_data: Dict,
                                 wall_data: Dict, drift: float,
                                 current_price: float, recent_regime_trades: Dict = None) -> Dict:
        """
        Get comprehensive trading recommendation for given date and market conditions
        
        Args:
            date: Trading date
            greeks_data: Greeks convergence data (vanna, charm, etc.)
            wall_data: Options wall data (call/put strengths)
            drift: Overnight E-mini drift
            current_price: Current SPX price
            
        Returns:
            Dictionary with trading recommendation
        """
        
        # Get enhanced market regime
        regime_data = self.regime_filter.get_enhanced_market_regime(date)
        
        # Check if trading should occur
        should_trade, trade_reason = self.regime_filter.should_trade_enhanced(date)

        # Check for regime coverage forcing (if primary filters fail)
        if recent_regime_trades is None:
            recent_regime_trades = {}

        force_trade, force_reason = self.should_force_trade_for_regime_coverage(
            regime_data, recent_regime_trades)

        if not should_trade and not force_trade:
            return {
                'should_trade': False,
                'reason': trade_reason,
                'regime_data': regime_data,
                'strategy': None,
                'position_size': 0,
                'confidence': 0.0
            }

        # Update reason if we're forcing trade for coverage
        if force_trade and not should_trade:
            trade_reason = force_reason
        
        # Get base strategy from playbook
        strategy = self.playbook.get_strategy_for_regime(
            vix_regime=regime_data['enhanced_vix_regime'],
            term_structure=regime_data['term_structure_regime'],
            uty_direction=regime_data['uty_direction'],
            quality_score=regime_data['regime_quality_score']
        )
        
        # Enhanced neutral regime handling - always try fallback for better trade frequency
        if strategy is None:
            self.logger.info("No strategy from regime analysis, trying neutral fallback")
            strategy = self.playbook.handle_neutral_regime_fallback(
                greeks_data=greeks_data,
                wall_data=wall_data,
                drift=drift,
                quality_score=regime_data['regime_quality_score']
            )

        # Additional fallback for very low quality scores - ensure minimum trade frequency
        if strategy is None and regime_data['regime_quality_score'] >= 0.1:
            self.logger.info("Creating emergency fallback strategy for very low quality regime")
            # Create a conservative strategy for very low quality conditions
            strategy = self.playbook._create_neutral_fallback_strategy(
                option_bias='call' if drift > 0 else 'put',
                quality_score=regime_data['regime_quality_score'],
                reason="emergency_low_quality"
            )
        
        if strategy is None:
            return {
                'should_trade': False,
                'reason': 'No suitable strategy found for current regime',
                'regime_data': regime_data,
                'strategy': None,
                'position_size': 0,
                'confidence': 0.0
            }
        
        # Enhance strategy with Greeks and wall analysis
        enhanced_strategy = self.playbook.enhance_strategy_with_greeks(
            strategy=strategy,
            greeks_data=greeks_data,
            wall_data=wall_data,
            drift=drift
        )
        
        # Calculate confidence score
        confidence = self._calculate_confidence_score(
            regime_data=regime_data,
            strategy=enhanced_strategy,
            greeks_data=greeks_data,
            wall_data=wall_data,
            drift=drift
        )
        
        return {
            'should_trade': True,
            'reason': trade_reason,
            'regime_data': regime_data,
            'strategy': enhanced_strategy,
            'confidence': confidence,
            'option_type_preference': self._get_option_type_from_strategy(enhanced_strategy),
            'position_multiplier': enhanced_strategy.position_multiplier,
            'max_risk_percent': enhanced_strategy.max_risk_percent,
            'expiration_range': enhanced_strategy.expiration_days,
            'strike_selection': enhanced_strategy.strike_selection
        }
    
    def _calculate_confidence_score(self, regime_data: Dict, strategy: OptionStrategy,
                                  greeks_data: Dict, wall_data: Dict, drift: float) -> float:
        """Calculate confidence score for the trading recommendation"""
        
        base_confidence = regime_data['regime_quality_score']
        
        # Greeks alignment bonus
        vanna = greeks_data.get('vanna_convergence', 0)
        if abs(vanna) > 0.5:
            if (vanna > 0 and 'call' in strategy.option_type) or \
               (vanna < 0 and 'put' in strategy.option_type):
                base_confidence += 0.1  # Greeks support strategy
            else:
                base_confidence -= 0.05  # Greeks conflict with strategy
        
        # Wall alignment bonus
        call_strength = wall_data.get('call_wall_strength', 0)
        put_strength = wall_data.get('put_wall_strength', 0)
        
        if call_strength > 5.0 and 'put' in strategy.option_type:
            base_confidence += 0.05  # Call walls support put strategy
        elif put_strength > 5.0 and 'call' in strategy.option_type:
            base_confidence += 0.05  # Put walls support call strategy
        
        # Drift alignment bonus
        if abs(drift) > 0.003:  # 0.3% threshold
            if (drift > 0 and 'call' in strategy.option_type) or \
               (drift < 0 and 'put' in strategy.option_type):
                base_confidence += 0.05  # Drift supports strategy
        
        # VIX regime bonus
        vix_regime = regime_data['enhanced_vix_regime']
        if vix_regime == 'optimal_vix':
            base_confidence += 0.1
        elif vix_regime == 'normal_vix':
            base_confidence += 0.05
        
        return min(1.0, max(0.0, base_confidence))

    def should_force_trade_for_regime_coverage(self, regime_data: Dict,
                                             recent_regime_trades: Dict) -> Tuple[bool, str]:
        """
        Determine if we should force a trade to ensure regime coverage
        This helps ensure we get at least one trade for each major regime combination
        """

        current_regime_key = (
            regime_data['enhanced_vix_regime'],
            regime_data['term_structure_regime']
        )

        # Define major regime combinations we want to ensure coverage for
        major_regimes = [
            ('optimal_vix', 'contango'),
            ('optimal_vix', 'backwardation'),
            ('optimal_vix', 'neutral'),
            ('normal_vix', 'contango'),
            ('normal_vix', 'backwardation'),
            ('normal_vix', 'neutral'),
            ('high_vix', 'contango'),
            ('high_vix', 'backwardation'),
            ('high_vix', 'neutral')
        ]

        # Check if this is a major regime we haven't traded in recently
        if (current_regime_key in major_regimes and
            current_regime_key not in recent_regime_trades and
            regime_data['regime_quality_score'] >= 0.1):

            return True, f"Forcing trade for regime coverage: {current_regime_key[0]} + {current_regime_key[1]}"

        return False, "No regime coverage forcing needed"
    
    def _get_option_type_from_strategy(self, strategy: OptionStrategy) -> str:
        """Extract option type preference from strategy"""
        
        if 'call' in strategy.option_type:
            return 'calls'
        elif 'put' in strategy.option_type:
            return 'puts'
        elif 'straddle' in strategy.option_type:
            return 'straddle'
        elif 'iron_condor' in strategy.option_type:
            return 'neutral'
        else:
            return 'neutral'
    
    def calculate_position_size(self, strategy: OptionStrategy, base_capital: float,
                              current_price: float, option_price: float) -> int:
        """Calculate position size using playbook methodology"""
        return self.playbook.calculate_position_size(
            strategy=strategy,
            base_capital=base_capital,
            current_price=current_price,
            option_price=option_price
        )
    
    def should_exit_position(self, strategy: OptionStrategy, entry_date: datetime,
                           current_date: datetime, entry_price: float, 
                           current_price: float, current_pnl: float) -> Tuple[bool, str]:
        """Determine if position should be exited"""
        return self.playbook.should_exit_position(
            strategy=strategy,
            entry_date=entry_date,
            current_date=current_date,
            entry_price=entry_price,
            current_price=current_price,
            current_pnl=current_pnl
        )
    
    def get_strategy_summary(self, strategy: OptionStrategy, regime_data: Dict) -> str:
        """Get human-readable strategy summary"""
        
        return (f"{strategy.name} | "
                f"VIX: {regime_data['vix']:.1f} ({regime_data['enhanced_vix_regime']}) | "
                f"TS: {regime_data['term_structure_regime']} | "
                f"UTY: {regime_data['uty_direction']} | "
                f"Quality: {regime_data['regime_quality_score']:.2f} | "
                f"Multiplier: {strategy.position_multiplier:.1f}x | "
                f"Risk: {strategy.max_risk_percent:.1f}% | "
                f"Target: {strategy.profit_target_percent:.0f}%")
    
    def validate_strategy_criteria(self, strategy: OptionStrategy, greeks_data: Dict,
                                 wall_data: Dict, drift: float) -> Tuple[bool, List[str]]:
        """
        Validate that current market conditions meet strategy entry criteria
        
        Returns:
            (criteria_met, failed_criteria_list)
        """
        
        failed_criteria = []
        
        # Check strategy-specific entry criteria
        for criterion in strategy.entry_criteria:
            if not self._check_criterion(criterion, greeks_data, wall_data, drift):
                failed_criteria.append(criterion)
        
        criteria_met = len(failed_criteria) == 0
        
        return criteria_met, failed_criteria
    
    def _check_criterion(self, criterion: str, greeks_data: Dict, 
                        wall_data: Dict, drift: float) -> bool:
        """Check if a specific criterion is met"""
        
        try:
            # Wall strength criteria
            if 'wall_strength>' in criterion:
                threshold = float(criterion.split('>')[1])
                if 'put_wall' in criterion:
                    return wall_data.get('put_wall_strength', 0) > threshold
                elif 'call_wall' in criterion:
                    return wall_data.get('call_wall_strength', 0) > threshold
            
            # Drift criteria
            if criterion == 'positive_drift':
                return drift > 0.001  # 0.1% threshold
            elif criterion == 'negative_drift':
                return drift < -0.001  # -0.1% threshold
            elif criterion == 'minimal_drift':
                return abs(drift) < 0.002  # Within 0.2%
            
            # Greeks criteria
            if 'vanna_convergence>' in criterion:
                threshold = float(criterion.split('>')[1])
                return abs(greeks_data.get('vanna_convergence', 0)) > threshold
            
            # Quality criteria
            if 'quality>' in criterion:
                threshold = float(criterion.split('>')[1])
                return True  # Quality already checked in main logic
            
            # Wall dominance criteria
            if criterion == 'put_wall_dominance':
                return wall_data.get('put_wall_strength', 0) > wall_data.get('call_wall_strength', 0)
            elif criterion == 'call_wall_dominance':
                return wall_data.get('call_wall_strength', 0) > wall_data.get('put_wall_strength', 0)
            elif criterion == 'low_gamma_walls':
                return max(wall_data.get('call_wall_strength', 0), 
                          wall_data.get('put_wall_strength', 0)) < 3.0
            
            # Default: assume criterion is met if not recognized
            return True
            
        except Exception as e:
            self.logger.warning(f"Error checking criterion '{criterion}': {e}")
            return True  # Default to True for unrecognized criteria

if __name__ == "__main__":
    # Test the enhanced strategy selector
    selector = EnhancedStrategySelector()
    
    # Test with sample data
    test_date = datetime(2024, 6, 15)
    test_greeks = {
        'vanna_convergence': 0.6,
        'charm_decay_signal': 0.4,
        'gamma': 0.02
    }
    test_walls = {
        'call_wall_strength': 6.5,
        'put_wall_strength': 4.2
    }
    test_drift = 0.005  # 0.5% positive drift
    test_price = 4200.0
    
    recommendation = selector.get_trading_recommendation(
        date=test_date,
        greeks_data=test_greeks,
        wall_data=test_walls,
        drift=test_drift,
        current_price=test_price
    )
    
    print("🎯 ENHANCED STRATEGY SELECTOR TEST")
    print("=" * 50)
    print(f"Should Trade: {recommendation['should_trade']}")
    print(f"Reason: {recommendation['reason']}")
    
    if recommendation['strategy']:
        strategy = recommendation['strategy']
        regime = recommendation['regime_data']
        print(f"\nStrategy: {strategy.name}")
        print(f"Option Type: {strategy.option_type}")
        print(f"Strike Selection: {strategy.strike_selection}")
        print(f"Expiration: {strategy.expiration_days[0]}-{strategy.expiration_days[1]} days")
        print(f"Position Multiplier: {strategy.position_multiplier:.1f}x")
        print(f"Max Risk: {strategy.max_risk_percent:.1f}%")
        print(f"Confidence: {recommendation['confidence']:.2f}")
        
        summary = selector.get_strategy_summary(strategy, regime)
        print(f"\nSummary: {summary}")
    
    print("\n✅ Enhanced strategy selector test completed")
