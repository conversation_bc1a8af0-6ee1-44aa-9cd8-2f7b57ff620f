"""
Constants and configuration values for the JPM Collar Strategy

This module contains all magic numbers, thresholds, and configuration constants
used throughout the trading system to improve maintainability and consistency.
"""

# =============================================================================
# TRADING CONSTANTS
# =============================================================================

# Data and Backtest Configuration
START_YEAR = 2023                      # Default start year for backtesting
BACKTEST_END_YEAR = 2025              # End year for backtesting

# Option Contract Specifications
SPX_MULTIPLIER = 100                  # SPX option point value in dollars
STRIKE_MULTIPLE = 25                  # Options strikes must be multiples of this value
MIN_CONTRACTS = 1                     # Minimum number of contracts per trade (reduced from 5)
MAX_CONTRACTS = 2                     # Maximum number of contracts per trade (reduced from 10)
DEFAULT_CONTRACTS = 1                 # Default number of contracts (reduced from 7)

# Position Sizing - TIGHTER RISK MANAGEMENT
BASE_POSITION_SIZE_PERCENT = 0.03  # 3% of capital per position (reduced from 5%)
MIN_POSITION_SIZE_PERCENT = 0.01   # 1% minimum position size (reduced from 2%)
MAX_POSITION_SIZE_PERCENT = 0.05   # 5% maximum position size (reduced from 15%)
MAX_RISK_PER_TRADE = 0.03          # Maximum 3% of capital per trade (reduced from 10%)

# Signal Strength Thresholds - LOWERED FOR MORE FREQUENT SIGNALS
WEAK_SIGNAL_THRESHOLD = 0.15       # Below this is considered weak (was 0.3)
MODERATE_SIGNAL_THRESHOLD = 0.4    # Above this is considered strong (was 0.6)
STRONG_SIGNAL_THRESHOLD = 0.65     # Above this is considered very strong (was 0.8)

# =============================================================================
# RISK MANAGEMENT CONSTANTS
# =============================================================================

# Profit and Loss Targets
DEFAULT_PROFIT_TARGET = 0.25       # 25% profit target
DEFAULT_LOSS_LIMIT = -0.35         # -35% loss limit
MAX_PROFIT_TARGET = 0.50           # Maximum profit target
MAX_LOSS_LIMIT = -0.50             # Maximum loss limit

# Days to Expiration - CLOSER EXPIRY FOR TIGHTER SPREADS
MIN_DTE_ENTRY = 7                  # Minimum days to expiration for entry (reduced from 60)
DEFAULT_DTE_THRESHOLD = 5          # Default DTE threshold for exit (reduced from 10)
MAX_DTE_ENTRY = 21                 # Maximum days to expiration for entry (reduced from 90)
TARGET_EXPIRY_DAYS = 14            # Target days to expiration for cluster strategy (reduced from 60)
MAX_HOLD_DAYS = 1                  # Maximum 1 day to hold positions (reduced from 2)

# Position Limits
MAX_OPEN_POSITIONS = 1             # Maximum number of open positions
DEFAULT_MAX_POSITIONS = 3          # Default maximum positions

# =============================================================================
# MARKET DATA CONSTANTS
# =============================================================================

# Greeks Thresholds
DEFAULT_DELTA_THRESHOLD = 0.15     # Default delta threshold
DEFAULT_GAMMA_THRESHOLD = 0.005    # Default gamma threshold
DEFAULT_VEGA_THRESHOLD = 0.10      # Default vega threshold
DEFAULT_THETA_THRESHOLD = -0.05    # Default theta threshold
DEFAULT_VANNA_THRESHOLD = 0.001    # Default vanna threshold
DEFAULT_CHARM_THRESHOLD = 0.001    # Default charm threshold
DEFAULT_VOMMA_THRESHOLD = 0.001    # Default vomma threshold
DEFAULT_ZOMMA_THRESHOLD = 0.001    # Default zomma threshold

# Volatility Thresholds
LOW_VOLATILITY_THRESHOLD = 12      # VIX below this is low volatility
HIGH_VOLATILITY_THRESHOLD = 28     # VIX above this is high volatility
EXTREME_VOLATILITY_THRESHOLD = 40  # VIX above this is extreme volatility

# =============================================================================
# CLUSTER STRATEGY CONSTANTS
# =============================================================================

# Signal Thresholds - TIGHTER SIGNAL REQUIREMENTS
DRIFT_THRESHOLD = 0.002               # 0.2% drift threshold for strong moves (tighter from 0.4%)
WALL_STRENGTH_THRESHOLD = 5.0         # Minimum wall strength required (increased from 2.0)
VOLUME_PERCENTILE = 80                # Volume percentile for significant options (increased from 70)

# Greeks Parameters
GAMMA_MULTIPLIER = 100                # Multiplier for gamma weighting
DELTA_MULTIPLIER = 10                 # Multiplier for delta weighting

# Clustering Parameters
BAND_WIDTH = 0.08                     # ±8% band width from current price for clustering (tighter from 10%)
INITIAL_CAPITAL = 100000.0            # Starting capital for backtesting

# =============================================================================
# SPREAD TRADING CONSTANTS (NEW)
# =============================================================================

# Spread Strategy Configuration
USE_SPREADS = True                    # Enable spread trading instead of outright options
SPREAD_WIDTH = 25                     # $25 spread width (1 strike difference)
MAX_SPREAD_RISK = 2000                # Maximum $20 risk per spread (20 * 100)
MIN_SPREAD_CREDIT = 300               # Minimum $3 credit to enter spread (3 * 100)
SPREAD_PROFIT_TARGET = 0.50           # Take profit at 50% of max profit
SPREAD_LOSS_LIMIT = 0.80              # Stop loss at 80% of max loss

# =============================================================================
# STRATEGY CONFIGURATION CONSTANTS
# =============================================================================

# Collar Width Percentages
TIGHT_COLLAR_CALL_OTM = 0.02       # 2% OTM for tight collar calls
TIGHT_COLLAR_PUT_LONG_OTM = 0.02   # 2% OTM for tight collar long puts
TIGHT_COLLAR_PUT_SHORT_OTM = 0.05  # 5% OTM for tight collar short puts

WIDE_COLLAR_CALL_OTM = 0.15        # 15% OTM for wide collar calls
WIDE_COLLAR_PUT_LONG_OTM = 0.20    # 20% OTM for wide collar long puts
WIDE_COLLAR_PUT_SHORT_OTM = 0.30   # 30% OTM for wide collar short puts

ASYMMETRIC_COLLAR_CALL_OTM = 0.08  # 8% OTM for asymmetric collar calls
ASYMMETRIC_COLLAR_PUT_LONG_OTM = 0.15  # 15% OTM for asymmetric collar long puts
ASYMMETRIC_COLLAR_PUT_SHORT_OTM = 0.25 # 25% OTM for asymmetric collar short puts

VOLATILITY_COLLAR_CALL_OTM = 0.12  # 12% OTM for volatility collar calls
VOLATILITY_COLLAR_PUT_LONG_OTM = 0.12  # 12% OTM for volatility collar long puts
VOLATILITY_COLLAR_PUT_SHORT_OTM = 0.24 # 24% OTM for volatility collar short puts

# Strategy Selection Multipliers
INTELLIGENT_POSITION_MULTIPLIER = 1.2  # Multiplier for intelligent selection
RADICAL_POSITION_MULTIPLIER = 1.2      # Multiplier for radical selection
DYNAMIC_POSITION_MULTIPLIER = 1.1      # Multiplier for dynamic selection

# =============================================================================
# EXECUTION CONSTANTS
# =============================================================================

# Slippage and Commissions
DEFAULT_SLIPPAGE_BPS = 5           # 5 basis points slippage
DEFAULT_COMMISSION_PER_CONTRACT = 1.50  # $1.50 per contract commission
LEGS_PER_COLLAR = 3                # Number of legs in a collar trade

# Rebalancing
DEFAULT_REBALANCE_FREQUENCY = 14   # Rebalance every 14 days
MIN_REBALANCE_FREQUENCY = 7        # Minimum rebalance frequency
MAX_REBALANCE_FREQUENCY = 30       # Maximum rebalance frequency

# =============================================================================
# DATA PROCESSING CONSTANTS
# =============================================================================

# Date Formats
DATE_FORMAT = '%Y-%m-%d'           # Standard date format
DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S'  # Standard datetime format

# File Extensions
EXCEL_EXTENSION = '.xlsx'          # Excel file extension
CSV_EXTENSION = '.csv'             # CSV file extension
PDF_EXTENSION = '.pdf'             # PDF file extension

# Data Validation
MIN_OPTION_PRICE = 0.01            # Minimum valid option price
MAX_OPTION_PRICE = 1000.0          # Maximum valid option price
MIN_UNDERLYING_PRICE = 1000.0      # Minimum valid underlying price
MAX_UNDERLYING_PRICE = 10000.0     # Maximum valid underlying price

# =============================================================================
# REPORTING CONSTANTS
# =============================================================================

# Performance Metrics
TRADING_DAYS_PER_YEAR = 252        # Trading days per year for annualization
RISK_FREE_RATE = 0.02              # Risk-free rate for Sharpe ratio calculation

# Report Configuration
MAX_TRADES_PER_PAGE = 25           # Maximum trades to show per page in reports
CHART_WIDTH = 12                   # Chart width in inches
CHART_HEIGHT = 8                   # Chart height in inches
CHART_DPI = 300                    # Chart resolution

# Color Schemes
PROFIT_COLOR = '#2E8B57'           # Green for profits
LOSS_COLOR = '#DC143C'             # Red for losses
NEUTRAL_COLOR = '#708090'          # Gray for neutral
BACKGROUND_COLOR = '#F5F5F5'       # Light gray background

# =============================================================================
# SIGNAL TIMING CONSTANTS
# =============================================================================

# Signal Processing
SIGNAL_LOOKBACK_DAYS = 5           # Days to look back for signal confirmation
SIGNAL_DELAY_DAYS = 1              # Days to delay entry after signal
CLOSE_SIGNAL_DELAY_DAYS = 1        # Days to delay close after close signal

# Zero-Day Expiration Handling
ZERO_DTE_CLOSE_TIME = '15:45'      # Time to close zero-DTE positions
EXPIRATION_CLOSE_TIME = '15:50'    # Time to close expiring positions

# =============================================================================
# VALIDATION CONSTANTS
# =============================================================================

# Data Quality Checks
MIN_DAILY_VOLUME = 100             # Minimum daily volume for valid data
MIN_OPEN_INTEREST = 50             # Minimum open interest for valid options
MAX_BID_ASK_SPREAD_PERCENT = 0.20  # Maximum bid-ask spread as % of mid

# Signal Quality Checks - LOWERED FOR MORE FREQUENT SIGNALS
MIN_SIGNAL_STRENGTH = 0.05         # Minimum signal strength to consider (was 0.1)
MAX_SIGNAL_STRENGTH = 1.0          # Maximum signal strength
MIN_CONFIDENCE_LEVEL = 0.05        # Minimum confidence level (was 0.1)
MAX_CONFIDENCE_LEVEL = 1.0         # Maximum confidence level
