"""
VIX Term Structure Analyzer for JPM Collar Strategy
Analyzes VIX contango/backwardation patterns using VIX momentum and volatility proxies
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime, timedelta

class VIXTermStructureAnalyzer:
    """
    Analyze VIX term structure patterns using momentum and volatility proxies
    Since VIX futures data may not be available, we use:
    1. VIX momentum (short-term vs medium-term)
    2. VIX volatility clustering
    3. VIX mean reversion patterns
    """
    
    def __init__(self, securities_path: str = "/Users/<USER>/Downloads/systems/strategy_package/data/securities/"):
        """Initialize VIX term structure analyzer"""
        self.securities_path = securities_path
        self.vix_data = None
        self.term_structure_data = None
        self.logger = logging.getLogger(__name__)
        
        # Term structure parameters
        self.SHORT_TERM_DAYS = 5    # Short-term momentum
        self.MEDIUM_TERM_DAYS = 20  # Medium-term momentum
        self.LONG_TERM_DAYS = 60    # Long-term trend
        
        # Contango/backwardation thresholds
        self.CONTANGO_THRESHOLD = 0.05    # 5% momentum difference
        self.BACKWARDATION_THRESHOLD = -0.05  # -5% momentum difference
        
    def load_vix_data(self) -> bool:
        """Load VIX data and calculate term structure proxies"""
        try:
            vix_path = self.securities_path + "VIX_full_5min.txt"
            self.logger.info(f"Loading VIX data from: {vix_path}")
            
            # Load 5-minute VIX data
            vix_5min = pd.read_csv(
                vix_path,
                names=['datetime', 'open', 'high', 'low', 'close', 'volume'],
                parse_dates=['datetime']
            )
            
            # Convert to daily data with additional metrics
            daily_vix = vix_5min.groupby(vix_5min['datetime'].dt.date).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).reset_index()
            
            daily_vix['date'] = pd.to_datetime(daily_vix['datetime'])
            daily_vix = daily_vix.rename(columns={'close': 'vix'})
            daily_vix = daily_vix.sort_values('date')
            
            # Calculate intraday volatility (proxy for term structure stress)
            daily_vix['intraday_range'] = (daily_vix['high'] - daily_vix['low']) / daily_vix['vix']
            daily_vix['gap'] = (daily_vix['open'] - daily_vix['vix'].shift(1)) / daily_vix['vix'].shift(1)
            
            self.vix_data = daily_vix
            
            print(f"✅ Loaded VIX data: {len(daily_vix)} days")
            print(f"   VIX range: {daily_vix['vix'].min():.1f} - {daily_vix['vix'].max():.1f}")
            print(f"   Date range: {daily_vix['date'].min()} to {daily_vix['date'].max()}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load VIX data: {e}")
            return False
    
    def calculate_term_structure_proxies(self):
        """Calculate term structure proxies using VIX momentum patterns"""
        
        if self.vix_data is None:
            if not self.load_vix_data():
                return False
        
        df = self.vix_data.copy()
        
        # Calculate momentum at different timeframes
        df['vix_5d_return'] = df['vix'].pct_change(self.SHORT_TERM_DAYS)
        df['vix_20d_return'] = df['vix'].pct_change(self.MEDIUM_TERM_DAYS)
        df['vix_60d_return'] = df['vix'].pct_change(self.LONG_TERM_DAYS)
        
        # Calculate moving averages for trend analysis
        df['vix_5d_ma'] = df['vix'].rolling(self.SHORT_TERM_DAYS).mean()
        df['vix_20d_ma'] = df['vix'].rolling(self.MEDIUM_TERM_DAYS).mean()
        df['vix_60d_ma'] = df['vix'].rolling(self.LONG_TERM_DAYS).mean()
        
        # Calculate volatility of VIX (volatility of volatility)
        df['vix_5d_vol'] = df['vix'].rolling(self.SHORT_TERM_DAYS).std()
        df['vix_20d_vol'] = df['vix'].rolling(self.MEDIUM_TERM_DAYS).std()
        
        # Term structure slope proxy (short-term vs medium-term momentum)
        df['term_structure_slope'] = df['vix_5d_return'] - df['vix_20d_return']
        
        # Mean reversion indicator
        df['vix_zscore'] = (df['vix'] - df['vix_20d_ma']) / df['vix_20d_vol']
        
        # Volatility clustering indicator
        df['vol_clustering'] = df['intraday_range'].rolling(5).mean() / df['intraday_range'].rolling(20).mean()
        
        # Term structure regime classification
        conditions = [
            df['term_structure_slope'] > self.CONTANGO_THRESHOLD,
            df['term_structure_slope'] < self.BACKWARDATION_THRESHOLD
        ]
        choices = ['contango', 'backwardation']
        df['term_structure_regime'] = np.select(conditions, choices, default='neutral')
        
        # VIX level regime
        df['vix_level_regime'] = pd.cut(
            df['vix'],
            bins=[0, 15, 25, 35, 100],
            labels=['low', 'normal', 'high', 'extreme']
        )
        
        # Combined regime (level + term structure)
        df['combined_regime'] = df['vix_level_regime'].astype(str) + '_' + df['term_structure_regime']
        
        self.term_structure_data = df
        
        print("✅ Calculated term structure proxies:")
        print(f"   Contango periods: {(df['term_structure_regime'] == 'contango').sum()} days")
        print(f"   Backwardation periods: {(df['term_structure_regime'] == 'backwardation').sum()} days")
        print(f"   Neutral periods: {(df['term_structure_regime'] == 'neutral').sum()} days")
        
        return True
    
    def get_term_structure_regime(self, date: datetime) -> Dict:
        """Get term structure regime for a specific date"""
        try:
            if self.term_structure_data is None:
                self.calculate_term_structure_proxies()
            
            # Convert to date if datetime
            if isinstance(date, datetime):
                date = date.date()
            
            # Find closest data
            mask = self.term_structure_data['date'].dt.date <= date
            if not mask.any():
                return self._get_default_regime(date)
            
            row = self.term_structure_data[mask].iloc[-1]
            
            return {
                'date': row['date'],
                'vix': row['vix'],
                'vix_level_regime': row['vix_level_regime'],
                'term_structure_regime': row['term_structure_regime'],
                'combined_regime': row['combined_regime'],
                'term_structure_slope': row['term_structure_slope'],
                'vix_zscore': row['vix_zscore'],
                'vol_clustering': row['vol_clustering'],
                'intraday_range': row['intraday_range'],
                'vix_5d_return': row['vix_5d_return'],
                'vix_20d_return': row['vix_20d_return']
            }
            
        except Exception as e:
            self.logger.warning(f"Could not get term structure regime for {date}: {e}")
            return self._get_default_regime(date)
    
    def _get_default_regime(self, date) -> Dict:
        """Return default regime values"""
        return {
            'date': date,
            'vix': 20.0,
            'vix_level_regime': 'normal',
            'term_structure_regime': 'neutral',
            'combined_regime': 'normal_neutral',
            'term_structure_slope': 0.0,
            'vix_zscore': 0.0,
            'vol_clustering': 1.0,
            'intraday_range': 0.05,
            'vix_5d_return': 0.0,
            'vix_20d_return': 0.0
        }
    
    def analyze_regime_performance(self, trades_data: pd.DataFrame) -> Dict:
        """Analyze trading performance by term structure regime"""
        
        if self.term_structure_data is None:
            self.calculate_term_structure_proxies()
        
        # Add term structure data to trades
        regime_data = []
        for _, trade in trades_data.iterrows():
            regime = self.get_term_structure_regime(trade['entry_date'])
            regime_data.append(regime)
        
        regime_df = pd.DataFrame(regime_data)
        enhanced_trades = pd.concat([trades_data, regime_df], axis=1)
        
        # Analyze performance by regime
        regime_performance = {}
        
        for regime in ['contango', 'backwardation', 'neutral']:
            regime_trades = enhanced_trades[enhanced_trades['term_structure_regime'] == regime]
            
            if len(regime_trades) > 0:
                regime_performance[regime] = {
                    'trade_count': len(regime_trades),
                    'win_rate': (regime_trades['final_pnl'] > 0).mean() * 100,
                    'avg_pnl': regime_trades['final_pnl'].mean(),
                    'total_pnl': regime_trades['final_pnl'].sum(),
                    'avg_vix': regime_trades['vix'].mean(),
                    'avg_slope': regime_trades['term_structure_slope'].mean()
                }
        
        # Analyze by combined regime
        combined_performance = {}
        for combined_regime in enhanced_trades['combined_regime'].unique():
            if pd.isna(combined_regime):
                continue
                
            regime_trades = enhanced_trades[enhanced_trades['combined_regime'] == combined_regime]
            
            if len(regime_trades) > 0:
                combined_performance[combined_regime] = {
                    'trade_count': len(regime_trades),
                    'win_rate': (regime_trades['final_pnl'] > 0).mean() * 100,
                    'avg_pnl': regime_trades['final_pnl'].mean(),
                    'total_pnl': regime_trades['final_pnl'].sum()
                }
        
        return {
            'term_structure_performance': regime_performance,
            'combined_regime_performance': combined_performance,
            'enhanced_trades': enhanced_trades
        }
    
    def should_trade_term_structure(self, date: datetime) -> Tuple[bool, str]:
        """
        Determine if trading should occur based on term structure regime
        Returns: (should_trade, reason)
        """
        regime = self.get_term_structure_regime(date)
        
        # Avoid trading during extreme backwardation (market stress) - Optimized threshold
        if (regime['term_structure_regime'] == 'backwardation' and
            regime['vix_zscore'] > 2.5):
            return False, f"Extreme backwardation with high VIX z-score ({regime['vix_zscore']:.1f})"
        
        # Avoid trading during extreme contango with low VIX (complacency)
        if (regime['term_structure_regime'] == 'contango' and 
            regime['vix'] < 12 and 
            regime['vix_zscore'] < -1.5):
            return False, f"Extreme contango with very low VIX ({regime['vix']:.1f})"
        
        return True, f"Term structure favorable ({regime['term_structure_regime']})"
    
    def get_position_adjustment(self, date: datetime) -> float:
        """Get position size adjustment based on term structure regime"""
        regime = self.get_term_structure_regime(date)
        
        # Base multiplier
        multiplier = 1.0
        
        # Adjust based on term structure regime
        if regime['term_structure_regime'] == 'contango':
            # Contango often indicates lower expected volatility
            multiplier *= 1.1  # Slightly increase position size
        elif regime['term_structure_regime'] == 'backwardation':
            # Backwardation often indicates higher expected volatility
            multiplier *= 0.9  # Slightly decrease position size
        
        # Adjust based on volatility clustering
        if regime['vol_clustering'] > 1.5:
            multiplier *= 0.8  # Reduce size during high volatility clustering
        elif regime['vol_clustering'] < 0.7:
            multiplier *= 1.2  # Increase size during low volatility periods
        
        return max(0.5, min(2.0, multiplier))  # Cap between 0.5x and 2.0x

if __name__ == "__main__":
    analyzer = VIXTermStructureAnalyzer()
    
    # Load and analyze VIX term structure
    if analyzer.calculate_term_structure_proxies():
        print("✅ VIX term structure analysis completed")
        
        # Test with sample date
        test_date = datetime(2024, 1, 15)
        regime = analyzer.get_term_structure_regime(test_date)
        print(f"\nSample regime for {test_date.date()}:")
        for key, value in regime.items():
            print(f"  {key}: {value}")
    else:
        print("❌ Failed to analyze VIX term structure")
