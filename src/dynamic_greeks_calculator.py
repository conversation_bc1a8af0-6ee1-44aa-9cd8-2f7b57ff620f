"""
Dynamic Greeks Calculator for Live Backtesting

Calculates Greeks in real-time during backtest, focusing on:
- Options near current price (within reasonable delta range)
- High open interest concentrations
- Target expiration ranges
- Fresh calculations each day as underlying moves
"""

import numpy as np
import pandas as pd
from scipy.stats import norm
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from src.constants import TARGET_EXPIRY_DAYS, STRIKE_MULTIPLE


class DynamicGreeksCalculator:
    """
    Real-time Greeks calculator optimized for backtesting
    
    Focuses on relevant options only:
    - Near-the-money options (within 20% of current price)
    - High open interest concentrations
    - Target expiration ranges
    """
    
    def __init__(self, risk_free_rate: float = 0.05):
        """Initialize the dynamic Greeks calculator"""
        self.risk_free_rate = risk_free_rate
        self.min_time_to_expiry = 1/365  # Minimum 1 day
        self.max_time_to_expiry = 180/365  # Maximum 180 days
        
        # Cache for performance
        self.greeks_cache = {}
        self.last_price = None
        self.cache_tolerance = 5.0  # Recalculate if price moves >$5
        
    def calculate_black_scholes_greeks(self, spot: float, strike: float, 
                                     time_to_expiry: float, volatility: float, 
                                     option_type: str) -> Dict[str, float]:
        """
        Calculate Black-Scholes Greeks efficiently
        
        Returns:
            Dictionary with delta, gamma, vanna, charm, vomma
        """
        try:
            # Handle edge cases
            if time_to_expiry <= 0:
                return self._intrinsic_greeks(spot, strike, option_type)
            
            if time_to_expiry > self.max_time_to_expiry:
                time_to_expiry = self.max_time_to_expiry
                
            # Calculate d1 and d2
            sqrt_t = np.sqrt(time_to_expiry)
            d1 = (np.log(spot / strike) + (self.risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / (volatility * sqrt_t)
            d2 = d1 - volatility * sqrt_t
            
            # Standard normal PDF and CDF
            n_d1 = norm.pdf(d1)
            N_d1 = norm.cdf(d1)
            N_d2 = norm.cdf(d2)
            
            # Delta
            if option_type.lower() == 'call':
                delta = N_d1
            else:
                delta = N_d1 - 1.0
            
            # Gamma (same for calls and puts)
            gamma = n_d1 / (spot * volatility * sqrt_t)
            
            # Vanna (delta sensitivity to volatility)
            vanna = -n_d1 * d2 / volatility
            
            # Charm (delta decay over time)
            if option_type.lower() == 'call':
                charm = -n_d1 * (2 * self.risk_free_rate * time_to_expiry - d2 * volatility * sqrt_t) / (2 * time_to_expiry * volatility * sqrt_t)
            else:
                charm = -n_d1 * (2 * self.risk_free_rate * time_to_expiry - d2 * volatility * sqrt_t) / (2 * time_to_expiry * volatility * sqrt_t)
            
            # Convert charm to per-day
            charm = charm * 365
            
            # Vomma (vega sensitivity to volatility)
            vomma = n_d1 * d1 * d2 / volatility
            
            return {
                'delta': delta,
                'gamma': gamma,
                'vanna': vanna,
                'charm': charm,
                'vomma': vomma
            }
            
        except Exception as e:
            # Return zero Greeks on error
            return {
                'delta': 0.0,
                'gamma': 0.0,
                'vanna': 0.0,
                'charm': 0.0,
                'vomma': 0.0
            }
    
    def _intrinsic_greeks(self, spot: float, strike: float, option_type: str) -> Dict[str, float]:
        """Return Greeks for options at expiration (intrinsic value only)"""
        if option_type.lower() == 'call':
            delta = 1.0 if spot > strike else 0.0
        else:
            delta = -1.0 if spot < strike else 0.0
            
        return {
            'delta': delta,
            'gamma': 0.0,
            'vanna': 0.0,
            'charm': 0.0,
            'vomma': 0.0
        }
    
    def filter_relevant_options(self, options_data: pd.DataFrame, 
                              current_price: float, current_date: pd.Timestamp) -> pd.DataFrame:
        """
        Filter options to only those relevant for Greeks calculations
        
        Criteria:
        - Within 20% of current price (or high OI)
        - Reasonable time to expiry (7-180 days)
        - Minimum volume or open interest
        """
        if options_data.empty:
            return pd.DataFrame()
        
        # Calculate time to expiry for all options
        options_data = options_data.copy()
        options_data['expiry_date'] = pd.to_datetime(options_data['expiration'])
        options_data['days_to_expiry'] = (options_data['expiry_date'] - current_date).dt.days
        options_data['time_to_expiry'] = options_data['days_to_expiry'] / 365.0
        
        # Filter by time to expiry
        relevant_options = options_data[
            (options_data['days_to_expiry'] >= 7) & 
            (options_data['days_to_expiry'] <= 180)
        ].copy()
        
        if relevant_options.empty:
            return pd.DataFrame()
        
        # Price range filter (20% around current price)
        price_range = current_price * 0.20
        price_filter = (
            (relevant_options['strike'] >= current_price - price_range) &
            (relevant_options['strike'] <= current_price + price_range)
        )
        
        # High open interest filter (regardless of price)
        high_oi_threshold = relevant_options['open_interest'].quantile(0.90)
        oi_filter = relevant_options['open_interest'] >= high_oi_threshold
        
        # Volume filter
        volume_filter = relevant_options['volume'] > 0
        
        # Combine filters: (near price OR high OI) AND has volume
        final_filter = (price_filter | oi_filter) & volume_filter
        
        relevant_options = relevant_options[final_filter]
        
        # Limit to top options by significance to prevent excessive computation
        if len(relevant_options) > 100:
            relevant_options['significance'] = (
                relevant_options['volume'] + 
                relevant_options['open_interest'] * 0.1 +
                (1 / (abs(relevant_options['strike'] - current_price) + 1)) * 1000  # Proximity bonus
            )
            relevant_options = relevant_options.nlargest(100, 'significance')
        
        return relevant_options
    
    def calculate_live_greeks(self, options_data: pd.DataFrame, 
                            current_price: float, current_date: pd.Timestamp,
                            volatility: float = 0.20) -> pd.DataFrame:
        """
        Calculate Greeks for relevant options during live backtesting
        
        Args:
            options_data: All options data for the day
            current_price: Current underlying price
            current_date: Current date
            volatility: Implied volatility estimate (default 20%)
            
        Returns:
            DataFrame with calculated Greeks for relevant options
        """
        # Check cache first
        if (self.last_price is not None and 
            abs(current_price - self.last_price) < self.cache_tolerance and
            current_date.strftime('%Y-%m-%d') in self.greeks_cache):
            return self.greeks_cache[current_date.strftime('%Y-%m-%d')]
        
        # Filter to relevant options only
        relevant_options = self.filter_relevant_options(options_data, current_price, current_date)
        
        if relevant_options.empty:
            return pd.DataFrame()
        
        # Calculate Greeks for each relevant option
        greeks_results = []
        
        for _, option in relevant_options.iterrows():
            try:
                # Get option parameters
                strike = option['strike']
                time_to_expiry = option['time_to_expiry']
                option_type = option['option_type'].lower()
                volume = option.get('volume', 0)
                open_interest = option.get('open_interest', 0)
                
                # Skip if no activity
                if volume == 0 and open_interest == 0:
                    continue
                
                # Calculate Greeks
                greeks = self.calculate_black_scholes_greeks(
                    current_price, strike, time_to_expiry, volatility, option_type
                )
                
                # Create result record
                result = {
                    'date': current_date,
                    'strike': strike,
                    'expiration': option['expiration'],
                    'option_type': option_type,
                    'underlying_price': current_price,
                    'time_to_expiry': time_to_expiry,
                    'days_to_expiry': option['days_to_expiry'],
                    'volume': volume,
                    'open_interest': open_interest,
                    'delta': greeks['delta'],
                    'gamma': greeks['gamma'],
                    'vanna': greeks['vanna'],
                    'charm': greeks['charm'],
                    'vomma': greeks['vomma']
                }
                
                greeks_results.append(result)
                
            except Exception as e:
                continue  # Skip problematic options
        
        # Create DataFrame
        greeks_df = pd.DataFrame(greeks_results)
        
        # Cache results
        self.greeks_cache[current_date.strftime('%Y-%m-%d')] = greeks_df
        self.last_price = current_price
        
        # Clean old cache entries (keep last 10 days)
        if len(self.greeks_cache) > 10:
            oldest_key = min(self.greeks_cache.keys())
            del self.greeks_cache[oldest_key]
        
        return greeks_df
    
    def analyze_greeks_convergence(self, greeks_df: pd.DataFrame, 
                                 current_price: float) -> Dict[str, float]:
        """
        Analyze Greeks convergence patterns for signal generation
        
        Returns:
            Dictionary with convergence metrics
        """
        if greeks_df.empty:
            return {
                'vanna_convergence': 0.0,
                'charm_momentum': 0.0,
                'gamma_concentration': 0.0,
                'delta_imbalance': 0.0,
                'vomma_volatility_signal': 0.0
            }
        
        # Weight by volume and open interest
        greeks_df['weight'] = greeks_df['volume'] + greeks_df['open_interest'] * 0.1
        
        # Vanna convergence (weighted by activity)
        weighted_vanna = (greeks_df['vanna'] * greeks_df['weight']).sum()
        total_weight = greeks_df['weight'].sum()
        vanna_convergence = weighted_vanna / (total_weight + 1e-6)
        
        # Charm momentum (time decay pressure)
        weighted_charm = (greeks_df['charm'] * greeks_df['weight']).sum()
        charm_momentum = weighted_charm / (total_weight + 1e-6)
        
        # Gamma concentration (convexity clustering)
        weighted_gamma = (greeks_df['gamma'] * greeks_df['weight']).sum()
        gamma_concentration = weighted_gamma / (total_weight + 1e-6)
        
        # Delta imbalance (directional bias)
        calls = greeks_df[greeks_df['option_type'] == 'c']
        puts = greeks_df[greeks_df['option_type'] == 'p']
        
        call_delta = (calls['delta'] * calls['weight']).sum() if not calls.empty else 0
        put_delta = (puts['delta'] * puts['weight']).sum() if not puts.empty else 0
        delta_imbalance = call_delta + put_delta  # Put delta is negative
        
        # Vomma volatility signal
        weighted_vomma = (greeks_df['vomma'] * greeks_df['weight']).sum()
        vomma_volatility_signal = weighted_vomma / (total_weight + 1e-6)
        
        return {
            'vanna_convergence': np.tanh(vanna_convergence * 10),  # Normalize to [-1, 1]
            'charm_momentum': np.tanh(charm_momentum / 100),
            'gamma_concentration': np.tanh(gamma_concentration * 1000),
            'delta_imbalance': np.tanh(delta_imbalance / 100),
            'vomma_volatility_signal': np.tanh(vomma_volatility_signal * 100)
        }
    
    def calculate_position_sizing_quality(self, greeks_metrics: Dict[str, float]) -> float:
        """
        Calculate position sizing quality based on Greeks convergence
        
        Returns:
            Quality score from 0.0 to 1.0
        """
        # Combine Greeks signals for quality assessment
        vanna_quality = abs(greeks_metrics['vanna_convergence'])
        charm_quality = abs(greeks_metrics['charm_momentum'])
        gamma_quality = abs(greeks_metrics['gamma_concentration'])
        
        # Weighted average quality
        quality = (
            0.4 * vanna_quality +      # 40% vanna convergence
            0.3 * charm_quality +      # 30% charm momentum
            0.3 * gamma_quality        # 30% gamma concentration
        )
        
        return min(quality, 1.0)
