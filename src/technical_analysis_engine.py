"""
Technical Analysis Engine for Enhanced Regime-Based Options Playbook
Implements SPX technical indicators to improve win rate beyond 34.1%
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional

class TechnicalAnalysisEngine:
    """
    Technical analysis engine for SPX underlying analysis
    Provides technical filters to enhance options trading signals
    """
    
    def __init__(self):
        """Initialize technical analysis engine"""
        self.logger = logging.getLogger(__name__)
        
        # Technical indicator parameters
        self.MA_SHORT = 20      # Short-term moving average
        self.MA_LONG = 50       # Long-term moving average
        self.RSI_PERIOD = 14    # RSI calculation period
        self.RSI_OVERBOUGHT = 70    # RSI overbought threshold
        self.RSI_OVERSOLD = 30      # RSI oversold threshold
        self.BB_PERIOD = 20     # Bollinger Bands period
        self.BB_STD = 2.0       # Bollinger Bands standard deviations
        self.VOLUME_LOOKBACK = 20   # Volume average lookback period
        
        # Cache for calculated indicators
        self.indicators_cache = {}
        
        print("✅ Technical Analysis Engine initialized")
        print(f"   📊 MA Periods: {self.MA_SHORT}/{self.MA_LONG} days")
        print(f"   📈 RSI: {self.RSI_PERIOD} days (OB: {self.RSI_OVERBOUGHT}, OS: {self.RSI_OVERSOLD})")
        print(f"   📉 Bollinger Bands: {self.BB_PERIOD} days, {self.BB_STD} std dev")
        print(f"   📊 Volume: {self.VOLUME_LOOKBACK} day average")
    
    def calculate_technical_indicators(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate all technical indicators for SPX price data
        
        Args:
            price_data: DataFrame with columns ['date', 'close', 'volume', 'high', 'low']
            
        Returns:
            DataFrame with technical indicators added
        """
        
        # Ensure data is sorted by date
        df = price_data.copy().sort_values('date')
        
        # Moving Averages
        df[f'ma_{self.MA_SHORT}'] = df['close'].rolling(window=self.MA_SHORT).mean()
        df[f'ma_{self.MA_LONG}'] = df['close'].rolling(window=self.MA_LONG).mean()
        
        # MA Crossover signals
        df['ma_bullish'] = (df[f'ma_{self.MA_SHORT}'] > df[f'ma_{self.MA_LONG}']) & \
                          (df[f'ma_{self.MA_SHORT}'].shift(1) <= df[f'ma_{self.MA_LONG}'].shift(1))
        df['ma_bearish'] = (df[f'ma_{self.MA_SHORT}'] < df[f'ma_{self.MA_LONG}']) & \
                          (df[f'ma_{self.MA_SHORT}'].shift(1) >= df[f'ma_{self.MA_LONG}'].shift(1))
        df['ma_trend'] = np.where(df[f'ma_{self.MA_SHORT}'] > df[f'ma_{self.MA_LONG}'], 'bullish', 'bearish')
        
        # RSI Calculation
        df['rsi'] = self._calculate_rsi(df['close'], self.RSI_PERIOD)
        df['rsi_overbought'] = df['rsi'] > self.RSI_OVERBOUGHT
        df['rsi_oversold'] = df['rsi'] < self.RSI_OVERSOLD
        df['rsi_neutral'] = (df['rsi'] >= self.RSI_OVERSOLD) & (df['rsi'] <= self.RSI_OVERBOUGHT)
        
        # Bollinger Bands
        bb_data = self._calculate_bollinger_bands(df['close'], self.BB_PERIOD, self.BB_STD)
        df['bb_upper'] = bb_data['upper']
        df['bb_middle'] = bb_data['middle']
        df['bb_lower'] = bb_data['lower']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        df['bb_outside'] = (df['close'] > df['bb_upper']) | (df['close'] < df['bb_lower'])
        df['bb_squeeze'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle'] < 0.1  # Tight bands
        
        # Volume Analysis
        df['volume_avg'] = df['volume'].rolling(window=self.VOLUME_LOOKBACK).mean()
        df['volume_ratio'] = df['volume'] / df['volume_avg']
        df['high_volume'] = df['volume_ratio'] > 1.2  # 20% above average
        df['low_volume'] = df['volume_ratio'] < 0.8   # 20% below average
        
        # Price momentum
        df['price_change_1d'] = df['close'].pct_change(1)
        df['price_change_5d'] = df['close'].pct_change(5)
        df['volatility_10d'] = df['price_change_1d'].rolling(window=10).std()
        
        return df
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate RSI (Relative Strength Index)"""
        
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int, std_dev: float) -> Dict:
        """Calculate Bollinger Bands"""
        
        middle = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        
        return {
            'upper': upper,
            'middle': middle,
            'lower': lower
        }
    
    def get_technical_filters(self, price_data: pd.DataFrame, date: datetime, 
                            signal_type: str) -> Dict:
        """
        Get technical analysis filters for a specific date and signal type
        
        Args:
            price_data: DataFrame with technical indicators
            date: Trading date
            signal_type: 'BULLISH' or 'BEARISH'
            
        Returns:
            Dictionary with filter results and recommendations
        """
        
        # Get data for the specific date
        date_data = price_data[price_data['date'] == date]
        
        if date_data.empty:
            return {
                'filters_available': False,
                'reason': 'No technical data available for date'
            }
        
        row = date_data.iloc[0]
        
        # Moving Average Filter
        ma_filter = self._evaluate_ma_filter(row, signal_type)
        
        # RSI Filter
        rsi_filter = self._evaluate_rsi_filter(row, signal_type)
        
        # Bollinger Bands Filter
        bb_filter = self._evaluate_bb_filter(row, signal_type)
        
        # Volume Filter
        volume_filter = self._evaluate_volume_filter(row)
        
        # Combine all filters
        all_filters = [ma_filter, rsi_filter, bb_filter, volume_filter]
        passed_filters = [f for f in all_filters if f['passed']]
        
        # Overall assessment
        filter_score = len(passed_filters) / len(all_filters)
        
        return {
            'filters_available': True,
            'date': date,
            'signal_type': signal_type,
            'ma_filter': ma_filter,
            'rsi_filter': rsi_filter,
            'bb_filter': bb_filter,
            'volume_filter': volume_filter,
            'filter_score': filter_score,
            'passed_filters': len(passed_filters),
            'total_filters': len(all_filters),
            'recommendation': self._get_filter_recommendation(filter_score, signal_type),
            'technical_data': {
                'close': row['close'],
                'ma_20': row.get(f'ma_{self.MA_SHORT}', None),
                'ma_50': row.get(f'ma_{self.MA_LONG}', None),
                'rsi': row.get('rsi', None),
                'bb_position': row.get('bb_position', None),
                'volume_ratio': row.get('volume_ratio', None)
            }
        }
    
    def _evaluate_ma_filter(self, row: pd.Series, signal_type: str) -> Dict:
        """Evaluate moving average filter"""
        
        ma_short = row.get(f'ma_{self.MA_SHORT}')
        ma_long = row.get(f'ma_{self.MA_LONG}')
        
        if pd.isna(ma_short) or pd.isna(ma_long):
            return {
                'name': 'Moving Average',
                'passed': False,
                'reason': 'Insufficient data for MA calculation',
                'confidence': 0.0
            }
        
        ma_trend = row.get('ma_trend', 'neutral')
        price = row['close']
        
        if signal_type == 'BULLISH':
            # For bullish signals, prefer uptrend and price above MAs
            trend_ok = ma_trend == 'bullish'
            price_ok = price > ma_short and price > ma_long
            passed = trend_ok and price_ok
            confidence = 0.8 if passed else 0.3
            reason = "Bullish MA trend and price above MAs" if passed else "MA trend not supportive of bullish signal"
            
        else:  # BEARISH
            # For bearish signals, prefer downtrend and price below MAs
            trend_ok = ma_trend == 'bearish'
            price_ok = price < ma_short and price < ma_long
            passed = trend_ok and price_ok
            confidence = 0.8 if passed else 0.3
            reason = "Bearish MA trend and price below MAs" if passed else "MA trend not supportive of bearish signal"
        
        return {
            'name': 'Moving Average',
            'passed': passed,
            'reason': reason,
            'confidence': confidence,
            'details': {
                'ma_trend': ma_trend,
                'price': price,
                'ma_20': ma_short,
                'ma_50': ma_long
            }
        }
    
    def _evaluate_rsi_filter(self, row: pd.Series, signal_type: str) -> Dict:
        """Evaluate RSI filter"""
        
        rsi = row.get('rsi')
        
        if pd.isna(rsi):
            return {
                'name': 'RSI',
                'passed': False,
                'reason': 'Insufficient data for RSI calculation',
                'confidence': 0.0
            }
        
        rsi_overbought = row.get('rsi_overbought', False)
        rsi_oversold = row.get('rsi_oversold', False)
        rsi_neutral = row.get('rsi_neutral', True)
        
        if signal_type == 'BULLISH':
            # Avoid bullish signals when overbought, prefer neutral or oversold
            passed = not rsi_overbought
            if rsi_oversold:
                confidence = 0.9  # Strong bullish setup
                reason = "RSI oversold - strong bullish setup"
            elif rsi_neutral:
                confidence = 0.7  # Good bullish setup
                reason = "RSI neutral - good bullish setup"
            else:
                confidence = 0.2  # Poor setup
                reason = "RSI overbought - avoid bullish signals"
                
        else:  # BEARISH
            # Avoid bearish signals when oversold, prefer neutral or overbought
            passed = not rsi_oversold
            if rsi_overbought:
                confidence = 0.9  # Strong bearish setup
                reason = "RSI overbought - strong bearish setup"
            elif rsi_neutral:
                confidence = 0.7  # Good bearish setup
                reason = "RSI neutral - good bearish setup"
            else:
                confidence = 0.2  # Poor setup
                reason = "RSI oversold - avoid bearish signals"
        
        return {
            'name': 'RSI',
            'passed': passed,
            'reason': reason,
            'confidence': confidence,
            'details': {
                'rsi': rsi,
                'overbought': rsi_overbought,
                'oversold': rsi_oversold,
                'neutral': rsi_neutral
            }
        }
    
    def _evaluate_bb_filter(self, row: pd.Series, signal_type: str) -> Dict:
        """Evaluate Bollinger Bands filter"""
        
        bb_position = row.get('bb_position')
        bb_outside = row.get('bb_outside', False)
        
        if pd.isna(bb_position):
            return {
                'name': 'Bollinger Bands',
                'passed': False,
                'reason': 'Insufficient data for BB calculation',
                'confidence': 0.0
            }
        
        # Avoid trades when price is outside Bollinger Bands (extreme conditions)
        passed = not bb_outside
        
        if bb_outside:
            confidence = 0.1
            reason = "Price outside Bollinger Bands - extreme conditions"
        elif 0.2 <= bb_position <= 0.8:
            confidence = 0.8
            reason = "Price within normal Bollinger Band range"
        else:
            confidence = 0.6
            reason = "Price near Bollinger Band edges"
        
        return {
            'name': 'Bollinger Bands',
            'passed': passed,
            'reason': reason,
            'confidence': confidence,
            'details': {
                'bb_position': bb_position,
                'bb_outside': bb_outside,
                'bb_upper': row.get('bb_upper'),
                'bb_lower': row.get('bb_lower'),
                'price': row['close']
            }
        }
    
    def _evaluate_volume_filter(self, row: pd.Series) -> Dict:
        """Evaluate volume filter"""
        
        volume_ratio = row.get('volume_ratio')
        high_volume = row.get('high_volume', False)
        
        if pd.isna(volume_ratio):
            return {
                'name': 'Volume',
                'passed': False,
                'reason': 'Insufficient data for volume calculation',
                'confidence': 0.0
            }
        
        # Prefer above-average volume for conviction
        passed = high_volume
        
        if high_volume:
            confidence = 0.8
            reason = "High volume confirms signal conviction"
        elif volume_ratio > 1.0:
            confidence = 0.6
            reason = "Above-average volume provides some confirmation"
        else:
            confidence = 0.4
            reason = "Below-average volume - weak confirmation"
        
        return {
            'name': 'Volume',
            'passed': passed,
            'reason': reason,
            'confidence': confidence,
            'details': {
                'volume_ratio': volume_ratio,
                'high_volume': high_volume,
                'volume': row.get('volume'),
                'volume_avg': row.get('volume_avg')
            }
        }
    
    def _get_filter_recommendation(self, filter_score: float, signal_type: str) -> Dict:
        """Get overall filter recommendation"""
        
        if filter_score >= 0.75:
            return {
                'action': 'STRONG_BUY' if signal_type == 'BULLISH' else 'STRONG_SELL',
                'confidence': 0.9,
                'reason': f"Strong technical confirmation ({filter_score:.1%} filters passed)"
            }
        elif filter_score >= 0.5:
            return {
                'action': 'BUY' if signal_type == 'BULLISH' else 'SELL',
                'confidence': 0.7,
                'reason': f"Good technical confirmation ({filter_score:.1%} filters passed)"
            }
        elif filter_score >= 0.25:
            return {
                'action': 'WEAK_BUY' if signal_type == 'BULLISH' else 'WEAK_SELL',
                'confidence': 0.5,
                'reason': f"Weak technical confirmation ({filter_score:.1%} filters passed)"
            }
        else:
            return {
                'action': 'AVOID',
                'confidence': 0.2,
                'reason': f"Poor technical setup ({filter_score:.1%} filters passed)"
            }


if __name__ == "__main__":
    # Test the technical analysis engine
    engine = TechnicalAnalysisEngine()
    
    # Create sample price data
    dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
    sample_data = pd.DataFrame({
        'date': dates,
        'close': 4000 + np.cumsum(np.random.randn(len(dates)) * 10),
        'volume': np.random.randint(1000000, 5000000, len(dates)),
        'high': 4000 + np.cumsum(np.random.randn(len(dates)) * 10) + 10,
        'low': 4000 + np.cumsum(np.random.randn(len(dates)) * 10) - 10
    })
    
    # Calculate indicators
    technical_data = engine.calculate_technical_indicators(sample_data)
    
    # Test filters
    test_date = pd.Timestamp('2023-06-15')
    filters = engine.get_technical_filters(technical_data, test_date, 'BULLISH')
    
    print("\n🧪 TECHNICAL ANALYSIS ENGINE TEST")
    print("=" * 50)
    print(f"Filter Score: {filters.get('filter_score', 0):.1%}")
    print(f"Recommendation: {filters.get('recommendation', {}).get('action', 'N/A')}")
    print("✅ Technical Analysis Engine test completed")
