"""
Enhanced Technical Strategy
Combines optimized Open-to-Open timing with technical analysis filters
Target: Improve win rate from 34.1% to 38-42%
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.technical_analysis_engine import TechnicalAnalysisEngine

class EnhancedTechnicalStrategy:
    """
    Enhanced strategy combining regime-based analysis, optimized timing, and technical filters
    """
    
    def __init__(self):
        """Initialize enhanced technical strategy"""
        self.technical_engine = TechnicalAnalysisEngine()
        
        # Technical filter thresholds
        self.FILTER_THRESHOLDS = {
            'strict': 0.75,      # 75% of filters must pass (highest win rate)
            'moderate': 0.5,     # 50% of filters must pass (balanced)
            'lenient': 0.25      # 25% of filters must pass (more trades)
        }
        
        print("✅ Enhanced Technical Strategy initialized")
        print("🎯 Target: Improve win rate from 34.1% to 38-42%")
    
    def prepare_spx_price_data(self, options_data: pd.DataFrame) -> pd.DataFrame:
        """
        Extract and prepare SPX price data from options dataset
        
        Args:
            options_data: Options dataset with underlying price data
            
        Returns:
            DataFrame with SPX price and volume data
        """
        
        print("📊 Preparing SPX price data for technical analysis...")
        
        # Extract unique daily price data
        price_data = options_data.groupby('date').agg({
            'underlying_close': 'first',
            'underlying_open': 'first',
            'underlying_high': 'first',
            'underlying_low': 'first',
            'volume': 'sum'  # Sum option volumes as proxy for underlying activity
        }).reset_index()

        # Use underlying data (SPX data is mapped to underlying columns)
        price_data['close'] = price_data['underlying_close']
        price_data['high'] = price_data['underlying_high'].fillna(price_data['close'] * 1.01)
        price_data['low'] = price_data['underlying_low'].fillna(price_data['close'] * 0.99)
        
        # Ensure we have required columns
        required_columns = ['date', 'close', 'high', 'low', 'volume']
        for col in required_columns:
            if col not in price_data.columns:
                if col == 'volume':
                    price_data[col] = 1000000  # Default volume
                else:
                    price_data[col] = price_data['close']
        
        # Sort by date
        price_data = price_data.sort_values('date').reset_index(drop=True)
        
        print(f"✅ Prepared {len(price_data)} days of SPX price data")
        print(f"📅 Date range: {price_data['date'].min()} to {price_data['date'].max()}")
        
        return price_data[required_columns]
    
    def apply_technical_filters(self, baseline_trades: pd.DataFrame, 
                              price_data: pd.DataFrame, 
                              filter_threshold: str = 'moderate') -> pd.DataFrame:
        """
        Apply technical analysis filters to baseline trades
        
        Args:
            baseline_trades: Baseline trades from optimized timing strategy
            price_data: SPX price data
            filter_threshold: 'strict', 'moderate', or 'lenient'
            
        Returns:
            Filtered trades with technical analysis applied
        """
        
        print(f"🔍 Applying technical filters (threshold: {filter_threshold})")
        
        # Calculate technical indicators
        technical_data = self.technical_engine.calculate_technical_indicators(price_data)
        
        # Apply filters to each trade
        filtered_trades = []
        filter_stats = {
            'total_trades': len(baseline_trades),
            'passed_ma': 0,
            'passed_rsi': 0,
            'passed_bb': 0,
            'passed_volume': 0,
            'passed_overall': 0
        }
        
        threshold_value = self.FILTER_THRESHOLDS[filter_threshold]
        
        for idx, trade in baseline_trades.iterrows():
            entry_date = pd.to_datetime(trade['entry_date'])
            signal_type = trade['signal_type']
            
            # Get technical filters for this trade
            filters = self.technical_engine.get_technical_filters(
                technical_data, entry_date, signal_type
            )
            
            if not filters['filters_available']:
                continue
            
            # Update filter statistics
            if filters['ma_filter']['passed']:
                filter_stats['passed_ma'] += 1
            if filters['rsi_filter']['passed']:
                filter_stats['passed_rsi'] += 1
            if filters['bb_filter']['passed']:
                filter_stats['passed_bb'] += 1
            if filters['volume_filter']['passed']:
                filter_stats['passed_volume'] += 1
            
            # Check if trade passes filter threshold
            filter_score = filters['filter_score']
            
            if filter_score >= threshold_value:
                filter_stats['passed_overall'] += 1
                
                # Enhance trade with technical analysis data
                enhanced_trade = trade.copy()
                enhanced_trade['technical_filter_score'] = filter_score
                enhanced_trade['technical_recommendation'] = filters['recommendation']['action']
                enhanced_trade['technical_confidence'] = filters['recommendation']['confidence']
                enhanced_trade['ma_passed'] = filters['ma_filter']['passed']
                enhanced_trade['rsi_passed'] = filters['rsi_filter']['passed']
                enhanced_trade['bb_passed'] = filters['bb_filter']['passed']
                enhanced_trade['volume_passed'] = filters['volume_filter']['passed']
                enhanced_trade['filter_threshold'] = filter_threshold
                
                # Apply technical enhancement to P&L (based on filter confidence)
                technical_multiplier = 1.0 + (filters['recommendation']['confidence'] - 0.5) * 0.1
                enhanced_trade['technical_enhanced_pnl'] = trade['final_pnl'] * technical_multiplier
                
                filtered_trades.append(enhanced_trade)
        
        # Convert to DataFrame
        if filtered_trades:
            result_df = pd.DataFrame(filtered_trades)
        else:
            result_df = pd.DataFrame()
        
        # Print filter statistics
        print(f"📊 Technical Filter Results ({filter_threshold} threshold):")
        print(f"   Total Trades: {filter_stats['total_trades']}")
        print(f"   MA Filter Passed: {filter_stats['passed_ma']} ({filter_stats['passed_ma']/filter_stats['total_trades']*100:.1f}%)")
        print(f"   RSI Filter Passed: {filter_stats['passed_rsi']} ({filter_stats['passed_rsi']/filter_stats['total_trades']*100:.1f}%)")
        print(f"   BB Filter Passed: {filter_stats['passed_bb']} ({filter_stats['passed_bb']/filter_stats['total_trades']*100:.1f}%)")
        print(f"   Volume Filter Passed: {filter_stats['passed_volume']} ({filter_stats['passed_volume']/filter_stats['total_trades']*100:.1f}%)")
        print(f"   Overall Passed: {filter_stats['passed_overall']} ({filter_stats['passed_overall']/filter_stats['total_trades']*100:.1f}%)")
        
        return result_df
    
    def test_individual_filters(self, baseline_trades: pd.DataFrame, 
                              price_data: pd.DataFrame) -> Dict:
        """
        Test each technical filter individually to measure impact
        
        Args:
            baseline_trades: Baseline trades
            price_data: SPX price data
            
        Returns:
            Dictionary with individual filter test results
        """
        
        print("🧪 TESTING INDIVIDUAL TECHNICAL FILTERS")
        print("=" * 50)
        
        # Calculate technical indicators
        technical_data = self.technical_engine.calculate_technical_indicators(price_data)
        
        # Test each filter individually
        filter_tests = {
            'baseline': self._calculate_performance(baseline_trades),
            'ma_only': self._test_single_filter(baseline_trades, technical_data, 'ma'),
            'rsi_only': self._test_single_filter(baseline_trades, technical_data, 'rsi'),
            'bb_only': self._test_single_filter(baseline_trades, technical_data, 'bb'),
            'volume_only': self._test_single_filter(baseline_trades, technical_data, 'volume')
        }
        
        # Print individual filter results
        baseline_win_rate = filter_tests['baseline']['win_rate']
        
        print(f"📊 INDIVIDUAL FILTER IMPACT:")
        print(f"   Baseline:     {baseline_win_rate:.1f}% win rate")
        
        for filter_name, results in filter_tests.items():
            if filter_name != 'baseline':
                improvement = results['win_rate'] - baseline_win_rate
                print(f"   {filter_name.upper():<12}: {results['win_rate']:.1f}% win rate ({improvement:+.1f}%) - {results['total_trades']} trades")
        
        return filter_tests
    
    def _test_single_filter(self, baseline_trades: pd.DataFrame, 
                          technical_data: pd.DataFrame, filter_type: str) -> Dict:
        """Test a single filter type"""
        
        filtered_trades = []
        
        for idx, trade in baseline_trades.iterrows():
            entry_date = pd.to_datetime(trade['entry_date'])
            signal_type = trade['signal_type']
            
            filters = self.technical_engine.get_technical_filters(
                technical_data, entry_date, signal_type
            )
            
            if not filters['filters_available']:
                continue
            
            # Check specific filter
            filter_passed = False
            if filter_type == 'ma':
                filter_passed = filters['ma_filter']['passed']
            elif filter_type == 'rsi':
                filter_passed = filters['rsi_filter']['passed']
            elif filter_type == 'bb':
                filter_passed = filters['bb_filter']['passed']
            elif filter_type == 'volume':
                filter_passed = filters['volume_filter']['passed']
            
            if filter_passed:
                filtered_trades.append(trade)
        
        if filtered_trades:
            return self._calculate_performance(pd.DataFrame(filtered_trades))
        else:
            return {'total_trades': 0, 'win_rate': 0.0, 'total_return': 0.0}
    
    def _calculate_performance(self, trades_df: pd.DataFrame) -> Dict:
        """Calculate performance metrics"""
        
        if trades_df.empty:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'total_return': 0.0,
                'max_drawdown': 0.0,
                'profit_factor': 0.0
            }
        
        # Use enhanced P&L if available, otherwise use original
        pnl_column = 'technical_enhanced_pnl' if 'technical_enhanced_pnl' in trades_df.columns else 'final_pnl'
        
        total_trades = len(trades_df)
        winning_trades = trades_df[trades_df[pnl_column] > 0]
        losing_trades = trades_df[trades_df[pnl_column] <= 0]
        
        win_rate = (len(winning_trades) / total_trades) * 100 if total_trades > 0 else 0.0
        total_pnl = trades_df[pnl_column].sum()
        total_return = (total_pnl / 100000.0) * 100
        
        # Profit factor
        gross_profit = winning_trades[pnl_column].sum() if len(winning_trades) > 0 else 0.0
        gross_loss = abs(losing_trades[pnl_column].sum()) if len(losing_trades) > 0 else 0.0
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0.0
        
        # Simplified drawdown
        trades_sorted = trades_df.sort_values('entry_date')
        trades_sorted['cumulative_pnl'] = trades_sorted[pnl_column].cumsum()
        trades_sorted['running_peak'] = trades_sorted['cumulative_pnl'].expanding().max()
        trades_sorted['drawdown'] = trades_sorted['cumulative_pnl'] - trades_sorted['running_peak']
        max_drawdown = abs(trades_sorted['drawdown'].min()) / 100000.0 * 100
        
        return {
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'win_rate': win_rate,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'avg_win': winning_trades[pnl_column].mean() if len(winning_trades) > 0 else 0.0,
            'avg_loss': losing_trades[pnl_column].mean() if len(losing_trades) > 0 else 0.0,
            'total_pnl': total_pnl
        }
    
    def run_comprehensive_technical_analysis(self, options_data: pd.DataFrame) -> Dict:
        """
        Run comprehensive technical analysis testing
        
        Args:
            options_data: Full options dataset
            
        Returns:
            Dictionary with all test results
        """
        
        print("🚀 COMPREHENSIVE TECHNICAL ANALYSIS TESTING")
        print("=" * 70)
        
        # Load baseline optimized timing trades
        try:
            baseline_trades = pd.read_csv('reports/optimized_timing_trades.csv')
            print(f"✅ Loaded {len(baseline_trades)} baseline trades (34.1% win rate)")
        except FileNotFoundError:
            print("❌ No optimized timing trades found. Run timing_optimized_backtest.py first.")
            return {}
        
        # Prepare SPX price data
        price_data = self.prepare_spx_price_data(options_data)
        
        # Test individual filters
        individual_results = self.test_individual_filters(baseline_trades, price_data)
        
        # Test combined filters with different thresholds
        print(f"\n🔄 TESTING COMBINED TECHNICAL FILTERS")
        print("-" * 50)
        
        combined_results = {}
        for threshold in ['lenient', 'moderate', 'strict']:
            filtered_trades = self.apply_technical_filters(baseline_trades, price_data, threshold)
            performance = self._calculate_performance(filtered_trades)
            combined_results[threshold] = {
                'performance': performance,
                'trades': filtered_trades
            }
            
            baseline_win_rate = individual_results['baseline']['win_rate']
            improvement = performance['win_rate'] - baseline_win_rate
            
            print(f"   {threshold.upper():<10}: {performance['win_rate']:.1f}% win rate ({improvement:+.1f}%) - {performance['total_trades']} trades")
        
        # Compile comprehensive results
        results = {
            'baseline_performance': individual_results['baseline'],
            'individual_filters': individual_results,
            'combined_filters': combined_results,
            'recommendations': self._generate_recommendations(individual_results, combined_results)
        }
        
        # Generate detailed report
        self._generate_technical_analysis_report(results)
        
        return results

    def _generate_recommendations(self, individual_results: Dict, combined_results: Dict) -> Dict:
        """Generate recommendations based on test results"""

        baseline_win_rate = individual_results['baseline']['win_rate']
        target_win_rate = 38.0  # Minimum target

        recommendations = {
            'best_individual_filter': None,
            'best_combined_approach': None,
            'target_achieved': False,
            'recommended_strategy': None
        }

        # Find best individual filter
        best_individual = None
        best_individual_improvement = 0

        for filter_name, results in individual_results.items():
            if filter_name != 'baseline':
                improvement = results['win_rate'] - baseline_win_rate
                if improvement > best_individual_improvement:
                    best_individual_improvement = improvement
                    best_individual = filter_name

        recommendations['best_individual_filter'] = {
            'filter': best_individual,
            'win_rate': individual_results[best_individual]['win_rate'] if best_individual else 0,
            'improvement': best_individual_improvement
        }

        # Find best combined approach
        best_combined = None
        best_combined_win_rate = 0

        for threshold, data in combined_results.items():
            win_rate = data['performance']['win_rate']
            if win_rate > best_combined_win_rate:
                best_combined_win_rate = win_rate
                best_combined = threshold

        recommendations['best_combined_approach'] = {
            'threshold': best_combined,
            'win_rate': best_combined_win_rate,
            'improvement': best_combined_win_rate - baseline_win_rate,
            'trades': combined_results[best_combined]['performance']['total_trades']
        }

        # Check if target achieved
        recommendations['target_achieved'] = best_combined_win_rate >= target_win_rate

        # Recommend strategy
        if recommendations['target_achieved']:
            recommendations['recommended_strategy'] = f"Combined filters ({best_combined} threshold)"
        elif best_individual_improvement > 2.0:  # Significant improvement
            recommendations['recommended_strategy'] = f"Individual filter ({best_individual})"
        else:
            recommendations['recommended_strategy'] = "Keep baseline strategy"

        return recommendations

    def _generate_technical_analysis_report(self, results: Dict):
        """Generate comprehensive technical analysis report"""

        print(f"\n📋 COMPREHENSIVE TECHNICAL ANALYSIS REPORT")
        print("=" * 70)

        baseline = results['baseline_performance']
        recommendations = results['recommendations']

        print(f"🎯 OBJECTIVE: Improve win rate from {baseline['win_rate']:.1f}% to 38-42%")
        print(f"📊 BASELINE: {baseline['total_trades']} trades, {baseline['win_rate']:.1f}% win rate, {baseline['total_return']:.1f}% return")

        print(f"\n🔍 INDIVIDUAL FILTER ANALYSIS:")
        print("-" * 40)

        for filter_name, results_data in results['individual_filters'].items():
            if filter_name != 'baseline':
                improvement = results_data['win_rate'] - baseline['win_rate']
                trade_reduction = baseline['total_trades'] - results_data['total_trades']
                print(f"   {filter_name.upper():<12}: {results_data['win_rate']:.1f}% ({improvement:+.1f}%) | "
                      f"{results_data['total_trades']} trades (-{trade_reduction})")

        print(f"\n🔄 COMBINED FILTER ANALYSIS:")
        print("-" * 40)

        for threshold, data in results['combined_filters'].items():
            perf = data['performance']
            improvement = perf['win_rate'] - baseline['win_rate']
            trade_reduction = baseline['total_trades'] - perf['total_trades']
            print(f"   {threshold.upper():<12}: {perf['win_rate']:.1f}% ({improvement:+.1f}%) | "
                  f"{perf['total_trades']} trades (-{trade_reduction}) | "
                  f"Return: {perf['total_return']:.1f}%")

        print(f"\n🏆 RECOMMENDATIONS:")
        print("-" * 30)

        best_individual = recommendations['best_individual_filter']
        best_combined = recommendations['best_combined_approach']

        print(f"Best Individual Filter: {best_individual['filter']} (+{best_individual['improvement']:.1f}%)")
        print(f"Best Combined Approach: {best_combined['threshold']} threshold (+{best_combined['improvement']:.1f}%)")
        print(f"Target Achieved: {'✅ YES' if recommendations['target_achieved'] else '❌ NO'}")
        print(f"Recommended Strategy: {recommendations['recommended_strategy']}")

        # Save detailed results
        self._save_technical_analysis_results(results)

        # Final assessment
        if recommendations['target_achieved']:
            print(f"\n🎉 SUCCESS: Target win rate achieved!")
            print(f"   Win Rate: {best_combined['win_rate']:.1f}% (target: 38-42%)")
            print(f"   Strategy: {recommendations['recommended_strategy']}")
        else:
            print(f"\n⚠️  PARTIAL SUCCESS: Target not fully achieved")
            print(f"   Best Win Rate: {best_combined['win_rate']:.1f}% (target: 38-42%)")
            print(f"   Recommendation: Consider further optimization or accept current improvement")

    def _save_technical_analysis_results(self, results: Dict):
        """Save technical analysis results to files"""

        # Save best performing trades
        best_threshold = results['recommendations']['best_combined_approach']['threshold']
        best_trades = results['combined_filters'][best_threshold]['trades']

        if not best_trades.empty:
            best_trades.to_csv('reports/technical_enhanced_trades.csv', index=False)
            print(f"\n💾 Best technical trades saved to: reports/technical_enhanced_trades.csv")

        # Save performance summary
        summary_data = []

        # Add baseline
        baseline = results['baseline_performance']
        summary_data.append({
            'Strategy': 'Baseline (Optimized Timing)',
            'Win_Rate': baseline['win_rate'],
            'Total_Trades': baseline['total_trades'],
            'Total_Return': baseline['total_return'],
            'Max_Drawdown': baseline['max_drawdown'],
            'Profit_Factor': baseline['profit_factor']
        })

        # Add individual filters
        for filter_name, perf in results['individual_filters'].items():
            if filter_name != 'baseline':
                summary_data.append({
                    'Strategy': f'Individual: {filter_name.upper()}',
                    'Win_Rate': perf['win_rate'],
                    'Total_Trades': perf['total_trades'],
                    'Total_Return': perf['total_return'],
                    'Max_Drawdown': perf['max_drawdown'],
                    'Profit_Factor': perf['profit_factor']
                })

        # Add combined filters
        for threshold, data in results['combined_filters'].items():
            perf = data['performance']
            summary_data.append({
                'Strategy': f'Combined: {threshold.title()}',
                'Win_Rate': perf['win_rate'],
                'Total_Trades': perf['total_trades'],
                'Total_Return': perf['total_return'],
                'Max_Drawdown': perf['max_drawdown'],
                'Profit_Factor': perf['profit_factor']
            })

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv('reports/technical_analysis_summary.csv', index=False)
        print(f"📊 Technical analysis summary saved to: reports/technical_analysis_summary.csv")


def main():
    """Run enhanced technical analysis"""

    print("🚀 ENHANCED TECHNICAL ANALYSIS FOR OPTIONS PLAYBOOK")
    print("=" * 70)
    print("🎯 Goal: Improve win rate from 34.1% to 38-42% using technical filters")
    print("📊 Filters: Moving Averages, RSI, Bollinger Bands, Volume")
    print("=" * 70)

    # Load options data (using existing combined file if available)
    try:
        options_data = pd.read_csv('data/SPX_COMPLETE_COMBINED.csv')
        options_data['date'] = pd.to_datetime(options_data['date'])
        options_data['expiration'] = pd.to_datetime(options_data['expiration'])
        print(f"✅ Loaded {len(options_data):,} options records")
    except FileNotFoundError:
        print("❌ No combined options data found. Run cluster_strategy_refactored.py first.")
        return

    # Initialize enhanced strategy
    strategy = EnhancedTechnicalStrategy()

    # Run comprehensive analysis
    results = strategy.run_comprehensive_technical_analysis(options_data)

    print(f"\n✅ Enhanced technical analysis completed!")

    return results


if __name__ == "__main__":
    results = main()
