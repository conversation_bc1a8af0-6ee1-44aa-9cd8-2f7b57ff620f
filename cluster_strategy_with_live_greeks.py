#!/usr/bin/env python3
"""
Enhanced Cluster Strategy with Live Greeks Calculation

Calculates Greeks dynamically during backtest:
- Fresh Greeks each day as underlying moves
- Focus on relevant options (near price + high OI)
- Dynamic position sizing based on Greeks quality
- Real-time convergence analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from src.dataloader import OptionsDataLoader
from src.config import Config
from src.emini_overnight_analyzer import Emini<PERSON>vernightAnalyzer
from src.dynamic_greeks_calculator import DynamicGreeksCalculator
from src.constants import (
    START_YEAR, BACKTEST_END_YEAR, STRIKE_MULTIPLE, TARGET_EXPIRY_DAYS,
    MAX_HOLD_DAYS, DRIFT_THRESHOLD, WALL_STRENGTH_THRESHOLD, VOLUME_PERCENTILE,
    GAMMA_MULTIPLIER, DELTA_MULTIPLIER, BAND_WIDTH, INIT<PERSON>L_CAPITAL,
    SPX_MULTIPLIER, MAX_CONTRACTS, MIN_CONTRACTS, MAX_RISK_PER_TRADE
)


class EnhancedClusterStrategy:
    """
    Enhanced cluster strategy with live Greeks calculation
    """
    
    def __init__(self, start_date: str, end_date: str):
        """Initialize the enhanced cluster strategy"""
        self.start_date = pd.to_datetime(start_date)
        self.end_date = pd.to_datetime(end_date)
        
        # Initialize Greeks calculator
        self.greeks_calculator = DynamicGreeksCalculator()
        
        # Strategy parameters
        self.params = {
            'drift_threshold': DRIFT_THRESHOLD,
            'wall_strength_threshold': WALL_STRENGTH_THRESHOLD,
            'volume_percentile': VOLUME_PERCENTILE,
            'gamma_multiplier': GAMMA_MULTIPLIER,
            'delta_multiplier': DELTA_MULTIPLIER,
            'band_width': BAND_WIDTH,
            'base_position_size': MIN_CONTRACTS,
            'max_position_size': MAX_CONTRACTS,
            'max_hold_days': MAX_HOLD_DAYS,
            'target_expiry_days': TARGET_EXPIRY_DAYS,
            'strike_multiple': STRIKE_MULTIPLE
        }
        
        # Initialize E-mini analyzer
        self.emini_analyzer = self._init_emini_analyzer()
        
        # Signal history for dynamic thresholds
        self.signal_history = []
        
    def _init_emini_analyzer(self):
        """Initialize E-mini analyzer with synchronized dates"""
        try:
            analyzer = EminiOvernightAnalyzer()
            analyzer.initialize_data(
                start_date=self.start_date.strftime('%Y-%m-%d'),
                end_date=self.end_date.strftime('%Y-%m-%d')
            )
            return analyzer
        except Exception as e:
            print(f"⚠️ Warning: Could not initialize E-mini analyzer: {e}")
            return None
    
    def detect_option_walls_with_live_greeks(self, options_data: pd.DataFrame, 
                                           current_price: float, current_date: pd.Timestamp) -> dict:
        """
        Detect option walls using live Greeks calculations
        """
        if options_data.empty:
            return self._empty_wall_analysis()
        
        # Calculate live Greeks for relevant options
        greeks_df = self.greeks_calculator.calculate_live_greeks(
            options_data, current_price, current_date
        )
        
        if greeks_df.empty:
            return self._empty_wall_analysis()
        
        # Calculate Greeks-weighted wall strengths
        greeks_df['gamma_weight'] = (
            greeks_df['gamma'] * 
            greeks_df['volume'] * 
            GAMMA_MULTIPLIER
        )
        
        greeks_df['delta_weight'] = (
            abs(greeks_df['delta']) * 
            greeks_df['open_interest'] * 
            DELTA_MULTIPLIER
        )
        
        # Separate calls and puts
        calls = greeks_df[greeks_df['option_type'] == 'c']
        puts = greeks_df[greeks_df['option_type'] == 'p']
        
        # Calculate wall strengths
        call_wall_strength = (calls['gamma_weight'].sum() + calls['delta_weight'].sum()) if not calls.empty else 0
        put_wall_strength = (puts['gamma_weight'].sum() + puts['delta_weight'].sum()) if not puts.empty else 0
        
        # Count significant strikes
        call_strikes = len(calls.groupby('strike')) if not calls.empty else 0
        put_strikes = len(puts.groupby('strike')) if not puts.empty else 0
        
        # Determine dominant wall
        if call_wall_strength > put_wall_strength:
            dominant_wall = 'CALL_WALL'
        elif put_wall_strength > call_wall_strength:
            dominant_wall = 'PUT_WALL'
        else:
            dominant_wall = 'NEUTRAL'
        
        # Analyze Greeks convergence
        greeks_metrics = self.greeks_calculator.analyze_greeks_convergence(greeks_df, current_price)
        
        return {
            'call_wall_strength': call_wall_strength,
            'put_wall_strength': put_wall_strength,
            'call_wall_count': call_strikes,
            'put_wall_count': put_strikes,
            'dominant_wall': dominant_wall,
            'delta_imbalance': greeks_metrics['delta_imbalance'],
            'vanna_convergence': greeks_metrics['vanna_convergence'],
            'charm_momentum': greeks_metrics['charm_momentum'],
            'gamma_concentration': greeks_metrics['gamma_concentration'],
            'vomma_volatility_signal': greeks_metrics['vomma_volatility_signal'],
            'greeks_quality': self.greeks_calculator.calculate_position_sizing_quality(greeks_metrics)
        }
    
    def _empty_wall_analysis(self) -> dict:
        """Return empty wall analysis"""
        return {
            'call_wall_strength': 0.0,
            'put_wall_strength': 0.0,
            'call_wall_count': 0,
            'put_wall_count': 0,
            'dominant_wall': 'NEUTRAL',
            'delta_imbalance': 0.0,
            'vanna_convergence': 0.0,
            'charm_momentum': 0.0,
            'gamma_concentration': 0.0,
            'vomma_volatility_signal': 0.0,
            'greeks_quality': 0.0
        }
    
    def generate_enhanced_signal(self, wall_data: dict, drift: float, current_price: float) -> dict:
        """
        Generate enhanced trading signal using live Greeks analysis
        """
        signal_type = 'NEUTRAL'
        signal_strength = 0.0
        rationale = "No clear signal"
        position_size = 0
        
        # Get wall and Greeks data
        call_strength = wall_data['call_wall_strength']
        put_strength = wall_data['put_wall_strength']
        vanna_conv = wall_data['vanna_convergence']
        charm_momentum = wall_data['charm_momentum']
        greeks_quality = wall_data['greeks_quality']
        
        # Minimum wall strength required
        min_wall_strength = WALL_STRENGTH_THRESHOLD
        
        # Skip if walls are too weak and Greeks quality is low
        if max(call_strength, put_strength) < min_wall_strength and greeks_quality < 0.3:
            return {
                'signal_type': signal_type,
                'signal_strength': signal_strength,
                'rationale': f"Weak signals: Walls={max(call_strength, put_strength):.1f}, Greeks quality={greeks_quality:.2f}",
                'position_size': position_size,
                'greeks_quality': greeks_quality
            }
        
        # Enhanced signal generation with Greeks
        if put_strength > call_strength:
            # Put wall dominance = Support level
            if drift >= 0 and vanna_conv > 0:
                signal_type = 'BULLISH'
                signal_strength = min((put_strength / 10000.0) * (1 + greeks_quality), 1.0)
                rationale = f"BULLISH: Put support + upward drift + positive vanna convergence ({vanna_conv:.3f})"
            elif drift < -self.params['drift_threshold'] and charm_momentum < 0:
                signal_type = 'BEARISH'
                signal_strength = min((put_strength + abs(drift)*100000) / 20000.0 * (1 + greeks_quality), 1.0)
                rationale = f"BEARISH: Breaking put support + down drift + negative charm ({charm_momentum:.3f})"
            elif abs(vanna_conv) > 0.3:  # Strong vanna signal
                signal_type = 'BULLISH' if vanna_conv > 0 else 'BEARISH'
                signal_strength = min(abs(vanna_conv) * greeks_quality, 1.0)
                rationale = f"{'BULLISH' if vanna_conv > 0 else 'BEARISH'}: Put support + vanna signal ({vanna_conv:.3f})"
        else:
            # Call wall dominance = Resistance level
            if drift <= 0 and vanna_conv < 0:
                signal_type = 'BEARISH'
                signal_strength = min((call_strength / 10000.0) * (1 + greeks_quality), 1.0)
                rationale = f"BEARISH: Call resistance + downward drift + negative vanna ({vanna_conv:.3f})"
            elif drift > self.params['drift_threshold'] and charm_momentum > 0:
                signal_type = 'BULLISH'
                signal_strength = min((call_strength + abs(drift)*100000) / 20000.0 * (1 + greeks_quality), 1.0)
                rationale = f"BULLISH: Breaking call resistance + up drift + positive charm ({charm_momentum:.3f})"
            elif abs(vanna_conv) > 0.3:  # Strong vanna signal
                signal_type = 'BEARISH' if vanna_conv > 0 else 'BULLISH'
                signal_strength = min(abs(vanna_conv) * greeks_quality, 1.0)
                rationale = f"{'BEARISH' if vanna_conv > 0 else 'BULLISH'}: Call resistance + vanna divergence ({vanna_conv:.3f})"
        
        # Calculate position size based on signal strength and Greeks quality
        if signal_strength > 0:
            base_size = max(
                self.params['base_position_size'],
                int(signal_strength * self.params['max_position_size'])
            )
            
            # Adjust size based on Greeks quality
            quality_multiplier = 0.5 + (greeks_quality * 1.5)  # 0.5x to 2.0x
            position_size = max(1, int(base_size * quality_multiplier))
            position_size = min(position_size, self.params['max_position_size'])
        
        return {
            'signal_type': signal_type,
            'signal_strength': signal_strength,
            'rationale': rationale,
            'position_size': position_size,
            'greeks_quality': greeks_quality,
            'vanna_convergence': vanna_conv,
            'charm_momentum': charm_momentum
        }
    
    def find_option_strike(self, signal_type: str, current_price: float, 
                          signal_date: pd.Timestamp, options_data: pd.DataFrame) -> dict:
        """
        Find appropriate option strike for trading
        """
        if signal_type == 'NEUTRAL':
            return None
        
        # Determine target strike based on signal type
        if signal_type == 'BULLISH':
            target_strike = ((int(current_price) // STRIKE_MULTIPLE) + 1) * STRIKE_MULTIPLE
            option_type = 'c'
        else:
            target_strike = (int(current_price) // STRIKE_MULTIPLE) * STRIKE_MULTIPLE
            option_type = 'p'
        
        # Target expiry date
        target_expiry = signal_date + pd.Timedelta(days=TARGET_EXPIRY_DAYS)
        
        # Find options near target strike and expiry
        target_options = options_data[
            (options_data['strike'] == target_strike) &
            (options_data['option_type'].str.lower() == option_type)
        ].copy()
        
        if target_options.empty:
            return None
        
        # Find expiry closest to target
        target_options['expiry_date'] = pd.to_datetime(target_options['expiration'])
        target_options['days_to_expiry'] = (target_options['expiry_date'] - signal_date).dt.days
        target_options = target_options[target_options['days_to_expiry'] > 0]
        
        if target_options.empty:
            return None
        
        # Get closest expiry to target
        closest_expiry_days = target_options['days_to_expiry'].iloc[
            (target_options['days_to_expiry'] - TARGET_EXPIRY_DAYS).abs().argsort()[:1]
        ].iloc[0]
        
        best_option = target_options[
            target_options['days_to_expiry'] == closest_expiry_days
        ].iloc[0]
        
        # Get entry price
        entry_price = best_option.get('Last Trade Price', 0)
        if entry_price <= 0:
            bid = best_option.get('bid', 0)
            ask = best_option.get('ask', 0)
            if bid > 0 and ask > 0:
                entry_price = (bid + ask) / 2
            else:
                entry_price = ask if ask > 0 else bid
        
        if entry_price <= 0:
            return None
        
        return {
            'strike': best_option['strike'],
            'expiry': best_option['expiration'],
            'option_type': best_option['option_type'],
            'entry_price': entry_price,
            'days_to_expiry': closest_expiry_days
        }


def run_enhanced_backtest(start_year: int = START_YEAR):
    """Run the enhanced cluster strategy backtest with live Greeks"""
    
    print("🧮 ENHANCED CLUSTER STRATEGY WITH LIVE GREEKS")
    print("=" * 70)
    print("LIVE GREEKS CALCULATION:")
    print("  📊 Fresh Greeks calculated each day as underlying moves")
    print("  🎯 Focus on relevant options (near price + high OI)")
    print("  🧮 Vanna convergence analysis for volatility-delta relationships")
    print("  ⚡ Charm momentum for time decay pressure")
    print("  📏 Dynamic position sizing based on Greeks quality")
    print(f"  ⏰ Options trading: Enter next day open, exit day {MAX_HOLD_DAYS} close, {TARGET_EXPIRY_DAYS} day expiry")
    print(f"  📅 Data from: {start_year} onwards")
    print("=" * 70)
    
    # Load data
    config = Config()
    loader = OptionsDataLoader(config)
    
    print(f"🔍 SEARCHING FOR SPX OPTIONS DATA (from {start_year})")
    print("=" * 50)
    
    options_data = loader.load_data_from_year(start_year)
    
    if options_data.empty:
        print("❌ No options data found!")
        return None
    
    # Get date range
    start_date = f"{start_year}-01-01"
    end_date = options_data['date'].max().strftime('%Y-%m-%d')
    
    print(f"📅 Backtest Period: {start_date} to {end_date}")
    
    # Initialize strategy
    strategy = EnhancedClusterStrategy(start_date, end_date)
    
    # Run backtest
    print(f"\n💰 Starting Capital: ${INITIAL_CAPITAL:,.2f}")
    print("🎯 Strategy: Enhanced Cluster Strategy with Live Greeks")
    
    # Get trading days
    trading_days = pd.date_range(start=start_date, end=end_date, freq='D')
    trading_days = [d for d in trading_days if d.weekday() < 5]  # Weekdays only
    
    # Initialize tracking
    current_capital = INITIAL_CAPITAL
    positions = []
    trades = []
    daily_analysis = []
    
    print(f"📈 Total trading days: {len(trading_days)}")
    
    for i, date in enumerate(trading_days):
        if i % 50 == 0:
            print(f"  Processing {i+1}/{len(trading_days)}: {date.strftime('%Y-%m-%d')}")
        
        # Get options data for this day
        day_options = options_data[options_data['date'] == date]
        
        if day_options.empty:
            continue
        
        # Get current price
        current_price = day_options['underlying_close'].iloc[0]
        
        # Get overnight drift
        overnight_drift = 0.0
        if strategy.emini_analyzer:
            try:
                overnight_drift = strategy.emini_analyzer.get_overnight_drift(date.strftime('%Y-%m-%d'))
            except:
                pass
        
        # Detect walls with live Greeks
        wall_data = strategy.detect_option_walls_with_live_greeks(day_options, current_price, date)
        
        # Generate enhanced signal
        signal = strategy.generate_enhanced_signal(wall_data, overnight_drift, current_price)
        
        # Debug output for first few days
        if i < 3:
            print(f"    🔍 Day {i+1}: Signal={signal['signal_type']}, Strength={signal['signal_strength']:.3f}, Quality={signal['greeks_quality']:.2f}")
            print(f"        Greeks: Vanna={signal['vanna_convergence']:.3f}, Charm={signal['charm_momentum']:.3f}")
            print(f"        Walls: {wall_data['dominant_wall']} (C:{wall_data['call_wall_count']}, P:{wall_data['put_wall_count']})")
            print(f"        Drift: {overnight_drift*100:.2f}%, Rationale: {signal['rationale']}")
        
        # Process existing positions (exit logic)
        for position in positions[:]:
            days_held = (date - position['entry_date']).days
            
            if days_held >= MAX_HOLD_DAYS:
                # Exit position
                exit_option = strategy.find_option_strike(
                    position['signal_type'], current_price, date, day_options
                )
                
                if exit_option:
                    exit_price = exit_option['entry_price']
                else:
                    exit_price = position['entry_price'] * 0.5  # Assume 50% loss if can't find
                
                # Calculate P&L
                pnl = (exit_price - position['entry_price']) * SPX_MULTIPLIER * position['position_size']
                current_capital += pnl
                
                # Record trade
                trade = {
                    'entry_date': position['entry_date'],
                    'exit_date': date,
                    'signal_type': position['signal_type'],
                    'strike': position['strike'],
                    'expiry': position['expiry'],
                    'option_type': position['option_type'],
                    'entry_price': position['entry_price'],
                    'exit_price': exit_price,
                    'position_size': position['position_size'],
                    'pnl': pnl,
                    'exit_reason': f"MAX_HOLD_CLOSE ({days_held} days)",
                    'days_held': days_held,
                    'greeks_quality': position.get('greeks_quality', 0)
                }
                trades.append(trade)
                
                # Remove position
                positions.remove(position)
                
                if i < 5:  # Debug for first few days
                    result = "WIN" if pnl > 0 else "LOSS"
                    print(f"    🎉 {result}: {position['option_type']} ${position['strike']} = ${pnl:,.0f} (Quality: {position.get('greeks_quality', 0):.2f})")
        
        # Entry logic
        if signal['signal_type'] != 'NEUTRAL' and len(positions) < 3:  # Max 3 positions
            option_details = strategy.find_option_strike(
                signal['signal_type'], current_price, date, day_options
            )
            
            if option_details:
                # Calculate position size with risk management
                contracts = signal['position_size']
                max_risk = current_capital * MAX_RISK_PER_TRADE
                max_contracts_by_risk = int(max_risk / (100 * option_details['entry_price']))
                contracts = min(contracts, max(1, max_contracts_by_risk))
                
                # Create new position
                new_position = {
                    'entry_date': date + pd.Timedelta(days=1),  # Enter next day
                    'signal_date': date,
                    'entry_price': option_details['entry_price'],
                    'strike': option_details['strike'],
                    'expiry': option_details['expiry'],
                    'option_type': option_details['option_type'],
                    'signal_type': signal['signal_type'],
                    'position_size': contracts,
                    'signal_strength': signal['signal_strength'],
                    'rationale': signal['rationale'],
                    'greeks_quality': signal['greeks_quality']
                }
                
                positions.append(new_position)
                
                if i < 5:  # Debug for first few days
                    print(f"    📈 ENTRY: {option_details['option_type']} ${option_details['strike']} at ${option_details['entry_price']}, {contracts} contracts (Quality: {signal['greeks_quality']:.2f})")
        
        # Store daily analysis
        daily_analysis.append({
            'date': date,
            'current_price': current_price,
            'overnight_drift': overnight_drift,
            'call_wall_strength': wall_data['call_wall_strength'],
            'put_wall_strength': wall_data['put_wall_strength'],
            'signal_type': signal['signal_type'],
            'signal_strength': signal['signal_strength'],
            'greeks_quality': signal['greeks_quality'],
            'vanna_convergence': signal['vanna_convergence'],
            'charm_momentum': signal['charm_momentum'],
            'active_positions': len(positions),
            'current_capital': current_capital
        })
    
    # Calculate results
    if trades:
        trades_df = pd.DataFrame(trades)
        winning_trades = trades_df[trades_df['pnl'] > 0]
        losing_trades = trades_df[trades_df['pnl'] <= 0]
        
        total_trades = len(trades_df)
        win_rate = len(winning_trades) / total_trades * 100
        total_pnl = trades_df['pnl'].sum()
        total_return = total_pnl / INITIAL_CAPITAL * 100
        
        avg_win = winning_trades['pnl'].mean() if not winning_trades.empty else 0
        avg_loss = losing_trades['pnl'].mean() if not losing_trades.empty else 0
        
        profit_factor = abs(winning_trades['pnl'].sum() / losing_trades['pnl'].sum()) if not losing_trades.empty and losing_trades['pnl'].sum() != 0 else float('inf')
        
        # Greeks quality analysis
        avg_quality = trades_df['greeks_quality'].mean()
        high_quality_trades = trades_df[trades_df['greeks_quality'] > 0.5]
        high_quality_win_rate = len(high_quality_trades[high_quality_trades['pnl'] > 0]) / len(high_quality_trades) * 100 if not high_quality_trades.empty else 0
        
        print(f"\n🧮 ENHANCED CLUSTER STRATEGY RESULTS (Live Greeks)")
        print("=" * 70)
        print(f"Total Trades: {total_trades}")
        print(f"Winning Trades: {len(winning_trades)}")
        print(f"Win Rate: {win_rate:.1f}%")
        print(f"Total P&L: ${total_pnl:,.2f}")
        print(f"Total Return: {total_return:.1f}%")
        print(f"Final Capital: ${current_capital:,.2f}")
        print(f"Average Win: ${avg_win:,.0f}")
        print(f"Average Loss: ${avg_loss:,.0f}")
        print(f"Profit Factor: {profit_factor:.2f}")
        print(f"\n📊 GREEKS QUALITY ANALYSIS:")
        print(f"Average Greeks Quality: {avg_quality:.2f}")
        print(f"High Quality Trades (>0.5): {len(high_quality_trades)}")
        print(f"High Quality Win Rate: {high_quality_win_rate:.1f}%")
        
        return {
            'trades': trades_df,
            'daily_analysis': pd.DataFrame(daily_analysis),
            'total_return': total_return,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'profit_factor': profit_factor,
            'avg_greeks_quality': avg_quality,
            'high_quality_win_rate': high_quality_win_rate
        }
    
    else:
        print("❌ No trades executed!")
        return None


if __name__ == "__main__":
    results = run_enhanced_backtest(START_YEAR)
