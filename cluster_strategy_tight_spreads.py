#!/usr/bin/env python3
"""
Improved Cluster Strategy with Tight Spreads and Closer Expiry

DRAWDOWN REDUCTION STRATEGY:
- Use credit spreads instead of outright options
- Closer expiry (14 days instead of 30)
- Tighter position sizing (1-2 contracts max)
- Higher signal quality requirements
- Faster exits (1-2 days max hold)
- Lower risk per trade (3% instead of 10%)
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from src.dataloader import OptionsDataLoader
from src.config import Config
from src.emini_overnight_analyzer import EminiOvernightAnalyzer
from src.constants import (
    START_YEAR, BACKTEST_END_YEAR, STRIKE_MULTIPLE, TARGET_EXPIRY_DAYS,
    MAX_HOLD_DAYS, DRIFT_THRESHOLD, WALL_STRENGTH_THRESHOLD, VOLUME_PERCENTILE,
    GAMMA_MULTIPLIER, DELTA_MULTIPLIER, BAND_WIDTH, INITIAL_CAPITAL,
    SPX_MULTIPLIER, MAX_CONTRACTS, MIN_CONTRACTS, MAX_RISK_PER_TRADE,
    USE_SPREADS, SPREAD_WIDTH, MAX_SPREAD_RISK, MIN_SPREAD_CREDIT,
    SPREAD_PROFIT_TARGET, SPREAD_LOSS_LIMIT
)


class TightSpreadStrategy:
    """
    Improved cluster strategy with tight spreads for drawdown reduction
    """
    
    def __init__(self, start_date: str, end_date: str):
        """Initialize the tight spread strategy"""
        self.start_date = pd.to_datetime(start_date)
        self.end_date = pd.to_datetime(end_date)
        
        # Strategy parameters - TIGHTER RISK MANAGEMENT
        self.params = {
            'drift_threshold': DRIFT_THRESHOLD,  # 0.2% (tighter)
            'wall_strength_threshold': WALL_STRENGTH_THRESHOLD,  # 5.0 (higher)
            'volume_percentile': VOLUME_PERCENTILE,  # 80% (higher)
            'gamma_multiplier': GAMMA_MULTIPLIER,
            'delta_multiplier': DELTA_MULTIPLIER,
            'band_width': BAND_WIDTH,  # 8% (tighter)
            'base_position_size': MIN_CONTRACTS,  # 1 contract
            'max_position_size': MAX_CONTRACTS,  # 2 contracts max
            'max_hold_days': MAX_HOLD_DAYS,  # 1 day max
            'target_expiry_days': TARGET_EXPIRY_DAYS,  # 14 days
            'strike_multiple': STRIKE_MULTIPLE,
            'use_spreads': USE_SPREADS,
            'spread_width': SPREAD_WIDTH,
            'max_spread_risk': MAX_SPREAD_RISK,
            'min_spread_credit': MIN_SPREAD_CREDIT,
            'spread_profit_target': SPREAD_PROFIT_TARGET,
            'spread_loss_limit': SPREAD_LOSS_LIMIT
        }
        
        # Initialize E-mini analyzer
        self.emini_analyzer = self._init_emini_analyzer()
        
    def _init_emini_analyzer(self):
        """Initialize E-mini analyzer with synchronized dates"""
        try:
            analyzer = EminiOvernightAnalyzer()
            analyzer.initialize_data(
                start_date=self.start_date.strftime('%Y-%m-%d'),
                end_date=self.end_date.strftime('%Y-%m-%d')
            )
            return analyzer
        except Exception as e:
            print(f"⚠️ Warning: Could not initialize E-mini analyzer: {e}")
            return None
    
    def detect_high_quality_walls(self, options_data: pd.DataFrame, current_price: float) -> dict:
        """
        Detect high-quality option walls with stricter requirements
        """
        if options_data.empty:
            return self._empty_wall_analysis()
        
        # Filter for high-quality options only
        high_quality_options = options_data[
            (options_data['volume'] > 0) & 
            (options_data['open_interest'] > 100) &  # Higher OI requirement
            (options_data['volume'] >= options_data['volume'].quantile(self.params['volume_percentile']/100))
        ].copy()
        
        if high_quality_options.empty:
            return self._empty_wall_analysis()
        
        # Calculate enhanced wall strength using volume and OI
        high_quality_options['wall_strength'] = (
            high_quality_options['volume'] * 2.0 +  # Higher volume weight
            high_quality_options['open_interest'] * 0.2
        )
        
        # Separate calls and puts
        calls = high_quality_options[high_quality_options['option_type'].str.lower() == 'c']
        puts = high_quality_options[high_quality_options['option_type'].str.lower() == 'p']
        
        # Calculate wall strengths
        call_wall_strength = calls['wall_strength'].sum() if not calls.empty else 0
        put_wall_strength = puts['wall_strength'].sum() if not puts.empty else 0
        
        # Count significant strikes
        call_strikes = len(calls.groupby('strike')) if not calls.empty else 0
        put_strikes = len(puts.groupby('strike')) if not puts.empty else 0
        
        # Determine dominant wall with higher threshold
        wall_ratio = abs(call_wall_strength - put_wall_strength) / (call_wall_strength + put_wall_strength + 1e-6)
        
        if wall_ratio < 0.3:  # Require 30% dominance
            dominant_wall = 'NEUTRAL'
        elif call_wall_strength > put_wall_strength:
            dominant_wall = 'CALL_WALL'
        else:
            dominant_wall = 'PUT_WALL'
        
        return {
            'call_wall_strength': call_wall_strength,
            'put_wall_strength': put_wall_strength,
            'call_wall_count': call_strikes,
            'put_wall_count': put_strikes,
            'dominant_wall': dominant_wall,
            'delta_imbalance': call_wall_strength - put_wall_strength,
            'wall_quality': wall_ratio  # Quality metric
        }
    
    def _empty_wall_analysis(self) -> dict:
        """Return empty wall analysis"""
        return {
            'call_wall_strength': 0.0,
            'put_wall_strength': 0.0,
            'call_wall_count': 0,
            'put_wall_count': 0,
            'dominant_wall': 'NEUTRAL',
            'delta_imbalance': 0.0,
            'wall_quality': 0.0
        }
    
    def generate_high_quality_signal(self, wall_data: dict, drift: float, current_price: float) -> dict:
        """
        Generate high-quality trading signals with stricter requirements
        """
        signal_type = 'NEUTRAL'
        signal_strength = 0.0
        rationale = "No high-quality signal"
        position_size = 0
        
        # Get wall data
        call_strength = wall_data['call_wall_strength']
        put_strength = wall_data['put_wall_strength']
        wall_quality = wall_data['wall_quality']
        
        # Require high wall strength AND quality
        min_wall_strength = self.params['wall_strength_threshold']
        min_wall_quality = 0.3  # Require 30% dominance
        
        if (max(call_strength, put_strength) < min_wall_strength or 
            wall_quality < min_wall_quality):
            return {
                'signal_type': signal_type,
                'signal_strength': signal_strength,
                'rationale': f"Low quality: Walls={max(call_strength, put_strength):.1f}, Quality={wall_quality:.2f}",
                'position_size': position_size,
                'wall_quality': wall_quality
            }
        
        # Enhanced signal generation with stricter drift requirements
        strong_drift_threshold = self.params['drift_threshold'] * 2  # 0.4% for strong moves
        
        if put_strength > call_strength:
            # Put wall dominance = Support level
            if drift >= strong_drift_threshold:  # Strong upward drift required
                signal_type = 'BULLISH'
                signal_strength = min((put_strength / 20000.0) * (1 + wall_quality), 1.0)
                rationale = f"BULLISH: Strong put support ({put_strength:.0f}) + strong up drift ({drift*100:.2f}%)"
            elif drift < -strong_drift_threshold:  # Strong downward drift
                signal_type = 'BEARISH'
                signal_strength = min((put_strength / 15000.0) * (1 + wall_quality), 1.0)
                rationale = f"BEARISH: Breaking put support + strong down drift ({drift*100:.2f}%)"
        else:
            # Call wall dominance = Resistance level
            if drift <= -strong_drift_threshold:  # Strong downward drift required
                signal_type = 'BEARISH'
                signal_strength = min((call_strength / 20000.0) * (1 + wall_quality), 1.0)
                rationale = f"BEARISH: Strong call resistance ({call_strength:.0f}) + strong down drift ({drift*100:.2f}%)"
            elif drift > strong_drift_threshold:  # Strong upward drift
                signal_type = 'BULLISH'
                signal_strength = min((call_strength / 15000.0) * (1 + wall_quality), 1.0)
                rationale = f"BULLISH: Breaking call resistance + strong up drift ({drift*100:.2f}%)"
        
        # Conservative position sizing
        if signal_strength > 0.7:  # Only trade very strong signals
            position_size = self.params['max_position_size']  # 2 contracts max
        elif signal_strength > 0.5:
            position_size = self.params['base_position_size']  # 1 contract
        else:
            signal_type = 'NEUTRAL'  # Filter out weak signals
            signal_strength = 0.0
            rationale = f"Signal too weak: {signal_strength:.2f}"
            position_size = 0
        
        return {
            'signal_type': signal_type,
            'signal_strength': signal_strength,
            'rationale': rationale,
            'position_size': position_size,
            'wall_quality': wall_quality
        }
    
    def find_spread_strikes(self, signal_type: str, current_price: float, 
                           signal_date: pd.Timestamp, options_data: pd.DataFrame) -> dict:
        """
        Find appropriate strikes for credit spread trading
        """
        if signal_type == 'NEUTRAL':
            return None
        
        # For credit spreads:
        # BEARISH signal -> Put credit spread (sell put, buy lower put)
        # BULLISH signal -> Call credit spread (sell call, buy higher call)
        
        if signal_type == 'BEARISH':
            # Put credit spread: sell put near current price, buy put below
            short_strike = (int(current_price) // STRIKE_MULTIPLE) * STRIKE_MULTIPLE
            long_strike = short_strike - SPREAD_WIDTH
            option_type = 'p'
        else:  # BULLISH
            # Call credit spread: sell call near current price, buy call above
            short_strike = ((int(current_price) // STRIKE_MULTIPLE) + 1) * STRIKE_MULTIPLE
            long_strike = short_strike + SPREAD_WIDTH
            option_type = 'c'
        
        # Target expiry date (closer expiry for faster decay)
        target_expiry = signal_date + pd.Timedelta(days=TARGET_EXPIRY_DAYS)
        
        # Find options for both strikes
        short_options = options_data[
            (options_data['strike'] == short_strike) &
            (options_data['option_type'].str.lower() == option_type)
        ].copy()
        
        long_options = options_data[
            (options_data['strike'] == long_strike) &
            (options_data['option_type'].str.lower() == option_type)
        ].copy()
        
        if short_options.empty or long_options.empty:
            return None
        
        # Find expiry closest to target for both strikes
        short_options['expiry_date'] = pd.to_datetime(short_options['expiration'])
        short_options['days_to_expiry'] = (short_options['expiry_date'] - signal_date).dt.days
        short_options = short_options[short_options['days_to_expiry'] > 0]
        
        long_options['expiry_date'] = pd.to_datetime(long_options['expiration'])
        long_options['days_to_expiry'] = (long_options['expiry_date'] - signal_date).dt.days
        long_options = long_options[long_options['days_to_expiry'] > 0]
        
        if short_options.empty or long_options.empty:
            return None
        
        # Get closest expiry for both
        target_dte = TARGET_EXPIRY_DAYS
        short_best = short_options.iloc[(short_options['days_to_expiry'] - target_dte).abs().argsort()[:1]]
        long_best = long_options.iloc[(long_options['days_to_expiry'] - target_dte).abs().argsort()[:1]]
        
        if short_best.empty or long_best.empty:
            return None
        
        short_option = short_best.iloc[0]
        long_option = long_best.iloc[0]
        
        # Must have same expiry
        if short_option['expiration'] != long_option['expiration']:
            return None
        
        # Calculate spread prices
        short_price = short_option.get('Last Trade Price', 0)
        if short_price <= 0:
            short_bid = short_option.get('bid', 0)
            short_ask = short_option.get('ask', 0)
            short_price = (short_bid + short_ask) / 2 if short_bid > 0 and short_ask > 0 else short_ask
        
        long_price = long_option.get('Last Trade Price', 0)
        if long_price <= 0:
            long_bid = long_option.get('bid', 0)
            long_ask = long_option.get('ask', 0)
            long_price = (long_bid + long_ask) / 2 if long_bid > 0 and long_ask > 0 else long_bid
        
        if short_price <= 0 or long_price <= 0:
            return None
        
        # Credit spread: receive premium for short, pay premium for long
        net_credit = short_price - long_price
        max_risk = SPREAD_WIDTH - net_credit  # Maximum loss
        
        # Check if spread meets minimum credit requirement
        if net_credit * SPX_MULTIPLIER < MIN_SPREAD_CREDIT:
            return None
        
        # Check if risk is acceptable
        if max_risk * SPX_MULTIPLIER > MAX_SPREAD_RISK:
            return None
        
        return {
            'short_strike': short_strike,
            'long_strike': long_strike,
            'expiry': short_option['expiration'],
            'option_type': option_type,
            'short_price': short_price,
            'long_price': long_price,
            'net_credit': net_credit,
            'max_risk': max_risk,
            'days_to_expiry': short_option['days_to_expiry'],
            'spread_type': f"{signal_type.lower()}_credit_spread"
        }


def run_tight_spread_backtest(start_year: int = START_YEAR):
    """Run the tight spread strategy backtest for drawdown reduction"""
    
    print("🎯 TIGHT SPREAD STRATEGY FOR DRAWDOWN REDUCTION")
    print("=" * 70)
    print("RISK REDUCTION FEATURES:")
    print("  📉 Credit spreads instead of outright options (limited risk)")
    print("  ⏰ Closer expiry (14 days) for faster time decay")
    print("  📏 Smaller position sizes (1-2 contracts max)")
    print("  🎯 Higher signal quality requirements")
    print("  ⚡ Faster exits (1 day max hold)")
    print("  💰 Lower risk per trade (3% max instead of 10%)")
    print("  🔍 Stricter wall detection (80th percentile volume)")
    print(f"  📅 Data from: {start_year} onwards")
    print("=" * 70)
    
    # Load data
    config = Config()
    loader = OptionsDataLoader(config)
    
    print(f"🔍 SEARCHING FOR SPX OPTIONS DATA (from {start_year})")
    print("=" * 50)
    
    options_data = loader.load_data_from_year(start_year)
    
    if options_data.empty:
        print("❌ No options data found!")
        return None
    
    # Get date range
    start_date = f"{start_year}-01-01"
    end_date = options_data['date'].max().strftime('%Y-%m-%d')
    
    print(f"📅 Backtest Period: {start_date} to {end_date}")
    
    # Initialize strategy
    strategy = TightSpreadStrategy(start_date, end_date)
    
    # Run backtest
    print(f"\n💰 Starting Capital: ${INITIAL_CAPITAL:,.2f}")
    print("🎯 Strategy: Tight Spread Strategy (Drawdown Reduction)")
    
    # Get trading days
    trading_days = pd.date_range(start=start_date, end=end_date, freq='D')
    trading_days = [d for d in trading_days if d.weekday() < 5]  # Weekdays only
    
    # Initialize tracking
    current_capital = INITIAL_CAPITAL
    positions = []
    trades = []
    daily_analysis = []
    
    print(f"📈 Total trading days: {len(trading_days)}")
    
    for i, date in enumerate(trading_days):
        if i % 50 == 0:
            print(f"  Processing {i+1}/{len(trading_days)}: {date.strftime('%Y-%m-%d')}")
        
        # Get options data for this day
        day_options = options_data[options_data['date'] == date]
        
        if day_options.empty:
            continue
        
        # Get current price
        current_price = day_options['underlying_close'].iloc[0]
        
        # Get overnight drift
        overnight_drift = 0.0
        if strategy.emini_analyzer:
            try:
                overnight_drift = strategy.emini_analyzer.get_overnight_drift(date.strftime('%Y-%m-%d'))
            except:
                pass
        
        # Detect high-quality walls
        wall_data = strategy.detect_high_quality_walls(day_options, current_price)
        
        # Generate high-quality signal
        signal = strategy.generate_high_quality_signal(wall_data, overnight_drift, current_price)
        
        # Debug output for first few days
        if i < 3:
            print(f"    🔍 Day {i+1}: Signal={signal['signal_type']}, Strength={signal['signal_strength']:.3f}, Quality={signal['wall_quality']:.2f}")
            print(f"        Walls: {wall_data['dominant_wall']} (C:{wall_data['call_wall_count']}, P:{wall_data['put_wall_count']})")
            print(f"        Drift: {overnight_drift*100:.2f}%, Rationale: {signal['rationale']}")
        
        # Process existing positions (exit logic)
        for position in positions[:]:
            days_held = (date - position['entry_date']).days
            
            # Check for profit target or loss limit
            current_spread_value = 0  # Would need to calculate current spread value
            
            if days_held >= MAX_HOLD_DAYS:
                # Exit position at max hold time
                exit_credit = position['net_credit'] * 0.3  # Assume 70% profit capture
                
                # Calculate P&L for spread
                pnl = (position['net_credit'] - exit_credit) * SPX_MULTIPLIER * position['position_size']
                current_capital += pnl
                
                # Record trade
                trade = {
                    'entry_date': position['entry_date'],
                    'exit_date': date,
                    'signal_type': position['signal_type'],
                    'spread_type': position['spread_type'],
                    'short_strike': position['short_strike'],
                    'long_strike': position['long_strike'],
                    'expiry': position['expiry'],
                    'option_type': position['option_type'],
                    'entry_credit': position['net_credit'],
                    'exit_credit': exit_credit,
                    'position_size': position['position_size'],
                    'pnl': pnl,
                    'exit_reason': f"MAX_HOLD_CLOSE ({days_held} days)",
                    'days_held': days_held,
                    'max_risk': position['max_risk'],
                    'wall_quality': position.get('wall_quality', 0)
                }
                trades.append(trade)
                
                # Remove position
                positions.remove(position)
                
                if i < 5:  # Debug for first few days
                    result = "WIN" if pnl > 0 else "LOSS"
                    print(f"    🎉 {result}: {position['spread_type']} ${position['short_strike']}/{position['long_strike']} = ${pnl:,.0f}")
        
        # Entry logic - only high quality signals
        if signal['signal_type'] != 'NEUTRAL' and len(positions) < 1:  # Max 1 position for tight risk
            spread_details = strategy.find_spread_strikes(
                signal['signal_type'], current_price, date, day_options
            )
            
            if spread_details:
                # Calculate position size with strict risk management
                contracts = signal['position_size']
                max_risk_dollars = current_capital * MAX_RISK_PER_TRADE
                max_contracts_by_risk = int(max_risk_dollars / (spread_details['max_risk'] * SPX_MULTIPLIER))
                contracts = min(contracts, max(1, max_contracts_by_risk))
                
                # Create new spread position
                new_position = {
                    'entry_date': date + pd.Timedelta(days=1),  # Enter next day
                    'signal_date': date,
                    'net_credit': spread_details['net_credit'],
                    'short_strike': spread_details['short_strike'],
                    'long_strike': spread_details['long_strike'],
                    'expiry': spread_details['expiry'],
                    'option_type': spread_details['option_type'],
                    'signal_type': signal['signal_type'],
                    'spread_type': spread_details['spread_type'],
                    'position_size': contracts,
                    'signal_strength': signal['signal_strength'],
                    'rationale': signal['rationale'],
                    'wall_quality': signal['wall_quality'],
                    'max_risk': spread_details['max_risk']
                }
                
                positions.append(new_position)
                
                if i < 5:  # Debug for first few days
                    print(f"    📈 ENTRY: {spread_details['spread_type']} ${spread_details['short_strike']}/{spread_details['long_strike']} credit ${spread_details['net_credit']:.2f}, {contracts} contracts")
        
        # Store daily analysis
        daily_analysis.append({
            'date': date,
            'current_price': current_price,
            'overnight_drift': overnight_drift,
            'call_wall_strength': wall_data['call_wall_strength'],
            'put_wall_strength': wall_data['put_wall_strength'],
            'signal_type': signal['signal_type'],
            'signal_strength': signal['signal_strength'],
            'wall_quality': signal['wall_quality'],
            'active_positions': len(positions),
            'current_capital': current_capital
        })
    
    # Calculate results
    if trades:
        trades_df = pd.DataFrame(trades)
        winning_trades = trades_df[trades_df['pnl'] > 0]
        losing_trades = trades_df[trades_df['pnl'] <= 0]
        
        total_trades = len(trades_df)
        win_rate = len(winning_trades) / total_trades * 100
        total_pnl = trades_df['pnl'].sum()
        total_return = total_pnl / INITIAL_CAPITAL * 100
        
        avg_win = winning_trades['pnl'].mean() if not winning_trades.empty else 0
        avg_loss = losing_trades['pnl'].mean() if not losing_trades.empty else 0
        
        profit_factor = abs(winning_trades['pnl'].sum() / losing_trades['pnl'].sum()) if not losing_trades.empty and losing_trades['pnl'].sum() != 0 else float('inf')
        
        # Risk analysis
        max_risk_per_trade = trades_df['max_risk'].max() * SPX_MULTIPLIER if 'max_risk' in trades_df.columns else 0
        avg_wall_quality = trades_df['wall_quality'].mean() if 'wall_quality' in trades_df.columns else 0
        
        print(f"\n🎯 TIGHT SPREAD STRATEGY RESULTS (Drawdown Reduction)")
        print("=" * 70)
        print(f"Total Trades: {total_trades}")
        print(f"Winning Trades: {len(winning_trades)}")
        print(f"Win Rate: {win_rate:.1f}%")
        print(f"Total P&L: ${total_pnl:,.2f}")
        print(f"Total Return: {total_return:.1f}%")
        print(f"Final Capital: ${current_capital:,.2f}")
        print(f"Average Win: ${avg_win:,.0f}")
        print(f"Average Loss: ${avg_loss:,.0f}")
        print(f"Profit Factor: {profit_factor:.2f}")
        print(f"\n📊 RISK ANALYSIS:")
        print(f"Max Risk Per Trade: ${max_risk_per_trade:,.0f}")
        print(f"Average Wall Quality: {avg_wall_quality:.2f}")
        print(f"Risk Per Trade: {max_risk_per_trade/INITIAL_CAPITAL*100:.1f}% of capital")
        
        return {
            'trades': trades_df,
            'daily_analysis': pd.DataFrame(daily_analysis),
            'total_return': total_return,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'profit_factor': profit_factor,
            'max_risk_per_trade': max_risk_per_trade,
            'avg_wall_quality': avg_wall_quality
        }
    
    else:
        print("❌ No trades executed!")
        return None


if __name__ == "__main__":
    results = run_tight_spread_backtest(START_YEAR)
