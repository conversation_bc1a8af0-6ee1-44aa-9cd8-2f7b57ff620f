#!/usr/bin/env python3
"""
Real Options Data Loader for Enhanced VIX Options Strategy
Loads actual historical SPX options data for realistic backtesting
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import glob
from pathlib import Path

class RealOptionsDataLoader:
    """Load and manage real historical SPX options data"""
    
    def __init__(self, data_root: str = "../optionhistory"):
        self.data_root = Path(data_root)
        self.options_data = {}
        self.loaded_quarters = set()
        
    def load_quarter_data(self, year: int, quarter: str) -> bool:
        """Load options data for a specific quarter"""
        
        quarter_key = f"{year}_{quarter}"
        if quarter_key in self.loaded_quarters:
            return True
            
        quarter_dir = self.data_root / f"{year}_{quarter}_option_chain"
        spx_file = quarter_dir / f"spx_complete_{year}_{quarter}.csv"
        
        if not spx_file.exists():
            print(f"⚠️ Options data not found: {spx_file}")
            return False
            
        try:
            print(f"📊 Loading options data: {quarter_key}")
            df = pd.read_csv(spx_file)
            
            # Convert date columns
            df['date'] = pd.to_datetime(df['date'])
            df['Expiry Date'] = pd.to_datetime(df['Expiry Date'])
            
            # Clean and validate data
            df = self._clean_options_data(df)
            
            # Store data
            self.options_data[quarter_key] = df
            self.loaded_quarters.add(quarter_key)
            
            print(f"✅ Loaded {len(df):,} option records for {quarter_key}")
            return True
            
        except Exception as e:
            print(f"❌ Error loading {quarter_key}: {str(e)}")
            return False
    
    def _clean_options_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and validate options data"""
        
        # Remove invalid prices
        df = df[df['Last Trade Price'] > 0]
        df = df[df['Bid Price'] >= 0]
        df = df[df['Ask Price'] > 0]
        
        # Remove invalid strikes
        df = df[df['Strike'] > 0]
        
        # Calculate mid price for better pricing
        df['Mid Price'] = (df['Bid Price'] + df['Ask Price']) / 2
        
        # Use Last Trade Price if available, otherwise use Mid Price
        df['Best Price'] = df['Last Trade Price'].fillna(df['Mid Price'])
        
        return df
    
    def get_option_price(self, date: datetime, strike: float, option_type: str, 
                        expiry_date: datetime, use_mid_price: bool = True) -> Optional[float]:
        """Get real option price for specific parameters"""
        
        # Determine which quarter to load
        year = date.year
        quarter = f"q{((date.month - 1) // 3) + 1}"
        quarter_key = f"{year}_{quarter}"
        
        # Load quarter data if not already loaded
        if quarter_key not in self.loaded_quarters:
            if not self.load_quarter_data(year, quarter):
                return None
        
        if quarter_key not in self.options_data:
            return None
            
        df = self.options_data[quarter_key]
        
        # Filter for specific option
        option_filter = (
            (df['date'] == date.strftime('%Y-%m-%d')) &
            (df['Strike'] == strike) &
            (df['Call/Put'] == option_type.lower()) &
            (df['Expiry Date'] == expiry_date.strftime('%Y-%m-%d'))
        )
        
        matching_options = df[option_filter]
        
        if len(matching_options) == 0:
            return None
        
        # Return best available price
        option_row = matching_options.iloc[0]
        
        if use_mid_price and option_row['Mid Price'] > 0:
            return float(option_row['Mid Price'])
        elif option_row['Last Trade Price'] > 0:
            return float(option_row['Last Trade Price'])
        else:
            return float(option_row['Mid Price'])
    
    def find_best_strike(self, date: datetime, underlying_price: float, 
                        option_type: str, target_expiry_days: int = 30,
                        otm_distance: float = 0.02) -> Optional[Tuple[float, datetime, float]]:
        """Find the best available strike and expiry for trading"""
        
        # Determine which quarter to load
        year = date.year
        quarter = f"q{((date.month - 1) // 3) + 1}"
        quarter_key = f"{year}_{quarter}"
        
        # Load quarter data if not already loaded
        if quarter_key not in self.loaded_quarters:
            if not self.load_quarter_data(year, quarter):
                return None
        
        if quarter_key not in self.options_data:
            return None
            
        df = self.options_data[quarter_key]
        
        # Filter for the specific date and option type
        date_filter = df['date'] == date.strftime('%Y-%m-%d')
        type_filter = df['Call/Put'] == option_type.lower()
        
        available_options = df[date_filter & type_filter].copy()
        
        if len(available_options) == 0:
            return None
        
        # Calculate days to expiry
        available_options['days_to_expiry'] = (
            available_options['Expiry Date'] - pd.to_datetime(date)
        ).dt.days
        
        # Filter for reasonable expiry range (20-40 days)
        expiry_filter = (
            (available_options['days_to_expiry'] >= 20) &
            (available_options['days_to_expiry'] <= 40)
        )
        
        valid_options = available_options[expiry_filter]
        
        if len(valid_options) == 0:
            return None
        
        # Calculate target strike
        if option_type.lower() == 'c':
            target_strike = underlying_price * (1 + otm_distance)
        else:  # put
            target_strike = underlying_price * (1 - otm_distance)
        
        # Round to nearest 25 (SPX standard)
        target_strike = round(target_strike / 25) * 25
        
        # Find closest available strike
        valid_options['strike_diff'] = abs(valid_options['Strike'] - target_strike)
        
        # Sort by strike difference, then by days to expiry closest to 30
        valid_options['expiry_diff'] = abs(valid_options['days_to_expiry'] - target_expiry_days)
        valid_options = valid_options.sort_values(['strike_diff', 'expiry_diff'])
        
        if len(valid_options) == 0:
            return None
        
        best_option = valid_options.iloc[0]
        
        strike = float(best_option['Strike'])
        expiry_date = pd.to_datetime(best_option['Expiry Date']).to_pydatetime()
        price = float(best_option['Best Price'])
        
        return strike, expiry_date, price
    
    def get_available_dates(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """Get all available trading dates in the specified range"""
        
        available_dates = set()
        
        # Determine quarters to load
        current_date = start_date
        while current_date <= end_date:
            year = current_date.year
            quarter = f"q{((current_date.month - 1) // 3) + 1}"
            quarter_key = f"{year}_{quarter}"
            
            # Load quarter data if not already loaded
            if quarter_key not in self.loaded_quarters:
                self.load_quarter_data(year, quarter)
            
            if quarter_key in self.options_data:
                quarter_dates = self.options_data[quarter_key]['date'].unique()
                for date_str in quarter_dates:
                    date_obj = pd.to_datetime(date_str).to_pydatetime()
                    if start_date <= date_obj <= end_date:
                        available_dates.add(date_obj)
            
            # Move to next quarter
            if current_date.month <= 3:
                current_date = datetime(current_date.year, 4, 1)
            elif current_date.month <= 6:
                current_date = datetime(current_date.year, 7, 1)
            elif current_date.month <= 9:
                current_date = datetime(current_date.year, 10, 1)
            else:
                current_date = datetime(current_date.year + 1, 1, 1)
        
        return sorted(list(available_dates))
    
    def get_underlying_price(self, date: datetime) -> Optional[float]:
        """Get SPX underlying price for a specific date"""
        
        # Determine which quarter to load
        year = date.year
        quarter = f"q{((date.month - 1) // 3) + 1}"
        quarter_key = f"{year}_{quarter}"
        
        # Load quarter data if not already loaded
        if quarter_key not in self.loaded_quarters:
            if not self.load_quarter_data(year, quarter):
                return None
        
        if quarter_key not in self.options_data:
            return None
            
        df = self.options_data[quarter_key]
        
        # Get SPX close price for the date
        date_filter = df['date'] == date.strftime('%Y-%m-%d')
        matching_data = df[date_filter]
        
        if len(matching_data) == 0:
            return None
        
        return float(matching_data.iloc[0]['spx_close'])
    
    def validate_data_availability(self, start_date: datetime, end_date: datetime) -> Dict:
        """Validate data availability for the specified date range"""
        
        validation_results = {
            'total_quarters': 0,
            'available_quarters': 0,
            'missing_quarters': [],
            'available_dates': 0,
            'date_range_coverage': 0.0
        }
        
        # Check each quarter in the range
        current_date = start_date
        total_quarters = 0
        available_quarters = 0
        
        while current_date <= end_date:
            year = current_date.year
            quarter = f"q{((current_date.month - 1) // 3) + 1}"
            quarter_key = f"{year}_{quarter}"
            
            total_quarters += 1
            
            if self.load_quarter_data(year, quarter):
                available_quarters += 1
            else:
                validation_results['missing_quarters'].append(quarter_key)
            
            # Move to next quarter
            if current_date.month <= 3:
                current_date = datetime(current_date.year, 4, 1)
            elif current_date.month <= 6:
                current_date = datetime(current_date.year, 7, 1)
            elif current_date.month <= 9:
                current_date = datetime(current_date.year, 10, 1)
            else:
                current_date = datetime(current_date.year + 1, 1, 1)
        
        validation_results['total_quarters'] = total_quarters
        validation_results['available_quarters'] = available_quarters
        
        # Get available dates
        available_dates = self.get_available_dates(start_date, end_date)
        validation_results['available_dates'] = len(available_dates)
        
        # Calculate coverage
        total_days = (end_date - start_date).days + 1
        validation_results['date_range_coverage'] = len(available_dates) / total_days if total_days > 0 else 0.0
        
        return validation_results

def main():
    """Test the real options data loader"""
    
    print("🔧 Testing Real Options Data Loader")
    print("=" * 50)
    
    loader = RealOptionsDataLoader()
    
    # Test data validation
    start_date = datetime(2012, 1, 3)
    end_date = datetime(2012, 3, 31)
    
    validation = loader.validate_data_availability(start_date, end_date)
    print(f"📊 Data Validation Results:")
    print(f"   Total Quarters: {validation['total_quarters']}")
    print(f"   Available Quarters: {validation['available_quarters']}")
    print(f"   Available Dates: {validation['available_dates']}")
    print(f"   Coverage: {validation['date_range_coverage']:.1%}")
    
    if validation['missing_quarters']:
        print(f"   Missing: {validation['missing_quarters']}")
    
    # Test option price lookup
    test_date = datetime(2012, 1, 3)
    underlying_price = loader.get_underlying_price(test_date)
    
    if underlying_price:
        print(f"\n📈 SPX Price on {test_date.strftime('%Y-%m-%d')}: ${underlying_price:.2f}")
        
        # Test finding best strike
        strike_info = loader.find_best_strike(
            test_date, underlying_price, 'p', target_expiry_days=30, otm_distance=0.02
        )
        
        if strike_info:
            strike, expiry, price = strike_info
            print(f"📊 Best Put Option:")
            print(f"   Strike: ${strike:.0f}")
            print(f"   Expiry: {expiry.strftime('%Y-%m-%d')}")
            print(f"   Price: ${price:.2f}")
    
    print("\n✅ Real Options Data Loader test completed!")

if __name__ == "__main__":
    main()
