"""
Market Regime Correlation Analyzer for JPM Collar Strategy
Analyzes correlations between strategy performance and market indicators (VIX, UTY)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class MarketRegimeAnalyzer:
    """Analyze strategy performance correlations with market regime indicators"""
    
    def __init__(self, trades_file: str = "reports/cluster_trades_refactored.csv",
                 daily_file: str = "reports/cluster_daily_refactored.csv"):
        """Initialize with trade and daily performance data"""
        self.trades_file = trades_file
        self.daily_file = daily_file
        self.trades_data = None
        self.daily_data = None
        self.market_data = None
        
    def load_strategy_data(self):
        """Load strategy trade and daily performance data"""
        try:
            # Load trades data
            self.trades_data = pd.read_csv(self.trades_file)
            self.trades_data['entry_date'] = pd.to_datetime(self.trades_data['entry_date'])
            self.trades_data['exit_date'] = pd.to_datetime(self.trades_data['exit_date'])
            
            # Load daily data
            self.daily_data = pd.read_csv(self.daily_file)
            self.daily_data['date'] = pd.to_datetime(self.daily_data['date'])
            
            print(f"✅ Loaded {len(self.trades_data)} trades and {len(self.daily_data)} daily records")
            print(f"   Trade date range: {self.trades_data['entry_date'].min()} to {self.trades_data['entry_date'].max()}")
            
        except Exception as e:
            print(f"❌ Error loading strategy data: {e}")
            
    def create_synthetic_market_data(self):
        """Create synthetic VIX and UTY data for analysis (since actual data not available)"""
        # Get date range from strategy data
        start_date = self.trades_data['entry_date'].min()
        end_date = self.trades_data['entry_date'].max()
        
        # Create date range
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # Create synthetic market data based on realistic patterns
        np.random.seed(42)  # For reproducible results
        
        market_data = []
        base_vix = 20.0
        base_uty = 100.0
        
        for i, date in enumerate(date_range):
            # VIX simulation with volatility clustering
            vix_change = np.random.normal(0, 0.8) + 0.1 * np.sin(i/30)  # Monthly cycle
            base_vix = max(10, min(50, base_vix + vix_change))
            
            # UTY simulation with sector rotation patterns
            uty_change = np.random.normal(0, 1.2) + 0.05 * np.cos(i/60)  # Bi-monthly cycle
            base_uty = max(80, min(120, base_uty + uty_change))
            
            # Add market stress periods (simulate 2023 banking crisis, 2024 election volatility)
            if date.year == 2023 and date.month in [3, 4]:  # Banking crisis
                base_vix *= 1.5
                base_uty *= 0.95
            elif date.year == 2024 and date.month in [10, 11]:  # Election volatility
                base_vix *= 1.3
                base_uty *= 0.98
                
            market_data.append({
                'date': date,
                'vix': base_vix,
                'uty': base_uty,
                'spx': 4000 + 200 * np.sin(i/100) + np.random.normal(0, 50)  # SPX simulation
            })
        
        self.market_data = pd.DataFrame(market_data)
        print(f"✅ Created synthetic market data for {len(self.market_data)} days")
        print(f"   VIX range: {self.market_data['vix'].min():.1f} - {self.market_data['vix'].max():.1f}")
        print(f"   UTY range: {self.market_data['uty'].min():.1f} - {self.market_data['uty'].max():.1f}")
        
    def analyze_vix_correlations(self) -> Dict:
        """Analyze correlations between strategy performance and VIX levels"""
        # Merge trades with market data
        trades_with_market = self.trades_data.merge(
            self.market_data, 
            left_on='entry_date', 
            right_on='date', 
            how='left'
        )
        
        # Define VIX regimes
        trades_with_market['vix_regime'] = pd.cut(
            trades_with_market['vix'],
            bins=[0, 15, 25, 35, 100],
            labels=['Low_VIX', 'Normal_VIX', 'High_VIX', 'Extreme_VIX']
        )
        
        # Calculate performance by VIX regime
        vix_analysis = {}
        
        for regime in ['Low_VIX', 'Normal_VIX', 'High_VIX', 'Extreme_VIX']:
            regime_trades = trades_with_market[trades_with_market['vix_regime'] == regime]
            
            if len(regime_trades) > 0:
                vix_analysis[regime] = {
                    'trade_count': len(regime_trades),
                    'win_rate': (regime_trades['pnl'] > 0).mean() * 100,
                    'avg_pnl': regime_trades['pnl'].mean(),
                    'total_pnl': regime_trades['pnl'].sum(),
                    'avg_vix': regime_trades['vix'].mean(),
                    'best_trade': regime_trades['pnl'].max(),
                    'worst_trade': regime_trades['pnl'].min()
                }
        
        return vix_analysis
        
    def analyze_uty_correlations(self) -> Dict:
        """Analyze correlations between strategy performance and UTY sector rotation"""
        # Merge trades with market data
        trades_with_market = self.trades_data.merge(
            self.market_data, 
            left_on='entry_date', 
            right_on='date', 
            how='left'
        )
        
        # Calculate UTY relative performance vs SPX
        trades_with_market['uty_relative'] = trades_with_market['uty'] / trades_with_market['spx'] * 100
        
        # Define UTY regimes (relative to SPX)
        uty_median = trades_with_market['uty_relative'].median()
        trades_with_market['uty_regime'] = np.where(
            trades_with_market['uty_relative'] > uty_median,
            'UTY_Outperform',
            'UTY_Underperform'
        )
        
        # Calculate performance by UTY regime
        uty_analysis = {}
        
        for regime in ['UTY_Outperform', 'UTY_Underperform']:
            regime_trades = trades_with_market[trades_with_market['uty_regime'] == regime]
            
            if len(regime_trades) > 0:
                uty_analysis[regime] = {
                    'trade_count': len(regime_trades),
                    'win_rate': (regime_trades['pnl'] > 0).mean() * 100,
                    'avg_pnl': regime_trades['pnl'].mean(),
                    'total_pnl': regime_trades['pnl'].sum(),
                    'avg_uty_relative': regime_trades['uty_relative'].mean(),
                    'best_trade': regime_trades['pnl'].max(),
                    'worst_trade': regime_trades['pnl'].min()
                }
        
        return uty_analysis
        
    def analyze_signal_quality_by_regime(self) -> Dict:
        """Analyze signal accuracy across different market regimes"""
        # Merge trades with market data
        trades_with_market = self.trades_data.merge(
            self.market_data, 
            left_on='entry_date', 
            right_on='date', 
            how='left'
        )
        
        # Define combined market regimes
        conditions = [
            (trades_with_market['vix'] < 20) & (trades_with_market['uty_relative'] > trades_with_market['uty_relative'].median()),
            (trades_with_market['vix'] < 20) & (trades_with_market['uty_relative'] <= trades_with_market['uty_relative'].median()),
            (trades_with_market['vix'] >= 20) & (trades_with_market['vix'] < 30),
            (trades_with_market['vix'] >= 30)
        ]
        
        choices = ['Low_Vol_UTY_Strong', 'Low_Vol_UTY_Weak', 'Normal_Vol', 'High_Vol']
        trades_with_market['market_regime'] = np.select(conditions, choices, default='Unknown')
        
        # Analyze signal quality by regime
        regime_analysis = {}
        
        for regime in choices:
            regime_trades = trades_with_market[trades_with_market['market_regime'] == regime]
            
            if len(regime_trades) > 0:
                # Analyze by signal type
                call_trades = regime_trades[regime_trades['option_type'] == 'call']
                put_trades = regime_trades[regime_trades['option_type'] == 'put']
                
                regime_analysis[regime] = {
                    'total_trades': len(regime_trades),
                    'overall_win_rate': (regime_trades['pnl'] > 0).mean() * 100,
                    'call_trades': len(call_trades),
                    'call_win_rate': (call_trades['pnl'] > 0).mean() * 100 if len(call_trades) > 0 else 0,
                    'put_trades': len(put_trades),
                    'put_win_rate': (put_trades['pnl'] > 0).mean() * 100 if len(put_trades) > 0 else 0,
                    'avg_pnl': regime_trades['pnl'].mean(),
                    'avg_vix': regime_trades['vix'].mean()
                }
        
        return regime_analysis

    def generate_regime_visualizations(self):
        """Generate comprehensive visualizations of regime analysis"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('JPM Collar Strategy: Market Regime Performance Analysis', fontsize=16, fontweight='bold')

        # VIX correlation analysis
        vix_analysis = self.analyze_vix_correlations()

        # Plot 1: Win Rate by VIX Regime
        regimes = list(vix_analysis.keys())
        win_rates = [vix_analysis[r]['win_rate'] for r in regimes]
        trade_counts = [vix_analysis[r]['trade_count'] for r in regimes]

        bars1 = axes[0,0].bar(regimes, win_rates, color=['green', 'blue', 'orange', 'red'])
        axes[0,0].set_title('Win Rate by VIX Regime')
        axes[0,0].set_ylabel('Win Rate (%)')
        axes[0,0].axhline(y=27.3, color='black', linestyle='--', label='Overall Win Rate')
        axes[0,0].legend()

        # Add trade count labels
        for bar, count in zip(bars1, trade_counts):
            axes[0,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                          f'n={count}', ha='center', va='bottom')

        # Plot 2: Average P&L by VIX Regime
        avg_pnls = [vix_analysis[r]['avg_pnl'] for r in regimes]
        bars2 = axes[0,1].bar(regimes, avg_pnls, color=['green', 'blue', 'orange', 'red'])
        axes[0,1].set_title('Average P&L by VIX Regime')
        axes[0,1].set_ylabel('Average P&L ($)')
        axes[0,1].axhline(y=-279, color='black', linestyle='--', label='Overall Avg P&L')
        axes[0,1].legend()

        # Plot 3: UTY Correlation Analysis
        uty_analysis = self.analyze_uty_correlations()
        uty_regimes = list(uty_analysis.keys())
        uty_win_rates = [uty_analysis[r]['win_rate'] for r in uty_regimes]
        uty_trade_counts = [uty_analysis[r]['trade_count'] for r in uty_regimes]

        bars3 = axes[0,2].bar(uty_regimes, uty_win_rates, color=['darkgreen', 'darkred'])
        axes[0,2].set_title('Win Rate by UTY Performance')
        axes[0,2].set_ylabel('Win Rate (%)')
        axes[0,2].axhline(y=27.3, color='black', linestyle='--', label='Overall Win Rate')
        axes[0,2].legend()

        for bar, count in zip(bars3, uty_trade_counts):
            axes[0,2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                          f'n={count}', ha='center', va='bottom')

        # Plot 4: Signal Quality by Market Regime
        signal_analysis = self.analyze_signal_quality_by_regime()
        signal_regimes = list(signal_analysis.keys())
        call_win_rates = [signal_analysis[r]['call_win_rate'] for r in signal_regimes]
        put_win_rates = [signal_analysis[r]['put_win_rate'] for r in signal_regimes]

        x = np.arange(len(signal_regimes))
        width = 0.35

        axes[1,0].bar(x - width/2, call_win_rates, width, label='Call Win Rate', color='lightblue')
        axes[1,0].bar(x + width/2, put_win_rates, width, label='Put Win Rate', color='lightcoral')
        axes[1,0].set_title('Call vs Put Win Rates by Market Regime')
        axes[1,0].set_ylabel('Win Rate (%)')
        axes[1,0].set_xticks(x)
        axes[1,0].set_xticklabels(signal_regimes, rotation=45)
        axes[1,0].legend()
        axes[1,0].axhline(y=27.3, color='black', linestyle='--', alpha=0.7)

        # Plot 5: Trade Distribution by VIX Level
        trades_with_market = self.trades_data.merge(
            self.market_data, left_on='entry_date', right_on='date', how='left'
        )

        winning_trades = trades_with_market[trades_with_market['pnl'] > 0]
        losing_trades = trades_with_market[trades_with_market['pnl'] <= 0]

        axes[1,1].scatter(winning_trades['vix'], winning_trades['pnl'],
                         color='green', alpha=0.6, label='Winning Trades')
        axes[1,1].scatter(losing_trades['vix'], losing_trades['pnl'],
                         color='red', alpha=0.6, label='Losing Trades')
        axes[1,1].set_title('Trade P&L vs VIX Level')
        axes[1,1].set_xlabel('VIX Level')
        axes[1,1].set_ylabel('Trade P&L ($)')
        axes[1,1].legend()
        axes[1,1].axvline(x=25, color='orange', linestyle='--', alpha=0.7, label='High VIX Threshold')

        # Plot 6: Cumulative P&L by Market Regime
        regime_cumulative = {}
        for regime in signal_regimes:
            regime_trades = trades_with_market[trades_with_market['market_regime'] == regime]
            if len(regime_trades) > 0:
                regime_cumulative[regime] = regime_trades['pnl'].cumsum().values

        for regime, cumsum in regime_cumulative.items():
            axes[1,2].plot(range(len(cumsum)), cumsum, label=regime, linewidth=2)

        axes[1,2].set_title('Cumulative P&L by Market Regime')
        axes[1,2].set_xlabel('Trade Number')
        axes[1,2].set_ylabel('Cumulative P&L ($)')
        axes[1,2].legend()
        axes[1,2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('reports/market_regime_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        return fig
