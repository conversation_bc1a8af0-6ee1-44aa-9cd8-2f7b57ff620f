#!/usr/bin/env python3
"""
Fast Cluster Strategy - Optimized for Speed
Simplified version without intensive Greeks calculations
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from src.dataloader import OptionsDataLoader
from src.config import Config
from src.emini_overnight_analyzer import EminiOvernightAnalyzer
from src.constants import (
    START_YEAR, BACKTEST_END_YEAR, STRIKE_MULTIPLE, TARGET_EXPIRY_DAYS,
    MAX_HOLD_DAYS, DRIFT_THRESHOLD, WALL_STRENGTH_THRESHOLD, VOLUME_PERCENTILE,
    GAMMA_MULTIPLIER, DELTA_MULTIPLIER, BAND_WIDTH, INITIAL_CAPITAL,
    SPX_MULTIPLIER, MAX_CONTRACTS, MIN_CONTRACTS, MAX_RISK_PER_TRADE
)

class FastClusterStrategy:
    """
    Fast cluster strategy optimized for speed
    Uses simplified Greeks and wall detection
    """
    
    def __init__(self, start_date: str, end_date: str):
        """Initialize the fast cluster strategy"""
        self.start_date = pd.to_datetime(start_date)
        self.end_date = pd.to_datetime(end_date)
        
        # Strategy parameters
        self.params = {
            'drift_threshold': DRIFT_THRESHOLD,
            'wall_strength_threshold': WALL_STRENGTH_THRESHOLD,
            'volume_percentile': VOLUME_PERCENTILE,
            'gamma_multiplier': GAMMA_MULTIPLIER,
            'delta_multiplier': DELTA_MULTIPLIER,
            'band_width': BAND_WIDTH,
            'base_position_size': MIN_CONTRACTS,
            'max_position_size': MAX_CONTRACTS,
            'max_hold_days': MAX_HOLD_DAYS,
            'target_expiry_days': TARGET_EXPIRY_DAYS,
            'strike_multiple': STRIKE_MULTIPLE
        }
        
        # Initialize E-mini analyzer
        self.emini_analyzer = self._init_emini_analyzer()
        
    def _init_emini_analyzer(self):
        """Initialize E-mini analyzer with synchronized dates"""
        try:
            analyzer = EminiOvernightAnalyzer()
            analyzer.initialize_data(
                start_date=self.start_date.strftime('%Y-%m-%d'),
                end_date=self.end_date.strftime('%Y-%m-%d')
            )
            return analyzer
        except Exception as e:
            print(f"⚠️ Warning: Could not initialize E-mini analyzer: {e}")
            return None
    
    def detect_option_walls_fast(self, options_data: pd.DataFrame, current_price: float) -> dict:
        """
        Fast wall detection using simplified metrics
        """
        if options_data.empty:
            return self._empty_wall_analysis()
        
        # Filter for significant options only
        significant_options = options_data[
            (options_data['volume'] > 0) | 
            (options_data['open_interest'] > 50)
        ].copy()
        
        if significant_options.empty:
            return self._empty_wall_analysis()
        
        # Calculate simple wall strength using volume and OI
        significant_options['wall_strength'] = (
            significant_options['volume'] * 1.0 + 
            significant_options['open_interest'] * 0.1
        )
        
        # Separate calls and puts
        calls = significant_options[significant_options['option_type'].str.lower() == 'c']
        puts = significant_options[significant_options['option_type'].str.lower() == 'p']
        
        # Calculate wall strengths
        call_wall_strength = calls['wall_strength'].sum() if not calls.empty else 0
        put_wall_strength = puts['wall_strength'].sum() if not puts.empty else 0
        
        # Count walls
        call_wall_count = len(calls.groupby('strike')) if not calls.empty else 0
        put_wall_count = len(puts.groupby('strike')) if not puts.empty else 0
        
        # Determine dominant wall
        if call_wall_strength > put_wall_strength:
            dominant_wall = 'CALL_WALL'
        elif put_wall_strength > call_wall_strength:
            dominant_wall = 'PUT_WALL'
        else:
            dominant_wall = 'NEUTRAL'
        
        return {
            'call_wall_strength': call_wall_strength,
            'put_wall_strength': put_wall_strength,
            'call_wall_count': call_wall_count,
            'put_wall_count': put_wall_count,
            'dominant_wall': dominant_wall,
            'delta_imbalance': call_wall_strength - put_wall_strength
        }
    
    def _empty_wall_analysis(self) -> dict:
        """Return empty wall analysis"""
        return {
            'call_wall_strength': 0.0,
            'put_wall_strength': 0.0,
            'call_wall_count': 0,
            'put_wall_count': 0,
            'dominant_wall': 'NEUTRAL',
            'delta_imbalance': 0.0
        }
    
    def generate_signal_fast(self, wall_data: dict, drift: float, current_price: float) -> dict:
        """
        Generate trading signal using fast logic
        """
        signal_type = 'NEUTRAL'
        signal_strength = 0.0
        rationale = "No clear signal"
        position_size = 0
        
        # Get wall dominance
        call_strength = wall_data['call_wall_strength']
        put_strength = wall_data['put_wall_strength']
        
        # Minimum wall strength required
        min_wall_strength = WALL_STRENGTH_THRESHOLD
        
        # Skip if walls are too weak
        if max(call_strength, put_strength) < min_wall_strength:
            return {
                'signal_type': signal_type,
                'signal_strength': signal_strength,
                'rationale': f"Weak walls: C={call_strength:.1f}, P={put_strength:.1f}",
                'position_size': position_size
            }
        
        # Signal generation logic
        if put_strength > call_strength:
            # Put wall dominance = Support level
            if drift >= 0:
                signal_type = 'BULLISH'
                signal_strength = min(put_strength / 10000.0, 1.0)
                rationale = f"BULLISH: Put walls support ({put_strength:.1f}) + upward bias ({drift*100:.2f}%)"
            elif drift < -self.params['drift_threshold']:
                signal_type = 'BEARISH'
                signal_strength = min((put_strength + abs(drift)*100000) / 20000.0, 1.0)
                rationale = f"BEARISH: Breaking put support + strong down drift ({drift*100:.2f}%)"
        else:
            # Call wall dominance = Resistance level
            if drift <= 0:
                signal_type = 'BEARISH'
                signal_strength = min(call_strength / 10000.0, 1.0)
                rationale = f"BEARISH: Call walls resistance ({call_strength:.1f}) + downward bias ({drift*100:.2f}%)"
            elif drift > self.params['drift_threshold']:
                signal_type = 'BULLISH'
                signal_strength = min((call_strength + abs(drift)*100000) / 20000.0, 1.0)
                rationale = f"BULLISH: Breaking call resistance + strong up drift ({drift*100:.2f}%)"
        
        # Calculate position size based on signal strength
        if signal_strength > 0:
            position_size = max(
                self.params['base_position_size'],
                int(signal_strength * self.params['max_position_size'])
            )
        
        return {
            'signal_type': signal_type,
            'signal_strength': signal_strength,
            'rationale': rationale,
            'position_size': position_size
        }
    
    def find_option_strike_fast(self, signal_type: str, current_price: float, 
                               signal_date: pd.Timestamp, options_data: pd.DataFrame) -> dict:
        """
        Fast option strike finding
        """
        if signal_type == 'NEUTRAL':
            return None
        
        # Determine target strike based on signal type
        if signal_type == 'BULLISH':
            target_strike = ((int(current_price) // STRIKE_MULTIPLE) + 1) * STRIKE_MULTIPLE
            option_type = 'c'
        else:
            target_strike = (int(current_price) // STRIKE_MULTIPLE) * STRIKE_MULTIPLE
            option_type = 'p'
        
        # Target expiry date
        target_expiry = signal_date + pd.Timedelta(days=TARGET_EXPIRY_DAYS)
        
        # Find options near target strike and expiry
        target_options = options_data[
            (options_data['strike'] == target_strike) &
            (options_data['option_type'].str.lower() == option_type)
        ].copy()
        
        if target_options.empty:
            return None
        
        # Find expiry closest to target
        target_options['expiry_date'] = pd.to_datetime(target_options['expiration'])
        target_options['days_to_expiry'] = (target_options['expiry_date'] - signal_date).dt.days
        target_options = target_options[target_options['days_to_expiry'] > 0]
        
        if target_options.empty:
            return None
        
        # Get closest expiry to target
        closest_expiry_days = target_options['days_to_expiry'].iloc[
            (target_options['days_to_expiry'] - TARGET_EXPIRY_DAYS).abs().argsort()[:1]
        ].iloc[0]
        
        best_option = target_options[
            target_options['days_to_expiry'] == closest_expiry_days
        ].iloc[0]
        
        # Get entry price
        entry_price = best_option.get('Last Trade Price', 0)
        if entry_price <= 0:
            bid = best_option.get('bid', 0)
            ask = best_option.get('ask', 0)
            if bid > 0 and ask > 0:
                entry_price = (bid + ask) / 2
            else:
                entry_price = ask if ask > 0 else bid
        
        if entry_price <= 0:
            return None
        
        return {
            'strike': best_option['strike'],
            'expiry': best_option['expiration'],
            'option_type': best_option['option_type'],
            'entry_price': entry_price,
            'days_to_expiry': closest_expiry_days
        }


def run_fast_backtest(start_year: int = START_YEAR):
    """Run the fast cluster strategy backtest"""
    
    print("🚀 FAST CLUSTER STRATEGY BACKTEST")
    print("=" * 60)
    print("OPTIMIZED FOR SPEED:")
    print("  📊 Simplified wall detection using volume/OI")
    print("  🌙 Overnight E-mini drift analysis")
    print("  🔴 Call walls + downward drift → BUY puts")
    print("  🟢 Put walls + upward drift → BUY calls")
    print("  📏 Position size: 1-3 contracts based on signal strength")
    print(f"  ⏰ Options trading: Enter next day open, exit day {MAX_HOLD_DAYS} close, {TARGET_EXPIRY_DAYS} day expiry")
    print(f"  📅 Data from: {start_year} onwards")
    print("=" * 60)
    
    # Load data
    config = Config()
    loader = OptionsDataLoader(config)
    
    print(f"🔍 SEARCHING FOR SPX OPTIONS DATA (from {start_year})")
    print("=" * 50)
    
    options_data = loader.load_data_from_year(start_year)
    
    if options_data.empty:
        print("❌ No options data found!")
        return None
    
    # Get date range
    start_date = f"{start_year}-01-01"
    end_date = options_data['date'].max().strftime('%Y-%m-%d')
    
    print(f"📅 Backtest Period: {start_date} to {end_date}")
    
    # Initialize strategy
    strategy = FastClusterStrategy(start_date, end_date)
    
    # Run backtest
    print(f"\n💰 Starting Capital: ${INITIAL_CAPITAL:,.2f}")
    print("🎯 Strategy: Fast Cluster Strategy")
    
    # Get trading days
    trading_days = pd.date_range(start=start_date, end=end_date, freq='D')
    trading_days = [d for d in trading_days if d.weekday() < 5]  # Weekdays only
    
    # Initialize tracking
    current_capital = INITIAL_CAPITAL
    positions = []
    trades = []
    daily_analysis = []
    
    print(f"📈 Total trading days: {len(trading_days)}")
    
    for i, date in enumerate(trading_days):
        if i % 50 == 0:
            print(f"  Processing {i+1}/{len(trading_days)}: {date.strftime('%Y-%m-%d')}")
        
        # Get options data for this day
        day_options = options_data[options_data['date'] == date]
        
        if day_options.empty:
            continue
        
        # Get current price
        current_price = day_options['underlying_close'].iloc[0]
        
        # Get overnight drift
        overnight_drift = 0.0
        if strategy.emini_analyzer:
            try:
                overnight_drift = strategy.emini_analyzer.get_overnight_drift(date.strftime('%Y-%m-%d'))
            except:
                pass
        
        # Detect walls
        wall_data = strategy.detect_option_walls_fast(day_options, current_price)
        
        # Generate signal
        signal = strategy.generate_signal_fast(wall_data, overnight_drift, current_price)
        
        # Process existing positions (exit logic)
        for position in positions[:]:
            days_held = (date - position['entry_date']).days
            
            if days_held >= MAX_HOLD_DAYS:
                # Exit position
                exit_price = strategy.find_option_strike_fast(
                    position['signal_type'], current_price, date, day_options
                )
                
                if exit_price:
                    exit_price_value = exit_price['entry_price']
                else:
                    exit_price_value = position['entry_price'] * 0.5  # Assume 50% loss if can't find
                
                # Calculate P&L
                pnl = (exit_price_value - position['entry_price']) * SPX_MULTIPLIER * position['position_size']
                current_capital += pnl
                
                # Record trade
                trade = {
                    'entry_date': position['entry_date'],
                    'exit_date': date,
                    'signal_type': position['signal_type'],
                    'strike': position['strike'],
                    'expiry': position['expiry'],
                    'option_type': position['option_type'],
                    'entry_price': position['entry_price'],
                    'exit_price': exit_price_value,
                    'position_size': position['position_size'],
                    'pnl': pnl,
                    'exit_reason': f"MAX_HOLD_CLOSE ({days_held} days)",
                    'days_held': days_held
                }
                trades.append(trade)
                
                # Remove position
                positions.remove(position)
                
                if i < 5:  # Debug for first few days
                    result = "WIN" if pnl > 0 else "LOSS"
                    print(f"    🎉 {result}: BUY {position['option_type']} ${position['strike']} = ${pnl:,.0f} ({trade['exit_reason']})")
        
        # Entry logic
        if signal['signal_type'] != 'NEUTRAL' and len(positions) < 3:  # Max 3 positions
            option_details = strategy.find_option_strike_fast(
                signal['signal_type'], current_price, date, day_options
            )
            
            if option_details:
                # Calculate position size with risk management
                contracts = signal['position_size']
                max_risk = current_capital * MAX_RISK_PER_TRADE
                max_contracts_by_risk = int(max_risk / (100 * option_details['entry_price']))
                contracts = min(contracts, max(1, max_contracts_by_risk))
                
                # Create new position
                new_position = {
                    'entry_date': date + pd.Timedelta(days=1),  # Enter next day
                    'signal_date': date,
                    'entry_price': option_details['entry_price'],
                    'strike': option_details['strike'],
                    'expiry': option_details['expiry'],
                    'option_type': option_details['option_type'],
                    'signal_type': signal['signal_type'],
                    'position_size': contracts,
                    'signal_strength': signal['signal_strength'],
                    'rationale': signal['rationale']
                }
                
                positions.append(new_position)
                
                if i < 5:  # Debug for first few days
                    print(f"    📈 ENTRY: BUY {option_details['option_type']} ${option_details['strike']} at ${option_details['entry_price']}, {contracts} contracts")
        
        # Store daily analysis
        daily_analysis.append({
            'date': date,
            'current_price': current_price,
            'overnight_drift': overnight_drift,
            'call_wall_strength': wall_data['call_wall_strength'],
            'put_wall_strength': wall_data['put_wall_strength'],
            'signal_type': signal['signal_type'],
            'signal_strength': signal['signal_strength'],
            'active_positions': len(positions),
            'current_capital': current_capital
        })
    
    # Calculate results
    if trades:
        trades_df = pd.DataFrame(trades)
        winning_trades = trades_df[trades_df['pnl'] > 0]
        losing_trades = trades_df[trades_df['pnl'] <= 0]
        
        total_trades = len(trades_df)
        win_rate = len(winning_trades) / total_trades * 100
        total_pnl = trades_df['pnl'].sum()
        total_return = total_pnl / INITIAL_CAPITAL * 100
        
        avg_win = winning_trades['pnl'].mean() if not winning_trades.empty else 0
        avg_loss = losing_trades['pnl'].mean() if not losing_trades.empty else 0
        
        profit_factor = abs(winning_trades['pnl'].sum() / losing_trades['pnl'].sum()) if not losing_trades.empty and losing_trades['pnl'].sum() != 0 else float('inf')
        
        print(f"\n📊 FAST CLUSTER STRATEGY RESULTS")
        print("=" * 60)
        print(f"Total Trades: {total_trades}")
        print(f"Winning Trades: {len(winning_trades)}")
        print(f"Win Rate: {win_rate:.1f}%")
        print(f"Total P&L: ${total_pnl:,.2f}")
        print(f"Total Return: {total_return:.1f}%")
        print(f"Final Capital: ${current_capital:,.2f}")
        print(f"Average Win: ${avg_win:,.0f}")
        print(f"Average Loss: ${avg_loss:,.0f}")
        print(f"Profit Factor: {profit_factor:.2f}")
        
        return {
            'trades': trades_df,
            'daily_analysis': pd.DataFrame(daily_analysis),
            'total_return': total_return,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'profit_factor': profit_factor
        }
    
    else:
        print("❌ No trades executed!")
        return None


if __name__ == "__main__":
    results = run_fast_backtest(START_YEAR)
