"""
Drawdown Analysis: Investigate trades 30-60 for signal inversion patterns
Analyze market regime that may cause signal failures
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class DrawdownAnalyzer:
    """
    Analyze drawdown periods and identify signal inversion patterns
    """
    
    def __init__(self):
        """Initialize drawdown analyzer"""
        
        self.trades_data = None
        self.drawdown_period = None
        self.regime_analysis = {}
        
        print("🔍 Drawdown Analyzer initialized")
        print("🎯 Focus: Trades 30-60 signal inversion investigation")
    
    def load_and_analyze_trades(self):
        """Load trades data and perform comprehensive analysis"""
        
        print("📊 Loading trades data...")
        
        try:
            # Load trades data
            self.trades_data = pd.read_csv('reports/pure_vix_trades.csv')
            self.trades_data['entry_date'] = pd.to_datetime(self.trades_data['entry_date'])
            self.trades_data['exit_date'] = pd.to_datetime(self.trades_data['exit_date'])
            
            # Add trade number
            self.trades_data['trade_number'] = range(1, len(self.trades_data) + 1)
            
            # Calculate cumulative P&L
            self.trades_data['cumulative_pnl'] = self.trades_data['trade_pnl'].cumsum()
            
            print(f"✅ Loaded {len(self.trades_data)} trades")
            print(f"📅 Date range: {self.trades_data['entry_date'].min()} to {self.trades_data['entry_date'].max()}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading trades data: {e}")
            return False
    
    def identify_drawdown_periods(self):
        """Identify significant drawdown periods"""
        
        print("📉 Identifying drawdown periods...")
        
        # Calculate running maximum
        self.trades_data['running_max'] = self.trades_data['cumulative_pnl'].expanding().max()
        
        # Calculate drawdown from peak
        self.trades_data['drawdown'] = self.trades_data['cumulative_pnl'] - self.trades_data['running_max']
        
        # Calculate drawdown percentage
        self.trades_data['drawdown_pct'] = (self.trades_data['drawdown'] / 
                                           (self.trades_data['running_max'] + 100000)) * 100
        
        # Find significant drawdown periods (>5% or >$5000)
        significant_drawdowns = self.trades_data[
            (self.trades_data['drawdown'] < -5000) | 
            (self.trades_data['drawdown_pct'] < -5)
        ]
        
        print(f"📊 Drawdown Analysis:")
        print(f"   Max Drawdown: ${self.trades_data['drawdown'].min():,.0f}")
        print(f"   Max Drawdown %: {self.trades_data['drawdown_pct'].min():.2f}%")
        print(f"   Significant Drawdown Periods: {len(significant_drawdowns)}")
        
        return significant_drawdowns
    
    def analyze_trades_30_60(self):
        """Analyze specific trades 30-60 for signal inversion"""
        
        print("🔍 Analyzing trades 30-60 for signal inversion...")
        
        # Extract trades 30-60
        target_trades = self.trades_data[
            (self.trades_data['trade_number'] >= 30) & 
            (self.trades_data['trade_number'] <= 60)
        ].copy()
        
        print(f"📊 Analyzing {len(target_trades)} trades (30-60)")
        print(f"📅 Period: {target_trades['entry_date'].min()} to {target_trades['entry_date'].max()}")
        
        # Calculate performance metrics for this period
        total_pnl = target_trades['trade_pnl'].sum()
        win_rate = (target_trades['trade_pnl'] > 0).sum() / len(target_trades) * 100
        avg_pnl = target_trades['trade_pnl'].mean()
        
        print(f"📈 Period Performance:")
        print(f"   Total P&L: ${total_pnl:,.0f}")
        print(f"   Win Rate: {win_rate:.1f}%")
        print(f"   Average P&L: ${avg_pnl:.0f}")
        
        # Analyze market regime during this period
        self._analyze_market_regime(target_trades)
        
        # Analyze signal patterns
        self._analyze_signal_patterns(target_trades)
        
        # Analyze VIX behavior
        self._analyze_vix_behavior(target_trades)
        
        return target_trades
    
    def _analyze_market_regime(self, trades):
        """Analyze market regime during drawdown period"""
        
        print("\n🌍 Market Regime Analysis:")
        
        # VIX regime analysis
        vix_stats = {
            'avg_vix': trades['vix'].mean(),
            'min_vix': trades['vix'].min(),
            'max_vix': trades['vix'].max(),
            'vix_volatility': trades['vix'].std(),
            'avg_vix_momentum': trades['vix_momentum'].mean(),
            'vix_momentum_volatility': trades['vix_momentum'].std()
        }
        
        print(f"📊 VIX Statistics:")
        print(f"   Average VIX: {vix_stats['avg_vix']:.2f}")
        print(f"   VIX Range: {vix_stats['min_vix']:.2f} - {vix_stats['max_vix']:.2f}")
        print(f"   VIX Volatility: {vix_stats['vix_volatility']:.2f}")
        print(f"   Average VIX Momentum: {vix_stats['avg_vix_momentum']:.2f}")
        print(f"   VIX Momentum Volatility: {vix_stats['vix_momentum_volatility']:.2f}")
        
        # Compare to overall strategy
        overall_vix = self.trades_data['vix'].mean()
        overall_momentum = self.trades_data['vix_momentum'].mean()
        
        print(f"\n📈 Comparison to Overall Strategy:")
        print(f"   VIX Difference: {vix_stats['avg_vix'] - overall_vix:+.2f}")
        print(f"   Momentum Difference: {vix_stats['avg_vix_momentum'] - overall_momentum:+.2f}")
        
        # Identify regime characteristics
        if vix_stats['avg_vix'] > 20:
            regime_type = "HIGH_VOLATILITY"
        elif vix_stats['avg_vix'] < 15:
            regime_type = "LOW_VOLATILITY"
        else:
            regime_type = "NORMAL_VOLATILITY"
        
        if abs(vix_stats['avg_vix_momentum']) > 1:
            momentum_type = "HIGH_MOMENTUM"
        else:
            momentum_type = "LOW_MOMENTUM"
        
        print(f"🎯 Identified Regime: {regime_type} + {momentum_type}")
        
        self.regime_analysis = {
            'regime_type': regime_type,
            'momentum_type': momentum_type,
            'vix_stats': vix_stats
        }
    
    def _analyze_signal_patterns(self, trades):
        """Analyze signal patterns during drawdown period"""
        
        print("\n📡 Signal Pattern Analysis:")
        
        # Signal direction distribution
        signal_dist = trades['signal_direction'].value_counts()
        print(f"📊 Signal Distribution:")
        for signal, count in signal_dist.items():
            pct = count / len(trades) * 100
            print(f"   {signal}: {count} trades ({pct:.1f}%)")
        
        # Performance by signal direction
        signal_performance = trades.groupby('signal_direction').agg({
            'trade_pnl': ['count', 'mean', 'sum', lambda x: (x > 0).sum() / len(x) * 100]
        }).round(2)
        
        signal_performance.columns = ['count', 'avg_pnl', 'total_pnl', 'win_rate']
        
        print(f"\n📈 Performance by Signal Direction:")
        print(signal_performance)
        
        # Strategy type analysis
        strategy_dist = trades['strategy_type'].value_counts()
        print(f"\n🎯 Strategy Type Distribution:")
        for strategy, count in strategy_dist.items():
            pct = count / len(trades) * 100
            print(f"   {strategy}: {count} trades ({pct:.1f}%)")
        
        # Performance by strategy type
        strategy_performance = trades.groupby('strategy_type').agg({
            'trade_pnl': ['count', 'mean', 'sum', lambda x: (x > 0).sum() / len(x) * 100]
        }).round(2)
        
        strategy_performance.columns = ['count', 'avg_pnl', 'total_pnl', 'win_rate']
        
        print(f"\n📊 Performance by Strategy Type:")
        print(strategy_performance)
        
        # Identify potential signal inversion
        bullish_performance = signal_performance.loc['BULLISH', 'avg_pnl'] if 'BULLISH' in signal_performance.index else 0
        bearish_performance = signal_performance.loc['BEARISH', 'avg_pnl'] if 'BEARISH' in signal_performance.index else 0
        
        if bullish_performance < 0 and bearish_performance < 0:
            print("⚠️ SIGNAL INVERSION DETECTED: Both bullish and bearish signals underperforming")
        elif bullish_performance < bearish_performance * 0.5:
            print("⚠️ BULLISH SIGNAL WEAKNESS: Bullish signals significantly underperforming")
        elif bearish_performance < bullish_performance * 0.5:
            print("⚠️ BEARISH SIGNAL WEAKNESS: Bearish signals significantly underperforming")
    
    def _analyze_vix_behavior(self, trades):
        """Analyze VIX behavior patterns during drawdown"""
        
        print("\n📊 VIX Behavior Analysis:")
        
        # VIX trend analysis
        vix_trend = np.polyfit(range(len(trades)), trades['vix'], 1)[0]
        momentum_trend = np.polyfit(range(len(trades)), trades['vix_momentum'], 1)[0]
        
        print(f"📈 VIX Trends:")
        print(f"   VIX Trend: {vix_trend:+.3f} per trade")
        print(f"   Momentum Trend: {momentum_trend:+.3f} per trade")
        
        # VIX regime transitions
        vix_regimes = []
        for _, trade in trades.iterrows():
            vix = trade['vix']
            if vix < 15:
                regime = 'LOW_VIX'
            elif vix > 25:
                regime = 'HIGH_VIX'
            else:
                regime = 'NORMAL_VIX'
            vix_regimes.append(regime)
        
        trades['vix_regime'] = vix_regimes
        regime_transitions = trades['vix_regime'].value_counts()
        
        print(f"\n🌍 VIX Regime Distribution:")
        for regime, count in regime_transitions.items():
            pct = count / len(trades) * 100
            print(f"   {regime}: {count} trades ({pct:.1f}%)")
        
        # Performance by VIX regime
        regime_performance = trades.groupby('vix_regime').agg({
            'trade_pnl': ['count', 'mean', 'sum', lambda x: (x > 0).sum() / len(x) * 100]
        }).round(2)
        
        regime_performance.columns = ['count', 'avg_pnl', 'total_pnl', 'win_rate']
        
        print(f"\n📊 Performance by VIX Regime:")
        print(regime_performance)
        
        # Identify problematic VIX conditions
        worst_regime = regime_performance['avg_pnl'].idxmin()
        worst_performance = regime_performance.loc[worst_regime, 'avg_pnl']
        
        print(f"\n⚠️ Worst Performing VIX Regime: {worst_regime} (${worst_performance:.0f} avg P&L)")
    
    def create_drawdown_visualization(self, target_trades):
        """Create comprehensive drawdown visualization"""
        
        print("📊 Creating drawdown visualization...")
        
        fig, axes = plt.subplots(3, 2, figsize=(16, 18))
        
        # 1. Cumulative P&L with drawdown period highlighted
        axes[0, 0].plot(self.trades_data['trade_number'], self.trades_data['cumulative_pnl'], 
                       linewidth=2, color='blue', label='Cumulative P&L')
        
        # Highlight trades 30-60
        highlight_trades = self.trades_data[
            (self.trades_data['trade_number'] >= 30) & 
            (self.trades_data['trade_number'] <= 60)
        ]
        
        axes[0, 0].plot(highlight_trades['trade_number'], highlight_trades['cumulative_pnl'], 
                       linewidth=3, color='red', label='Trades 30-60')
        
        axes[0, 0].set_title('Cumulative P&L - Drawdown Period Highlighted')
        axes[0, 0].set_xlabel('Trade Number')
        axes[0, 0].set_ylabel('Cumulative P&L ($)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Individual trade P&L for period 30-60
        colors = ['green' if pnl > 0 else 'red' for pnl in target_trades['trade_pnl']]
        axes[0, 1].bar(target_trades['trade_number'], target_trades['trade_pnl'], 
                      color=colors, alpha=0.7)
        axes[0, 1].set_title('Individual Trade P&L (Trades 30-60)')
        axes[0, 1].set_xlabel('Trade Number')
        axes[0, 1].set_ylabel('Trade P&L ($)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. VIX levels during drawdown period
        axes[1, 0].plot(target_trades['trade_number'], target_trades['vix'], 
                       linewidth=2, color='purple', marker='o', markersize=4)
        axes[1, 0].axhline(y=15, color='green', linestyle='--', alpha=0.7, label='Low VIX (15)')
        axes[1, 0].axhline(y=25, color='red', linestyle='--', alpha=0.7, label='High VIX (25)')
        axes[1, 0].set_title('VIX Levels During Drawdown Period')
        axes[1, 0].set_xlabel('Trade Number')
        axes[1, 0].set_ylabel('VIX Level')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. VIX momentum during drawdown period
        axes[1, 1].plot(target_trades['trade_number'], target_trades['vix_momentum'], 
                       linewidth=2, color='orange', marker='s', markersize=4)
        axes[1, 1].axhline(y=0, color='black', linestyle='-', alpha=0.5)
        axes[1, 1].axhline(y=2, color='red', linestyle='--', alpha=0.7, label='High Momentum (+2)')
        axes[1, 1].axhline(y=-2, color='blue', linestyle='--', alpha=0.7, label='High Momentum (-2)')
        axes[1, 1].set_title('VIX Momentum During Drawdown Period')
        axes[1, 1].set_xlabel('Trade Number')
        axes[1, 1].set_ylabel('VIX Momentum')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        # 5. Signal direction performance
        signal_perf = target_trades.groupby('signal_direction')['trade_pnl'].mean()
        signal_colors = ['green' if pnl > 0 else 'red' for pnl in signal_perf.values]
        axes[2, 0].bar(signal_perf.index, signal_perf.values, color=signal_colors, alpha=0.7)
        axes[2, 0].set_title('Average P&L by Signal Direction (Trades 30-60)')
        axes[2, 0].set_ylabel('Average P&L ($)')
        axes[2, 0].grid(True, alpha=0.3)
        
        # 6. VIX regime performance
        if 'vix_regime' in target_trades.columns:
            regime_perf = target_trades.groupby('vix_regime')['trade_pnl'].mean()
            regime_colors = ['green' if pnl > 0 else 'red' for pnl in regime_perf.values]
            axes[2, 1].bar(regime_perf.index, regime_perf.values, color=regime_colors, alpha=0.7)
            axes[2, 1].set_title('Average P&L by VIX Regime (Trades 30-60)')
            axes[2, 1].set_ylabel('Average P&L ($)')
            axes[2, 1].tick_params(axis='x', rotation=45)
            axes[2, 1].grid(True, alpha=0.3)
        
        plt.suptitle('DRAWDOWN ANALYSIS: TRADES 30-60 INVESTIGATION', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # Save visualization
        plt.savefig('reports/drawdown_analysis_trades_30_60.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ Drawdown visualization saved: reports/drawdown_analysis_trades_30_60.png")
    
    def generate_recommendations(self, target_trades):
        """Generate recommendations based on drawdown analysis"""
        
        print("\n💡 RECOMMENDATIONS BASED ON DRAWDOWN ANALYSIS:")
        
        # Analyze the specific issues found
        avg_vix = target_trades['vix'].mean()
        avg_momentum = target_trades['vix_momentum'].mean()
        win_rate = (target_trades['trade_pnl'] > 0).sum() / len(target_trades) * 100
        
        recommendations = []
        
        # VIX-based recommendations
        if avg_vix > 20:
            recommendations.append("🔴 HIGH VIX PERIOD: Consider tighter VIX filtering (lower threshold)")
            recommendations.append("📉 VOLATILITY REGIME: Reduce position sizes in high VIX periods")
        
        if abs(avg_momentum) > 1.5:
            recommendations.append("⚡ HIGH VIX MOMENTUM: Add momentum decay filters")
            recommendations.append("🎯 MOMENTUM ADJUSTMENT: Consider momentum direction in signal generation")
        
        # Signal-based recommendations
        bullish_perf = target_trades[target_trades['signal_direction'] == 'BULLISH']['trade_pnl'].mean()
        bearish_perf = target_trades[target_trades['signal_direction'] == 'BEARISH']['trade_pnl'].mean()
        
        if bullish_perf < -100:
            recommendations.append("📈 BULLISH SIGNAL ISSUE: Review bullish signal generation logic")
        
        if bearish_perf < -100:
            recommendations.append("📉 BEARISH SIGNAL ISSUE: Review bearish signal generation logic")
        
        # Strategy-based recommendations
        if win_rate < 40:
            recommendations.append("🎲 LOW WIN RATE: Consider additional signal filters")
            recommendations.append("⚖️ RISK MANAGEMENT: Implement tighter stop-losses")
        
        # Market regime recommendations
        if self.regime_analysis.get('regime_type') == 'HIGH_VOLATILITY':
            recommendations.append("🌪️ HIGH VOLATILITY REGIME: Skip trades when VIX > 22 (vs current 25)")
            recommendations.append("🛡️ DEFENSIVE POSTURE: Reduce position multipliers in volatile periods")
        
        # Print recommendations
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")
        
        if not recommendations:
            print("   ✅ No specific issues identified - drawdown appears to be normal variance")
        
        return recommendations


def main():
    """Run comprehensive drawdown analysis"""
    
    print("🔍 DRAWDOWN ANALYSIS: TRADES 30-60 INVESTIGATION")
    print("=" * 70)
    print("🎯 Goal: Identify market regime causing signal inversion")
    print("📊 Focus: Analyze trades 30-60 for patterns and issues")
    print("=" * 70)
    
    # Initialize analyzer
    analyzer = DrawdownAnalyzer()
    
    # Load and analyze trades
    if not analyzer.load_and_analyze_trades():
        print("❌ Failed to load trades data")
        return
    
    # Identify drawdown periods
    significant_drawdowns = analyzer.identify_drawdown_periods()
    
    # Analyze specific trades 30-60
    target_trades = analyzer.analyze_trades_30_60()
    
    # Create visualization
    analyzer.create_drawdown_visualization(target_trades)
    
    # Generate recommendations
    recommendations = analyzer.generate_recommendations(target_trades)
    
    print(f"\n✅ DRAWDOWN ANALYSIS COMPLETED!")
    print(f"📊 Key Findings:")
    print(f"   • Analyzed {len(target_trades)} trades in period 30-60")
    print(f"   • Identified {len(recommendations)} specific recommendations")
    print(f"   • Market regime: {analyzer.regime_analysis.get('regime_type', 'Unknown')}")
    print(f"   • Momentum type: {analyzer.regime_analysis.get('momentum_type', 'Unknown')}")
    
    return analyzer, target_trades, recommendations


if __name__ == "__main__":
    results = main()
