"""
Enhanced Strike Selection Strategy
Integrates sophisticated strike selection with regime-based analysis and technical filters
Maintains 50.0% win rate while optimizing cost efficiency
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.sophisticated_strike_selection import SophisticatedStrikeSelector

class EnhancedStrikeSelectionStrategy:
    """
    Enhanced strategy combining sophisticated strike selection with existing framework
    """
    
    def __init__(self):
        """Initialize enhanced strike selection strategy"""
        self.strike_selector = SophisticatedStrikeSelector()
        
        # Strategy selection parameters
        self.STRATEGY_SELECTION_RULES = {
            'directional': {
                'vix_range': (10, 35),
                'trend_strength_min': 0.3,
                'technical_confidence_min': 0.6
            },
            'butterfly': {
                'vix_range': (12, 25),
                'trend_strength_max': 0.5,
                'neutral_market_preference': True
            },
            'neutral': {
                'vix_range': (8, 20),
                'trend_strength_max': 0.3,
                'low_volatility_preference': True
            }
        }
        
        # Cost optimization targets
        self.COST_REDUCTION_TARGET = 0.15  # 15% cost reduction target
        self.PREMIUM_EFFICIENCY_MIN = 2.0   # Minimum profit/premium ratio
        
        print("✅ Enhanced Strike Selection Strategy initialized")
        print(f"🎯 Target: Maintain 50.0% win rate with 10-20% cost reduction")
    
    def analyze_existing_trades(self, baseline_trades: pd.DataFrame, 
                              options_data: pd.DataFrame) -> Dict:
        """
        Analyze existing trades and apply sophisticated strike selection
        
        Args:
            baseline_trades: Existing high-performing trades (50% win rate)
            options_data: Full options dataset
            
        Returns:
            Dictionary with enhanced trade analysis
        """
        
        print("🔍 ANALYZING EXISTING TRADES FOR STRIKE OPTIMIZATION")
        print("=" * 60)
        
        enhanced_trades = []
        cost_savings_analysis = {
            'original_total_cost': 0,
            'optimized_total_cost': 0,
            'trades_analyzed': 0,
            'trades_optimized': 0,
            'cost_reduction_achieved': 0
        }
        
        for idx, trade in baseline_trades.iterrows():
            
            # Extract trade details
            entry_date = pd.to_datetime(trade['entry_date'])
            signal_type = trade['signal_type']
            original_strike = trade['strike']
            original_price = trade['entry_price']
            expiry = pd.to_datetime(trade['expiry'])
            
            # Get market data for the trade date
            trade_day_options = options_data[options_data['date'] == entry_date]
            if trade_day_options.empty:
                continue
            
            current_price = trade_day_options.iloc[0]['underlying_close']
            
            # Determine optimal strategy type for this trade
            strategy_type = self._determine_optimal_strategy(
                signal_type, trade.get('technical_filter_score', 0.5), 
                trade.get('vix_level', 20)
            )
            
            # Apply sophisticated strike selection
            optimized_strike = self._apply_sophisticated_selection(
                trade_day_options, strategy_type, signal_type, 
                current_price, expiry
            )
            
            if optimized_strike:
                # Calculate cost comparison
                original_cost = original_price * 100  # SPX multiplier
                optimized_cost = optimized_strike.get('mid_price', original_price) * 100
                
                cost_savings = original_cost - optimized_cost
                cost_reduction_pct = cost_savings / original_cost if original_cost > 0 else 0
                
                # Enhanced trade record
                enhanced_trade = trade.copy()
                enhanced_trade['original_strike'] = original_strike
                enhanced_trade['original_entry_price'] = original_price
                enhanced_trade['optimized_strategy_type'] = strategy_type
                enhanced_trade['optimized_strike'] = optimized_strike.get('strike', original_strike)
                enhanced_trade['optimized_entry_price'] = optimized_strike.get('mid_price', original_price)
                enhanced_trade['cost_savings'] = cost_savings
                enhanced_trade['cost_reduction_pct'] = cost_reduction_pct
                enhanced_trade['cost_efficiency_score'] = optimized_strike.get('cost_efficiency_score', 0)
                
                # Recalculate P&L with optimized pricing
                if 'exit_price' in trade and pd.notna(trade['exit_price']):
                    price_change = trade['exit_price'] - optimized_strike.get('mid_price', original_price)
                    enhanced_pnl = price_change * 100 * trade['position_size']
                    enhanced_trade['optimized_pnl'] = enhanced_pnl
                else:
                    enhanced_trade['optimized_pnl'] = trade['final_pnl']
                
                enhanced_trades.append(enhanced_trade)
                
                # Update cost savings analysis
                cost_savings_analysis['original_total_cost'] += original_cost
                cost_savings_analysis['optimized_total_cost'] += optimized_cost
                cost_savings_analysis['trades_optimized'] += 1
            
            cost_savings_analysis['trades_analyzed'] += 1
        
        # Calculate overall cost reduction
        if cost_savings_analysis['original_total_cost'] > 0:
            total_savings = (cost_savings_analysis['original_total_cost'] - 
                           cost_savings_analysis['optimized_total_cost'])
            cost_savings_analysis['cost_reduction_achieved'] = (
                total_savings / cost_savings_analysis['original_total_cost']
            )
        
        # Convert to DataFrame
        enhanced_trades_df = pd.DataFrame(enhanced_trades) if enhanced_trades else pd.DataFrame()
        
        # Performance analysis
        performance_analysis = self._analyze_enhanced_performance(
            baseline_trades, enhanced_trades_df
        )
        
        return {
            'enhanced_trades': enhanced_trades_df,
            'cost_savings_analysis': cost_savings_analysis,
            'performance_analysis': performance_analysis,
            'optimization_summary': self._generate_optimization_summary(
                cost_savings_analysis, performance_analysis
            )
        }
    
    def _determine_optimal_strategy(self, signal_type: str, technical_confidence: float,
                                  vix_level: float) -> str:
        """Determine optimal strategy type based on market conditions"""
        
        # Directional strategy conditions
        directional_rules = self.STRATEGY_SELECTION_RULES['directional']
        if (directional_rules['vix_range'][0] <= vix_level <= directional_rules['vix_range'][1] and
            technical_confidence >= directional_rules['technical_confidence_min'] and
            signal_type in ['BULLISH', 'BEARISH']):
            return 'directional'
        
        # Butterfly strategy conditions  
        butterfly_rules = self.STRATEGY_SELECTION_RULES['butterfly']
        if (butterfly_rules['vix_range'][0] <= vix_level <= butterfly_rules['vix_range'][1] and
            technical_confidence <= 0.5):  # Moderate confidence suggests range-bound
            return 'butterfly'
        
        # Neutral strategy conditions
        neutral_rules = self.STRATEGY_SELECTION_RULES['neutral']
        if (neutral_rules['vix_range'][0] <= vix_level <= neutral_rules['vix_range'][1] and
            technical_confidence <= neutral_rules['trend_strength_max']):
            return 'neutral'
        
        # Default to directional if no clear preference
        return 'directional'
    
    def _apply_sophisticated_selection(self, options_data: pd.DataFrame, strategy_type: str,
                                     signal_type: str, current_price: float, 
                                     expiry: datetime) -> Optional[Dict]:
        """Apply sophisticated strike selection based on strategy type"""
        
        if strategy_type == 'directional':
            return self.strike_selector.select_directional_strike(
                options_data, signal_type, current_price, expiry, cost_optimization=True
            )
        elif strategy_type == 'butterfly':
            # Choose delta target based on signal strength
            if signal_type == 'BULLISH':
                delta_target = 'moderate'  # 40-delta for moderate bullish bias
            elif signal_type == 'BEARISH':
                delta_target = 'conservative'  # 20-delta for conservative positioning
            else:
                delta_target = 'moderate'
            
            return self.strike_selector.select_butterfly_strike(
                options_data, delta_target, current_price, expiry
            )
        elif strategy_type == 'neutral':
            vix_level = 20  # Default VIX level - would be extracted from market data
            return self.strike_selector.select_neutral_strategy(
                options_data, vix_level, current_price, expiry
            )
        
        return None
    
    def _analyze_enhanced_performance(self, baseline_trades: pd.DataFrame,
                                    enhanced_trades: pd.DataFrame) -> Dict:
        """Analyze performance improvements from enhanced strike selection"""
        
        if enhanced_trades.empty:
            return {
                'baseline_performance': self._calculate_performance_metrics(baseline_trades),
                'enhanced_performance': {},
                'improvement_metrics': {}
            }
        
        baseline_perf = self._calculate_performance_metrics(baseline_trades)
        
        # Calculate enhanced performance using optimized P&L
        enhanced_perf = self._calculate_performance_metrics(
            enhanced_trades, pnl_column='optimized_pnl'
        )
        
        # Calculate improvement metrics
        improvement_metrics = {
            'win_rate_change': enhanced_perf['win_rate'] - baseline_perf['win_rate'],
            'return_change': enhanced_perf['total_return'] - baseline_perf['total_return'],
            'profit_factor_change': enhanced_perf['profit_factor'] - baseline_perf['profit_factor'],
            'avg_cost_reduction': enhanced_trades['cost_reduction_pct'].mean(),
            'trades_with_cost_savings': (enhanced_trades['cost_savings'] > 0).sum(),
            'total_cost_savings': enhanced_trades['cost_savings'].sum()
        }
        
        return {
            'baseline_performance': baseline_perf,
            'enhanced_performance': enhanced_perf,
            'improvement_metrics': improvement_metrics
        }
    
    def _calculate_performance_metrics(self, trades_df: pd.DataFrame, 
                                     pnl_column: str = 'final_pnl') -> Dict:
        """Calculate performance metrics for trades"""
        
        if trades_df.empty:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'total_return': 0.0,
                'profit_factor': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0
            }
        
        total_trades = len(trades_df)
        winning_trades = trades_df[trades_df[pnl_column] > 0]
        losing_trades = trades_df[trades_df[pnl_column] <= 0]
        
        win_rate = (len(winning_trades) / total_trades) * 100 if total_trades > 0 else 0.0
        total_pnl = trades_df[pnl_column].sum()
        total_return = (total_pnl / 100000.0) * 100  # Starting capital = 100k
        
        # Profit factor
        gross_profit = winning_trades[pnl_column].sum() if len(winning_trades) > 0 else 0.0
        gross_loss = abs(losing_trades[pnl_column].sum()) if len(losing_trades) > 0 else 0.0
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0.0
        
        avg_win = winning_trades[pnl_column].mean() if len(winning_trades) > 0 else 0.0
        avg_loss = losing_trades[pnl_column].mean() if len(losing_trades) > 0 else 0.0
        
        return {
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'win_rate': win_rate,
            'total_return': total_return,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'total_pnl': total_pnl
        }
    
    def _generate_optimization_summary(self, cost_analysis: Dict, 
                                     performance_analysis: Dict) -> Dict:
        """Generate comprehensive optimization summary"""
        
        baseline_perf = performance_analysis['baseline_performance']
        enhanced_perf = performance_analysis.get('enhanced_performance', {})
        improvements = performance_analysis.get('improvement_metrics', {})
        
        # Determine success criteria
        cost_target_met = cost_analysis['cost_reduction_achieved'] >= self.COST_REDUCTION_TARGET
        win_rate_maintained = improvements.get('win_rate_change', 0) >= -1.0  # Allow 1% tolerance
        
        success_score = 0
        if cost_target_met:
            success_score += 1
        if win_rate_maintained:
            success_score += 1
        if improvements.get('return_change', 0) > 0:
            success_score += 1
        
        return {
            'optimization_success': success_score >= 2,  # At least 2 of 3 criteria met
            'cost_target_achieved': cost_target_met,
            'win_rate_maintained': win_rate_maintained,
            'overall_improvement': improvements.get('return_change', 0) > 0,
            'success_score': f"{success_score}/3",
            'key_metrics': {
                'cost_reduction': f"{cost_analysis['cost_reduction_achieved']*100:.1f}%",
                'win_rate_change': f"{improvements.get('win_rate_change', 0):+.1f}%",
                'return_improvement': f"{improvements.get('return_change', 0):+.1f}%",
                'trades_optimized': f"{cost_analysis['trades_optimized']}/{cost_analysis['trades_analyzed']}"
            },
            'recommendations': self._generate_recommendations(cost_analysis, improvements)
        }
    
    def _generate_recommendations(self, cost_analysis: Dict, improvements: Dict) -> List[str]:
        """Generate actionable recommendations"""
        
        recommendations = []
        
        cost_reduction = cost_analysis['cost_reduction_achieved']
        win_rate_change = improvements.get('win_rate_change', 0)
        
        if cost_reduction >= self.COST_REDUCTION_TARGET:
            recommendations.append("✅ Cost reduction target achieved - implement sophisticated strike selection")
        else:
            recommendations.append("⚠️ Cost reduction below target - consider more aggressive optimization")
        
        if win_rate_change >= 0:
            recommendations.append("✅ Win rate maintained or improved - strategy enhancement successful")
        elif win_rate_change > -2.0:
            recommendations.append("⚠️ Minor win rate decline acceptable given cost benefits")
        else:
            recommendations.append("❌ Significant win rate decline - review strategy selection criteria")
        
        if improvements.get('return_change', 0) > 0:
            recommendations.append("✅ Overall returns improved - full implementation recommended")
        
        # Strategy-specific recommendations
        if cost_analysis['trades_optimized'] / cost_analysis['trades_analyzed'] < 0.8:
            recommendations.append("📊 Consider expanding option liquidity criteria for more optimizations")
        
        return recommendations


def main():
    """Run enhanced strike selection analysis"""
    
    print("🚀 ENHANCED STRIKE SELECTION STRATEGY ANALYSIS")
    print("=" * 70)
    print("🎯 Goal: Maintain 50.0% win rate with 10-20% cost reduction")
    print("📊 Methods: Directional, Butterfly, and Neutral strike optimization")
    print("=" * 70)
    
    # Load baseline high-performing trades
    try:
        baseline_trades = pd.read_csv('reports/technical_enhanced_trades.csv')
        print(f"✅ Loaded {len(baseline_trades)} high-performing trades (50.0% win rate)")
    except FileNotFoundError:
        print("❌ No technical enhanced trades found. Run enhanced_technical_strategy.py first.")
        return
    
    # Load options data
    try:
        options_data = pd.read_csv('data/SPX_COMPLETE_COMBINED.csv')
        options_data['date'] = pd.to_datetime(options_data['date'])
        options_data['expiration'] = pd.to_datetime(options_data['expiration'])
        print(f"✅ Loaded {len(options_data):,} options records")
    except FileNotFoundError:
        print("❌ No combined options data found. Run cluster_strategy_refactored.py first.")
        return
    
    # Initialize enhanced strategy
    strategy = EnhancedStrikeSelectionStrategy()
    
    # Run comprehensive analysis
    results = strategy.analyze_existing_trades(baseline_trades, options_data)
    
    # Generate detailed report
    generate_strike_optimization_report(results)
    
    print(f"\n✅ Enhanced strike selection analysis completed!")
    
    return results


def generate_strike_optimization_report(results: Dict):
    """Generate comprehensive strike optimization report"""
    
    print(f"\n📋 SOPHISTICATED STRIKE SELECTION REPORT")
    print("=" * 70)
    
    cost_analysis = results['cost_savings_analysis']
    performance_analysis = results['performance_analysis']
    optimization_summary = results['optimization_summary']
    
    # Cost Analysis
    print(f"💰 COST OPTIMIZATION ANALYSIS:")
    print(f"   Trades Analyzed: {cost_analysis['trades_analyzed']}")
    print(f"   Trades Optimized: {cost_analysis['trades_optimized']}")
    print(f"   Original Total Cost: ${cost_analysis['original_total_cost']:,.0f}")
    print(f"   Optimized Total Cost: ${cost_analysis['optimized_total_cost']:,.0f}")
    print(f"   Cost Reduction Achieved: {cost_analysis['cost_reduction_achieved']*100:.1f}%")
    
    # Performance Comparison
    baseline = performance_analysis['baseline_performance']
    enhanced = performance_analysis.get('enhanced_performance', {})
    
    if enhanced:
        print(f"\n📈 PERFORMANCE COMPARISON:")
        print(f"                    Baseline    Enhanced    Change")
        print(f"Win Rate:           {baseline['win_rate']:8.1f}%   {enhanced['win_rate']:8.1f}%   {enhanced['win_rate']-baseline['win_rate']:+6.1f}%")
        print(f"Total Return:       {baseline['total_return']:8.1f}%   {enhanced['total_return']:8.1f}%   {enhanced['total_return']-baseline['total_return']:+6.1f}%")
        print(f"Profit Factor:      {baseline['profit_factor']:8.2f}    {enhanced['profit_factor']:8.2f}    {enhanced['profit_factor']-baseline['profit_factor']:+6.2f}")
    
    # Optimization Summary
    print(f"\n🎯 OPTIMIZATION SUMMARY:")
    print(f"   Success Score: {optimization_summary['success_score']}")
    print(f"   Cost Target Achieved: {'✅' if optimization_summary['cost_target_achieved'] else '❌'}")
    print(f"   Win Rate Maintained: {'✅' if optimization_summary['win_rate_maintained'] else '❌'}")
    print(f"   Overall Improvement: {'✅' if optimization_summary['overall_improvement'] else '❌'}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    for rec in optimization_summary['recommendations']:
        print(f"   {rec}")
    
    # Save results
    if not results['enhanced_trades'].empty:
        results['enhanced_trades'].to_csv('reports/sophisticated_strike_trades.csv', index=False)
        print(f"\n💾 Enhanced trades saved to: reports/sophisticated_strike_trades.csv")


if __name__ == "__main__":
    results = main()
